<template>
	<view class="main">


		<view class="tech-dots">
			<view class="tech-dot"></view>
			<view class="tech-dot"></view>
			<view class="tech-dot"></view>
			<view class="tech-dot"></view>
			<view class="tech-dot"></view>
		</view>
		<view class="floating-particles">
			<view v-for="n in 15" :key="n" class="particle"></view>
		</view>
		<view
			:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
			class="iconDizhssi">
			<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
			<view class="font-size_30rpx" @click="navig()">
				企业定位
			</view>
		</view>
		<view class="center">
			<view class="s-tips">
				企业定位, 开启企业增长之旅
			</view>
			<textarea type="text" v-model="value" maxlength="300" class="r-input margin-bottom_40rpx"
				placeholder="请一句话描述您的业务，如:我是手机硬件评测的自媒体人" placeholder-class="placeholder"></textarea>
			<view class="m-but" @click="goPage">开启定位</view>
		</view>
	</view>
</template>

<script>
	export default {

		data() {
			return {
				imgUrl: this.$imgUrl,
				value: '',
				heightSystemss: '',
				statusBarHeightss: '',
			}
		},
		onLoad() {
			this.getSystemInfo();
		},
		methods: {
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},
			goPage() {
				if (!this.value) {
					this.$sun.toast('请输入您的业务描述', 'none');
					return
				}
				uni.setStorageSync('prdjectData', this.value)
				uni.navigateTo({
					url: '/subPackages/subPackageA/AIProject'
				})
			}
		},

	}
</script>

<style lang="scss">
	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		display: flex;
		align-items: center;
	}

	.m-but {
		width: 170rpx;
		border-radius: 100rpx;
		text-align: center;
		padding: 22rpx 0;
		color: #FFF;
		font-weight: 600;
		box-shadow: 0px -1px 11px 4px rgba(127, 27, 255, 0.2);
		background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
	}

	.center {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		z-index: 2;
	}

	.s-tips {
		position: absolute;
		top: -160rpx;
		width: 100%;
		text-align: center;
		background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
		text-fill-color: transparent;
		font-size: 40rpx;
		font-weight: bold;
	}

	.placeholder {
		color: #999999;
	}

	.r-input {
		width: 670rpx;
		padding: 20rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.main {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		background-image: radial-gradient(circle at 10% 20%, rgba(50, 30, 100, 0.2) 0%, rgba(8, 14, 30, 0.5) 90%);
		overflow: hidden;
	}

	/* 添加科技感背景元素 */
	.main::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background-image:
			linear-gradient(rgba(20, 40, 100, 0.08) 1px, transparent 1px),
			linear-gradient(90deg, rgba(20, 40, 100, 0.08) 1px, transparent 1px);
		background-size: 30px 30px;
		z-index: 1;
		animation: gridMove 30s linear infinite;
		transform: rotate(15deg);
	}

	.main::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background:
			radial-gradient(circle at 20% 10%, rgba(127, 27, 255, 0.15) 0%, transparent 30%),
			radial-gradient(circle at 80% 90%, rgba(65, 88, 208, 0.15) 0%, transparent 30%),
			radial-gradient(circle at 50% 50%, rgba(8, 14, 30, 0.1) 0%, rgba(8, 14, 30, 0.5) 100%);
		z-index: 1;
	}

	@keyframes gridMove {
		0% {
			transform: translateY(0) rotate(15deg);
		}

		100% {
			transform: translateY(-100px) rotate(15deg);
		}
	}

	/* 添加科技感光点元素 */
	.tech-dots {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		pointer-events: none;
		z-index: 1;
	}

	.tech-dot {
		position: absolute;
		width: 6px;
		height: 6px;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.4);
		box-shadow: 0 0 15px 4px rgba(127, 27, 255, 0.4);
		animation: pulse 2s infinite;
	}

	.tech-dot:nth-child(1) {
		top: 10%;
		left: 20%;
		animation-delay: 0s;
	}

	.tech-dot:nth-child(2) {
		top: 70%;
		left: 10%;
		animation-delay: 0.5s;
	}

	.tech-dot:nth-child(3) {
		top: 30%;
		left: 85%;
		animation-delay: 1s;
	}

	.tech-dot:nth-child(4) {
		top: 60%;
		left: 75%;
		animation-delay: 1.5s;
	}

	.tech-dot:nth-child(5) {
		top: 20%;
		left: 50%;
		animation-delay: 2s;
	}

	@keyframes pulse {
		0% {
			transform: scale(1);
			opacity: 0.2;
			box-shadow: 0 0 15px 2px rgba(127, 27, 255, 0.2);
		}

		50% {
			transform: scale(2);
			opacity: 0.8;
			box-shadow: 0 0 20px 5px rgba(127, 27, 255, 0.6);
		}

		100% {
			transform: scale(1);
			opacity: 0.2;
			box-shadow: 0 0 15px 2px rgba(127, 27, 255, 0.2);
		}
	}

	/* 飘动粒子动画 */
	.floating-particles {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		overflow: hidden;
		z-index: 1;
		pointer-events: none;
	}

	.particle {
		position: absolute;
		width: 2px;
		height: 2px;
		background: rgba(255, 255, 255, 0.5);
		border-radius: 50%;
		box-shadow: 0 0 4px 1px rgba(127, 27, 255, 0.3);
		animation: float 15s linear infinite;
	}

	.particle:nth-child(1) {
		top: 10%;
		left: 20%;
		animation-duration: 10s;
	}

	.particle:nth-child(2) {
		top: 20%;
		left: 80%;
		animation-duration: 12s;
	}

	.particle:nth-child(3) {
		top: 30%;
		left: 40%;
		animation-duration: 9s;
	}

	.particle:nth-child(4) {
		top: 40%;
		left: 60%;
		animation-duration: 14s;
	}

	.particle:nth-child(5) {
		top: 50%;
		left: 10%;
		animation-duration: 11s;
	}

	.particle:nth-child(6) {
		top: 60%;
		left: 30%;
		animation-duration: 13s;
	}

	.particle:nth-child(7) {
		top: 70%;
		left: 70%;
		animation-duration: 8s;
	}

	.particle:nth-child(8) {
		top: 80%;
		left: 50%;
		animation-duration: 15s;
	}

	.particle:nth-child(9) {
		top: 90%;
		left: 90%;
		animation-duration: 10s;
	}

	.particle:nth-child(10) {
		top: 15%;
		left: 45%;
		animation-duration: 11s;
	}

	.particle:nth-child(11) {
		top: 25%;
		left: 65%;
		animation-duration: 9s;
	}

	.particle:nth-child(12) {
		top: 35%;
		left: 15%;
		animation-duration: 12s;
	}

	.particle:nth-child(13) {
		top: 45%;
		left: 85%;
		animation-duration: 10s;
	}

	.particle:nth-child(14) {
		top: 65%;
		left: 25%;
		animation-duration: 14s;
	}

	.particle:nth-child(15) {
		top: 85%;
		left: 55%;
		animation-duration: 13s;
	}

	@keyframes float {
		0% {
			transform: translateY(0) translateX(0) scale(1);
			opacity: 0;
		}

		10% {
			opacity: 0.8;
			transform: translateY(-10px) translateX(10px) scale(1.2);
		}

		50% {
			transform: translateY(-50px) translateX(-20px) scale(0.8);
			opacity: 0.4;
		}

		90% {
			transform: translateY(-100px) translateX(30px) scale(1.5);
			opacity: 0.1;
		}

		100% {
			transform: translateY(-120px) translateX(20px) scale(0);
			opacity: 0;
		}
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
</style>