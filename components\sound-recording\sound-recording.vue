<template>
	<view class="recorder" :style="[{ 'background-color': backgroundColor }]">
		<text class="now-date">{{ reDate }}</text>
		<view class="v-tips">
			<block v-if="finish == 1">
				点击开始录制语音,建议录制<span>15~{{maximum}}秒</span>内
			</block>
			<block v-if="finish == 5">
				点击完成录音,建议录制<span>15~{{maximum}}秒</span>内
			</block>
			<block v-if="finish == 2 || finish == 4">
				点击播放录音,点击完成保存录音
			</block>
			<block v-if="finish == 3">
				点击暂停播放录音,点击完成保存录音
			</block>
		</view>
		<!-- <view class="recorder-box display-a" v-if="finish == 1" @click="handle" @longpress="onStartRecoder"
			@touchend="onEndRecoder"> -->
		<view class="recorder-box display-a" v-if="finish == 1 || finish == 5">
			<view class="display-a-jc s-frame margin-right_30rpx">
				<image class="img-108" :src="imgUrl+'108.png'"></image>
				<view class="color_959595">取消</view>
			</view>
			<view @click="handle">
				<u-circle-progress :bgColor="bgColor" :colorType="colorType" :activeColor="theme" :duration="0" :percent="calcProgress">
					<view class="u-progress-content">
						<image :src="imgUrl+'sound-recording/voice.png'" class="img-v"></image>
					</view>
				</u-circle-progress>
			</view>
			<view class="display-a-jc s-frame margin-left_30rpx">
				<image class="img-108" :src="imgUrl+'110.png'"></image>
				<view class="color_959595">完成</view>
			</view>
		</view>

		<view class="recorder-box display-a" v-else>
			<view class="display-a-jc s-frame margin-right_30rpx" @click="cancel">
				<image class="img-108" :src="imgUrl+'109.png'"></image>
				<view class="color_FFFFFF font-size_24rpx">取消</view>
			</view>
			<view @click="playVoice">
				<u-circle-progress :bgColor="bgColor" :colorType="colorType" :inactiveColor="inactiveColor" :activeColor="theme" :duration="0"
					:percent="playProgress">
					<view class="u-progress-content">
						<image
							:src="finish == 3 ? imgUrl+'sound-recording/pause.png' : imgUrl+'sound-recording/play.png' "
							class="img-v"></image>
					</view>
				</u-circle-progress>
			</view>
			<view class="display-a-jc s-frame margin-left_30rpx" @click="confirm">
				<image class="img-108" :src="imgUrl+'111.png'"></image>
				<view class="color_FFFFFF font-size_24rpx">完成</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uCircleProgress from '../u-circle-progress/u-circle-progress.vue'
	const recorderManager = uni.getRecorderManager();
	const innerAudioContext = uni.createInnerAudioContext();
	export default {
		components: {
			uCircleProgress
		},
		props: {
			width: {
				type: String,
				default: '160rpx'
			},
			height: {
				type: String,
				default: '160rpx'
			},
			showTop: {
				type: Boolean,
				default: true
			},
			autoConfirm: {
				type: Boolean,
				default: false
			},
			maximum: {
				type: [Number, String],
				default: 60
			},
			duration: {
				type: Number,
				default: 20
			},
			colorType: {
				type: String,
				default: '1'
			},
			backgroundColor: {
				type: String,
				default: '#111317'
			},
			inactiveColor: {
				type: String,
				default: '#111317'
			},
			bgColor: {
				type: String,
				default: '#111317'
			},
			theme: {
				type: String,
				default: '#525252'
			},
			confirmText: {
				type: String,
				default: '完成'
			}
		},
		data() {
			return {

				reDate: '00:00',
				sec: 0,
				min: 0,

				finish: 1, //1未录制 5开始录制 2录制结束 3开始播放 4暂停

				voicePath: '',

				playProgress: 100,
				timer: null,

				imgUrl: this.$imgUrl,
				isUserStop: false,
			};
		},
		created() {
			// 监听
			this.onMonitorEvents()
		},
		computed: {
			// 录制时间计算
			calcProgress() {
				return (this.sec + (this.min * 60)) / this.maximum * 100
			}
		},
		methods: {

			// 完成事件
			confirm() {
				if (!innerAudioContext.paused) {
					innerAudioContext.stop();
					this.finish = 4;
				}
				// console.log("路径====>", this.voicePath);
				this.$emit('confirm', this.voicePath);
				this.reset();
				if (this.autoConfirm && !this.isUserStop) {
					this.onStartRecoder();
				}else{
					this.$emit('stopAll');
				}
			},

			// 取消事件
			cancel() {
				if (!innerAudioContext.paused) {
					innerAudioContext.stop();
					this.finish = 4;
				}
				this.$emit('cancel')
				this.reset();
			},

			// 点击事件
			async handle() {

				this.$emit('click')

				if (this.finish == 1) {
					let res = await this.audioAuthorize()
					// console.log("----->", res);
					if (res) {
						if(this.autoConfirm) {
							this.isUserStop = false;
						}
						this.onStartRecoder()
					}
				} else {
					this.onEndRecoder()
				}

			},
			// 重新录制
			reset() {
				this.voicePath = ''
				this.min = 0
				this.sec = 0
				this.reDate = '00:00'
				this.playProgress = 100
				this.finish = 1;
				this.$emit('reset')
			},

			// 播放暂停录音
			playVoice() {
				innerAudioContext.src = this.voicePath;
				if (this.finish == 2 || this.finish == 4) {
					innerAudioContext.play();
					this.finish = 3;
					// console.log("开始播放---->11111",this.finish);
				}else {
					innerAudioContext.pause();
					this.finish = 4;
					// console.log("暂停播放---->11111",this.finish);
				}
				this.$forceUpdate();
				this.$emit('playVoice', innerAudioContext.paused);
			},

			// 录制结束
			onEndRecoder() {
				if (this.autoConfirm) {
					this.isUserStop = true;
				}
				recorderManager.stop();
				this.finish = 2;
				
				// console.log("结束录制", this.finish);
			},

			// 开始录制
			onStartRecoder() {
				recorderManager.start({
					format: 'mp3',
					duration: this.maximum * 1000,
					sampleRate: 44100,
					encodeBitRate: 128000
				})
				this.finish = 5;
				// console.log("开始录制", this.finish);

			},

			// 监听
			onMonitorEvents() {
				// 录制开始
				recorderManager.onStart(() => {
					// console.log("开始录制--->", this.finish);
					// uni.showLoading({
					// 	title: '录制中...'
					// })
					this.startDate()
					this.$emit('start')
				})
				// 录制结束
				recorderManager.onStop(({
					tempFilePath
				}) => {
					this.voicePath = tempFilePath
					clearInterval(this.timer)
					// uni.hideLoading()
					this.$emit('end')
					if (this.autoConfirm && !this.isUserStop) {
						this.confirm();
					}
					// console.log("--->录制结束", this.voicePath,this.finish);
				})
				// 播放进度
				innerAudioContext.onTimeUpdate(() => {
					
					// console.log("播放进度");
					
					let totalDate = innerAudioContext.duration
					let nowTime = innerAudioContext.currentTime
					let surplus = totalDate - nowTime
					this.playProgress = surplus / totalDate * 100

					let _min = Math.floor(surplus / 60)
					if (_min < 10) _min = '0' + _min;
					let _sec = Math.floor(surplus % 60)
					if (_sec < 10) _sec = '0' + _sec;
					this.reDate = _min + ':' + _sec
				})
				// 播放暂停
				innerAudioContext.onPause(() => {
					this.resetDate()
					this.playProgress = 100
					this.$emit('stop')
					// console.log("---1111>暂停播放",this.finish);
				})
				// 播放停止
				innerAudioContext.onStop(() => {
					this.resetDate()
					this.playProgress = 100
					this.finish = 4;
					// console.log("---1111>播放停止",this.finish);
					this.$emit('stop')
				})
				//音频自然播放结束
				innerAudioContext.onEnded(() => {
					this.resetDate()
					this.playProgress = 100
					this.finish = 4;
					// console.log("---1111>播放结束",this.finish);
					this.$emit('ended')
				})
			},
			// 录音计时
			startDate() {
				clearInterval(this.timer)
				this.sec = 0
				this.min = 0
				this.timer = setInterval(() => {
					this.sec += this.duration / 1000
					if (this.sec >= 60) {
						this.min++
						this.sec = 0
					}
					this.resetDate()
				}, this.duration)
			},
			// 播放时间
			resetDate() {
				let _s = this.sec < 10 ? '0' + parseInt(this.sec) : parseInt(this.sec)
				let _m = this.min < 10 ? '0' + this.min : this.min
				this.reDate = _m + ':' + _s
			},


			async audioAuthorize() {
				const self = this
				let res = await this.getSetting()
				let auth = res.authSetting['scope.record']
				// 拒绝授权
				if (auth === false) {
					uni.showModal({
						content: '您已拒绝录音授权，是否手动开启权限？',
						async success(res) {
							if (res.confirm) {
								// console.log('用户点击确定');
								await self.openSetting()
							} else if (res.cancel) {
								// console.log('用户点击取消');
								uni.navigateBack()
							}
						}
					})
					return false
				}
				// 或者未进行过授权
				if (!auth) {
					let res = await this.getAuthorize('scope.record')
					return res.errMsg === 'authorize:ok'
				}
				return true
			},
			// 获取授权设置
			getSetting() {
				return new Promise((resolve, reject) => {
					uni.getSetting({
						success(res) {
							// console.log(`获取用户授权结果成功`, res);
							resolve(res)
						}
					})
				})
			},
			// 调起客户端小程序设置界面
			openSetting() {
				return new Promise((resolve, reject) => {
					uni.openSetting({
						success(res) {
							// console.log(`调起客户端小程序设置界面成功`, res);
							resolve(res)
						},
						fail(e) {
							// console.log(`调起客户端小程序设置界面失败`, e);
							resolve(e)
						}
					})
				})
			},
			// 获取授权
			getAuthorize(scope) {
				return new Promise((resolve, reject) => {
					uni.authorize({
						scope: scope,
						success(res) {
							// console.log(`获取${scope}权限成功`, res);
							resolve(res)
						},
						fail(e) {
							// console.log(`获取${scope}权限失败`, e);
							resolve(e)
						}
					})
				})
			},
		}
	}
</script>

<style lang="scss">
	.s-frame {
		width: 130rpx;
		padding: 10rpx 0;
		border-radius: 100rpx;
		background-color: #636363;
	}

	.color_959595 {
		font-size: 24rpx;
		color: #959595;
	}

	.img-108 {
		width: 28rpx;
		height: 28rpx;
		margin-right: 4rpx;
		margin-top: 2rpx;
	}

	.v-tips {
		font-size: 26rpx;
		margin-bottom: 30rpx;
		color: #FFF;

		span {
			color: #40CAFE;
			margin: 0 4rpx;
		}
	}

	.u-progress-content {
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(136, 118, 230), rgb(176, 136, 248) 100%);
	}

	.img-v {
		width: 160rpx;
		height: 160rpx;
	}

	.recorder {
		position: relative;
		display: flex;
		align-items: center;
		flex-direction: column;
		font-size: 24rpx;
		width: 710rpx;
		z-index: 9;
		.re-top {
			display: flex;
			justify-content: space-between;
			padding: 10rpx 20rpx;
			width: 100%;
			font-size: 28rpx;
			box-sizing: border-box;
		}

		.title {
			font-size: 36rpx;
			color: #333;
			padding: 20rpx 0 30rpx;
		}

		.recorder-box {
			position: relative;
		}

		.now-date {
			// text-align: center;
			color: #40CAFE;
			font-size: 38rpx;
			font-weight: bold;
			margin-bottom: 24rpx;
		}
	}
</style>