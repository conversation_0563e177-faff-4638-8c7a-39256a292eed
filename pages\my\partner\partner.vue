<template>
	<view>
		
		<view class="bg" :style="{'background-image': 'url('+partnerSet.apply_bg+')'}">
			<view :style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view @click="navig()">
					
				</view>
			</view>
			
			<view style="height: 1006rpx;"></view>
			
			<view class="p-frame">
				<input type="text" v-model="name" class="input-p margin-bottom_30rpx" placeholder="请输入联系人名称" placeholder-class="placeholder" />
				<input type="number" v-model="telphone" maxlength="11" class="input-p" placeholder="请输入联系人电话" placeholder-class="placeholder" />
			</view>
			
		</view>
		
		<view style="height: 150rpx;"></view>
		
		<view class="but" @click="but()" :style="{'background': ''+partnerSet.apply_button+'','color': ''+partnerSet.apply_text+''}">
			申请成为合伙人
			<block v-if="partnerSet.is_money == 1 && partnerSet.money > 0">
				<span>￥{{partnerSet.money}}</span>
			</block>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				partnerSet: {},
				
				heightSystemss: '',
				statusBarHeightss: '',
				
				name: '', //姓名
				telphone: '', //电话
				
				imgUrl: this.$imgUrl,
				
				isWhether: true, //防止重复点击
				
				templateObj: {},
				
			}
		},
		
		onLoad() {
			this.getTemplate();
			this.getSystemInfo();
			this.getPartnerSet();
		},
		
		onShow() {
			
		},
		
		methods: {
			
			//模板设置
			async getTemplate() {
				const result = await this.$http.post({
					url: this.$api.template,
				});
				if (result.errno == 0) {
					this.templateObj = result.data;
				}
			},
			
			async but() {
				
				if (!this.name) {
					this.$sun.toast("请输入联系人姓名!",'none');
					return;
				}
				if (!this.telphone) {
					this.$sun.toast("请输入联系人电话!",'none');
					return;
				}
				
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				
				uni.getSetting({
					withSubscriptions: true,
					success: (res) => {
						console.log(res.subscriptionsSetting);
						if (res.subscriptionsSetting.mainSwitch == false) {
							this.save();
						} else {
							// 获取下发权限
							uni.requestSubscribeMessage({
								tmplIds: [this.templateObj.apply_result_template], //此处写在后台获取的模板ID，可以写多个模板ID，看自己的需求
								success: async (data) => {
									if (data[this.templateObj.apply_result_template] == 'accept') { //accept--用户同意 reject--用户拒绝 ban--微信后台封禁,可不管
										this.save();
									} else {
										uni.showModal({
											title: '温馨提示',
											content: '您已拒绝授权，将无法在微信中收到通知！',
											showCancel: false,
											success: res => {
												if (res.confirm) {
													// 这里可以写自己的逻辑
													this.save();
													console.log('拒绝授权', data);
												}
											}
										})
									}
								},
								fail: (err) => {
									this.save();
									console.log('失败', err);
								},
								complete: (result) => {
				
									console.log('完成', result);
								}
							});
						}
					}
				});
				
			},
			
			async save() {
				const result = await this.$http.post({
					url: this.$api.partnerAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: this.name,
						telphone: this.telphone
					}
				});
				if (result.errno == 0) {
					if (this.partnerSet.is_money == 1 && this.partnerSet.money > 0) {
						this.wxPay(result.data)
					}else {
						this.$sun.toast("申请成功,请等待审核",'none');
						setTimeout(() => {
							this.isWhether = true;
							uni.navigateBack();
						}, 2000);
					}
				} else {
					this.$sun.toast(result.message,'none');
					this.isWhether = true;
				}
			},
			
			/*  微信支付  */
			async wxPay(log_no) {
				const result = await this.$http.post({
					url: this.$api.pay,
					data: {
						openid: uni.getStorageSync('openid'),
						price: this.partnerSet.money,
						log_no: log_no,
						name: "合伙人申请"
					}
				});
				if (result.errno == 0) {
					uni.requestPayment({
						provider: 'wxpay',
						timeStamp: result.data.timeStamp,
						nonceStr: result.data.nonceStr,
						package: result.data.package,
						signType: result.data.signType,
						paySign: result.data.paySign,
						success: async (res) => {
							this.$sun.toast("支付成功,请等待审核",'none');
							setTimeout(() => {
								this.isWhether = true;
								uni.navigateBack();
							}, 2000);
						},
						fail: (err) => {
							this.isWhether = true;
							this.$sun.toast("取消支付",'error');
						}
					});
				}else {
					this.isWhether = true;
					if (result.errno == -1){
						this.$sun.toast(result.message,'none');
						return;
					}
					if (result.return_code == 'FAIL'){
						uni.showModal({
							title: '支付配置错误',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									
								}
							}
						})
					}else {
						uni.showModal({
							title: '提示',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									
								}
							}
						})
					}
				}
			},
			
			//合伙人设置
			async getPartnerSet() {
				const result = await this.$http.post({
					url: this.$api.partnerSet
				});
				if (result.errno == 0) {
					this.partnerSet = result.data;
				}
			},
			
			navig() {
				let pages = getCurrentPages();  //获取所有页面栈实例列表
				
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}else {
					uni.navigateBack();
				}
			},
			
			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},
			
		}
	}
</script>

<style lang="scss">
	
	.input-p {
		width: 560rpx;
		background-color: #FFF;
		border-radius: 10rpx;
		padding: 14rpx 20rpx;
	}
	
	.p-frame {
		// border: 1px solid #ff0000;
		width: 600rpx;
		margin-left: 75rpx;
	}
	
	.but {
		width: 670rpx;
		border-radius: 10rpx;
		padding: 20rpx 0;
		text-align: center;
		position: fixed;
		bottom: 50rpx;
		left: 40rpx;
		background: linear-gradient(90.00deg, rgb(253, 226, 170),rgb(249, 189, 118) 55.978%,rgb(248, 194, 125) 100%);
		color: #442D25;
		font-size: 32rpx;
	}
	
	.bg {
		width: 750rpx;
		height: 3996rpx;
		background-repeat: no-repeat;
		background-size: contain;
	}
	
	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}
	
	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		// font-size: 30rpx;
		// font-weight: bold;
		display: flex;
		align-items: center;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000000;
	}
	
</style>
