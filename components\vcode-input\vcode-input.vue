<template>
	<view class="vcode-input-body">
		<text class="vcode-input-item" 
		:class="isBorderLine?'vcode-input-line':'vcode-input-border'"
		v-for="(v,index) in sum" 
		:key="index"
		@tap.stop="setFocus"
		:style="{
			borderColor:text.length===index&&focus?borderActiveColor:(text.length>index?borderValueColor:borderColor),
			color:text.length>index?textColor:textColor
		}"
		>{{text[index]}}</text>
		
		<view class="hidden-input">
			<input
			id="vcodeInput"
			ref="vcodeInput"
			type="number" 
			:show-confirm-bar="false"
			auto-blur
			:focus="focus"
			:maxlength="sum"
			v-model="value"
			@blur="setBlur"
			@focus="setFocus"
			:password="isPassword"
			placeholder="验证码"/>
		</view>
	</view>
</template>

<script>
	export default {
		name:'VcodeInput',
		props: {
			autofocus:{
				type: Boolean,
				default: false
			},
			sum:{
				type: Number,
				default: 4
			},
			isBorderLine:{
				type:Boolean,
				default:false
			},
			borderColor:{
				type:String,
				default:'#DADADA'
			},
			borderValueColor:{
				type:String,
				default:'#424456'
			},
			textColor:{
				type:String,
				default:'#000000'
			},
			borderActiveColor:{
				type:String,
				default:'#DADADA'
			},
			isAutoComplete:{
				type: Boolean,
				default: true
			},
			isPassword:{
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				focus:false,
				text:[],
				value:''
			};
		},
		watch:{
			value(value,oldVal){
				if(this.isAutoComplete){
					if(value.length>=this.sum){
						this.focus=false;
						this.$emit('vcodeInput', value);
					}
				}else{
					this.$emit('vcodeInput', value);
				}
				if(this.isPassword){
					let val='';
					for (let i = 0; i < value.length; i++) {
						val+='●';
					}
					this.text=val;
				}else{
					this.text=value.split("");
				}
			}
		},
		mounted() {
			this.$nextTick(() => {
				this.initInput()
			})
		},
		methods:{
			initInput(){
				if(this.autofocus)
				this.focus=true;
				// #ifdef H5
				this.$refs.vcodeInput.$refs.input.setAttribute('type','number');
				this.$refs.vcodeInput.$refs.input.setAttribute('pattern','[0-9]*')
				// #endif
			},
			setBlur(){
				uni.hideKeyboard();
				this.$nextTick(() => {
					this.focus=false;
				})
			},
			setFocus(){
				this.focus=true;
			},
			clearValue(){
				this.setBlur();
				this.value='';
				this.text=[];
				this.$forceUpdate();
			}
		}
	}
</script>

<style lang="scss" scoped>
.vcode-input-body{
	margin-left: -36rpx;
	margin-right: -36rpx;
	position: relative;
	overflow: hidden;
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	// justify-content: center;
	align-items: center;
	padding: 0 20rpx;
}
.vcode-input-item{
	width: 76rpx;
	height: 76rpx;
	margin-left: 12rpx;
	margin-right: 12rpx;
	line-height: 76rpx;
	text-align: center;
	font-weight: 500;
}
.vcode-input-border{
	border-style: solid;
	border-width: 2rpx;
	border-color: $uni-border-color;
	border-radius: 4rpx;
}
.vcode-input-line{
	border-bottom-style: solid;
	border-bottom-width: 2rpx;
	border-color: $uni-border-color;
}
.hidden-input{
	width: 1px;
	height: 1px;
	position: absolute;
	left: -1px;
	top: -1px;
	overflow: hidden;
}
</style>
