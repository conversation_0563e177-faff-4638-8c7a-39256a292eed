<template>
	<view>
		<!-- <scroll-view style="height:250px;" :scroll-y="true"> -->
			<view class="charts-box">
				<!-- 开启滚动仅需   :ontouch="true"  -->
				<qiun-data-charts :type="type" :chartData="chartData" @getIndex="getIndex" :opts="opts" :ontouch="ontouch" :canvas2d="true" :pageScrollTop="pageScrollTop" />
			</view>
		<!-- </scroll-view> -->
	</view>
</template>

<script>
	export default {
		props: {
			chartData: {
				type: Object,
				default () {
					return {}
				}
			},
			optse: {
				type: Object,
				default () {
					return {}
				}
			},
			// 是否开启滑动
			ontouch: {
				type: <PERSON>olean,
				default () {
					return false
				}
			},

			xAxis: {
				type: Object,
				default () {
					return {}
				}
			},
			
			yAxis: {
				type: Object,
				default () {
					return {}
				}
			},
			
			//进度条
			arcbar: {
				type: Object,
				default () {
					return {}
				}
			},
			
			// 柱状图
			column: {
				type: Object,
				default () {
					return {}
				}
			},
			// 折线图
			area: {
				type: Object,
				default () {
					return {}
				}
			},

			// 折线图
			line: {
				type: Object,
				default () {
					return {}
				}
			},
			// 类型
			type: {
				type: String,
				default () {
					return ""
				}
			},
			// 线的颜色
			color: {
				type: Array,
				default () {
					return ['1890FF']
				}
			},
			// 判断用什么形式
			isExse: {
				type: Boolean,
				default () {
					return false
				}
			},
		},
		data() {
			return {
				opts: {
					series: [],
					color: this.color,
					touchMoveLimit: 24,
					enableScroll: true,
					legend: {},
					xAxis: this.xAxis,
					yAxis: this.yAxis,
					extra: {
						column: this.column,
						area: this.area,
						line: this.line,
						arcbar: this.arcbar,
					},
				},
				pageScrollTop: 0
			};
		},
		onReady() {

		},
		onPageScroll(e) {
			this.pageScrollTop = e.scrollTop
		},
		methods: {
			getIndex(e) {
				this.$emit('change', e);
			},
		}
	};
</script>

<style scoped>
	/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
	.charts-box {
		width: 662rpx;
		height: 500rpx;
		background-color: #FFFFFF;
		/* position: relative; */
		/* left: 0;
		top: 0; */
		overflow: hidden;
	}
	
</style>
