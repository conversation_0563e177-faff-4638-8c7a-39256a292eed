<template>
	<view>

		<!-- <view class="h_20rpx"></view> -->

		<block v-if="param.base_video">
			<video class="video-url" :class="{full: type === 2}" :src="param.base_video" :autoplay="true"></video>
		</block>
		<block v-else>
			<image class="video-url" mode="widthFix" :src="param.decode_img"></image>
		</block>

		<view class="v-but" v-if="type === 1" @click="add()">添加形象</view>
		<view v-else-if="type === 2"></view>
		<view class="v-but" v-else @click="but()">视频合成</view>

		<view class="h_20rpx"></view>

	</view>
</template>

<script>
	export default {
		data() {
			return {

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				imgUrl: this.$imgUrl,

				isWhether: true, //判断重复点击

				param: {
					base_video: '', //形象视频
					decode_img: '', //形象图片
					id: '', //形象ID
					isSel: '', //1 2 3 4号线路
					current_status: '',
					new_current_status: '',
					composite_current_status: '',
					four_current_status: '',
				},

				type: null

			}
		},

		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)
			}

			this.param = JSON.parse(decodeURIComponent(options.param));
			if (this.type === 2) {
				this.$sun.title(this.param.name);
			} else {
				this.$sun.title('形象视频');
			}

		},

		onShow() {

		},

		methods: {

			but() {

				uni.navigateTo({
					url: '/pages/index/clip/clip?param=' + encodeURIComponent(JSON.stringify(this.param))
				})

			},
			async add() {
				// console.log('添加形象', this.param.id);
				const result = await this.$http.post({
					url: this.$api.usageAvatarList,
					data: {
						uid: uni.getStorageSync('uid'),
						avatar_id: this.param.id
					}
				});
				if (result.errno === 0) {
					this.$sun.toast('添加成功', "none")
				} else {
					this.$sun.toast(result.message, "none")
				}


			},

		}
	}
</script>

<style lang="scss">
	.full {
		width: 750rpx !important;
		height: 1333rpx !important;
		margin-left: 0 !important;
	}

	.img-167 {
		width: 40rpx;
		height: 40rpx;
		margin-right: 6rpx;
	}

	.v-frame2 {
		background: rgb(184, 117, 248);
		;
		border: 1px solid rgb(184, 117, 248);
	}

	.v-frame3 {
		border: 1px solid #979797;
	}

	.v-frame1 {
		border: 1px solid rgb(184, 117, 248);
	}

	.v-frame {
		width: 304rpx;
		border-radius: 10rpx;
		padding: 20rpx 0;
	}

	.img-166 {
		width: 40rpx;
		height: 40rpx;
		margin-right: 6rpx;
	}

	.v-but {
		width: 710rpx;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(188, 120, 248), rgb(135, 80, 242) 100%);
		padding: 20rpx 0;
		margin: 40rpx 20rpx 20rpx;
		color: #FFF;
		font-size: 32rpx;
		text-align: center;
	}

	.video-url {
		width: 648rpx;
		height: 1152rpx;
		margin-left: 51rpx;
	}

	page {
		border-top: none;
		background-color: #232323;
	}
</style>