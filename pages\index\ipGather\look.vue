<template>
	<view>

		<view class="bg" :style="{'background-image': 'url('+imgUrl+'405.png'+')'}">

			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_30rpx" @click="navig()">
					作品列表
				</view>
			</view>

			<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
				@up="upCallback" :up="upOption" :down="downOption">
				<block v-for="(item,index) in list" :key="index">
					<view class="list-public">
						<view class="look-name">作品{{index+1}}</view>
						<view class="display-a-js video-url" @click="getCopy(item.video_url)">
							<view class="font-overflow color_FFFFFF" style="width: 630rpx;">{{item.video_url}}</view>
							<image class="img-423" :src="imgUrl+'423.png'"></image>
						</view>
						<view class="save-text" @click="getDetail(item.content)" v-if="item.content">
							查看文案
						</view>
						<view class="save-text" @click="polling(item.id)" v-else>
							解析作品文案
							<!-- <span class="margin-left_16rpx">
								{{tallySetObj.douyin_homepage_video}}点/1次
							</span> -->
						</view>
					</view>
				</block>
			</mescroll-body>


		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {

				imgUrl: this.$imgUrl,

				heightSystemss: '',
				statusBarHeightss: '',
				windowHeight: '',

				id: '',

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],

				intervalId: '', //倒计时

				isWhether: true, //判断重复点击

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

			}
		},

		onLoad(options) {
			this.getSystemInfo();
			if (options.id) {
				this.id = options.id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}
		},

		onShow() {

		},

		onHide() {
			if (this.intervalId) {
				clearInterval(this.intervalId);
			}
		},

		methods: {

			//查看文案
			getDetail(msgText) {

				uni.setStorageSync("answer", msgText);

				uni.navigateTo({
					url: '/pages/index/ipGather/detail'
				})
			},

			//轮询调用检查
			polling(id) {

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				this.getVideoAnalysis(id);

				// this.intervalId = setInterval(() => {
				// 	this.getVideoAnalysis(id);
				// }, 3000);
			},

			//解析
			async getVideoAnalysis(id) {

				uni.showLoading({
					mask: true,
					title: '正在解析...'
				})

				const result = await this.$http.post({
					url: this.$api.videoAnalysis,
					data: {
						id: id
					},
					loading: ''
				});

				if (result.errno == -3) {
					setTimeout(() => {
						this.getVideoAnalysis(id);
					}, 6100);
				} else {
					if (result.errno == 0) {
						// clearInterval(this.intervalId);
						uni.hideLoading();
						this.getHomeVideoAudio(id);
					} else {
						uni.hideLoading();
						// clearInterval(this.intervalId);
						this.isWhether = true;
						if (result.errno == -2) {
							uni.showModal({
								content: result.message,
								cancelText: "取消",
								confirmText: "去开通",
								success: (res) => {
									if (res.confirm) {
										uni.navigateTo({
											url: '/pages/my/member'
										})
									} else if (res.cancel) {

									}
								}
							})
						} else {
							this.$sun.toast(result.message, 'none');
						}
					}
				}



			},

			// //解析
			// async getHomeVideoAudio(id) {

			// 	uni.showLoading({
			// 		mask: true,
			// 		title: '正在解析...'
			// 	})

			// 	const result = await this.$http.post({
			// 		url: this.$api.homeVideoAudio,
			// 		data: {
			// 			id: id
			// 		},
			// 		loading: ''
			// 	});

			// 	if (result.errno == -3) {

			// 		setTimeout(() => {
			// 			this.getHomeVideoAudio(id);
			// 		}, 6100);

			// 	}else {
			// 		if (result.errno == 0) {

			// 			uni.hideLoading();

			// 			this.getHomeVideoAudioCopy(id);

			// 		} else {
			// 			uni.hideLoading();
			// 			this.isWhether = true;
			// 			this.$sun.toast(result.message, 'none');
			// 		}
			// 	}

			// },

			//获取文案
			async getHomeVideoAudioCopy(id) {

				uni.showLoading({
					mask: true,
					title: '正在解析...'
				})

				const result = await this.$http.post({
					url: this.$api.getHomeVideoAudioCopywriting,
					data: {
						id: id
					},
					loading: ''
				});

				if (result.errno == -3) {
					setTimeout(() => {
						this.getHomeVideoAudioCopy(id);
					}, 6100);

				} else {
					if (result.errno == 0) {
						uni.hideLoading();
						this.$sun.toast("解析成功");
						setTimeout(() => {
							this.isWhether = true;
							this.$nextTick(() => {
								this.mescroll.resetUpScroll();
							});
						}, 2000);
					} else {
						uni.hideLoading();
						this.isWhether = true;
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//复制
			getCopy(url) {
				if (url) {
					uni.setClipboardData({
						data: url,
						success: () => {
							// 复制成功的回调
							this.$sun.toast('复制成功');
							// uni.showToast({
							// 	title: '复制成功',
							// 	icon: 'success',
							// 	duration: 4000
							// });
						},
						fail: (err) => {
							console.log("复制失败原因===>", err);
							// 复制失败的回调
							uni.showToast({
								title: '复制失败：' + err,
								icon: 'none'
							});
						}
					});
				}
			},

			async upCallback(scroll) {

				const result = await this.$http.post({
					url: this.$api.homevideoList,
					data: {
						uid: uni.getStorageSync('uid'),
						id: this.id,
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
						this.windowHeight = res.windowHeight * 2 - 204;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	.save-text {
		text-align: center;
		color: #20FF86;
		font-size: 32rpx;
		font-weight: 600;
	}

	.video-url {
		width: 670rpx;
		padding-bottom: 30rpx;
		border-bottom: 1px solid #505050;
		margin-bottom: 30rpx;
	}

	.img-423 {
		width: 32rpx;
		height: 32rpx;
	}

	.look-name {
		color: #20FF86;
		font-size: 30rpx;
		margin-bottom: 30rpx;
		font-weight: 500;
	}

	.list-public {
		background-color: #262626;
		padding: 30rpx 20rpx;
	}

	.bg {
		width: 750rpx;
		height: 580rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 200rpx 0 0;
	}

	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		display: flex;
		align-items: center;
	}

	page {
		border: none;
		background: rgba(48, 48, 48, 1);
		width: 100%;
		overflow-x: hidden !important;
	}
</style>