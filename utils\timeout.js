import store from './index.js';
export const timeOut = () => {
	let WebviewList = [];
	// 上一次点击时间
	let lastTime = null
	// 当前时间
	let currentTime = null
	// 超时时间【可以自己设置】
	// let sys_timeout = 5 * 1000
	let sys_timeout = 30 * 60 * 1000
	// 每隔多长时间检查是否超时【可以自己设置】
	let check_time = 1000
	// 计时器【此为功能实现的方法，现在为空】	
	let goOut = null
	const isTimeOut = () => {
		// 页面上一次的点击时间
		lastTime = store.state.lastTime
		currentTime = new Date().getTime()
		// 超时了
		if ((currentTime - lastTime) > sys_timeout) {
			return true;
		} else {
			return false;
		}
	}
	const isLoginOut = () => {
		clearInterval(goOut);
		//setInterval方法来确定多长时间检测一次有没有超时
		goOut = setInterval(() => {

			var pages = getCurrentPages();
			var page = pages[pages.length - 1];
			var currentWebview = page.$getAppWebview();
			// console.log(currentWebview); //获得当前webview的id

			
			if (isTimeOut()) {
				uni.showModal({
				    title: '提示',
				    content: '长时间未操作界面提醒',
					showCancel:false,
				    success: async (res)=> {
				        if (res.confirm) {
							store.commit('lastTimeUpdata', new Date().getTime());
				        } else if (res.cancel) {
				            console.log('用户点击取消');
				        }
				    }
				});
				//已经超时跳转到相应界面，不需要计时了
				// clearInterval(goOut)
			} else {

			}
			let flag = false;
			for (let webview of WebviewList) {
				if (webview.__uniapp_route == currentWebview.__uniapp_route) {
					flag = true;
				}
			}
			if (!flag) {
				WebviewList.push(currentWebview);
				currentWebview.addEventListener('touchstart', function() {
					console.log('点击了');
					store.commit('lastTimeUpdata', new Date().getTime());
				}, false);
				currentWebview.addEventListener('close', function() {
					// console.log('关闭了');
					for (var i = 0; i < WebviewList.length; i++) {
						if (WebviewList[i].__uniapp_route == currentWebview
							.__uniapp_route) {
							WebviewList.splice(i, 1);
						}
					}
				}, false);
			}

		}, check_time);
	}
	isLoginOut();
}