<template>
	<view>
		<template v-if="tabId === 2">
			<view class="display-a padding_30rpx color_FFFFFF">
				<view class="d-line"></view>
				<view class="font-size_32rpx">标题内容</view>
				<view class="btn" @click="startStream(titleData.answer, 1)">AI改写</view>
			</view>
			<textarea v-model="titleData.answer" class="edit-popup-textarea" placeholder="请输入标题内容"
				placeholder-class="placeholder" :maxlength="-1"></textarea>
			<view class="display-a padding_30rpx color_FFFFFF">
				<view class="d-line"></view>
				<view class="font-size_32rpx">话题</view>
				<view class="btn" @click="startStream(titleData.challenges, 2)">AI改写</view>
			</view>
			<textarea v-model="titleData.challenges" class="edit-popup-textarea" placeholder="请输入话题"
				placeholder-class="placeholder" :maxlength="-1"></textarea>
			<view style="height: 140rpx;"></view>

			<view class="but" @click="getEdit()">确认修改</view>
		</template>
		<template v-else>
			<view class="display-a padding_30rpx color_FFFFFF">
				<view class="d-line"></view>
				<view class="font-size_32rpx">类型</view>
				<view class="font-size_26rpx margin-left_20rpx">
					(
					<block v-if="obj.type == 6">企业宣传</block>
					<block v-if="obj.type == 7">同城团购</block>
					<block v-if="obj.type == 8">电商带货</block>
					<block v-if="obj.type == 9">知识科普</block>
					<block v-if="obj.type == 10">情感专家</block>
					<block v-if="obj.type == 11">口播文案</block>
					<block v-if="obj.type == 12">朋友圈营销</block>
					<block v-if="obj.type == 13">小红书笔记</block>
					<block v-if="obj.type == 14">文案仿写</block>
					<block v-if="obj.type == 15">智能代写</block>
					<block v-if="obj.type == 16">视频提取</block>
					<block v-if="obj.type == 17">一键仿写</block>
					<block v-if="ai_collect.type_tile">{{ai_collect.type_tile}}</block>
					)
				</view>
				<view class="btn" @click="startStream(tabId === 3 ?  ai_collect.content : obj.answer, tabId === 3 ? 4 : 3)">AI改写</view>
				<!-- <view class="font-size_24rpx margin-left_20rpx" style="color: #CBCACA;">{{obj.words}}字</view> -->
			</view>

			<view class="d-text">
				<!-- <rich-parser :html="obj.answer" domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load
					ref="article" selectable show-with-animation use-anchor>
				</rich-parser> -->
				<textarea v-if="tabId === 3" v-model="ai_collect.content" placeholder="请输入话题" style="height: 680rpx;"
					placeholder-class="placeholder" :maxlength="-1"></textarea>
				<textarea v-else v-model="obj.answer" placeholder="请输入" style="height: 680rpx;"
					placeholder-class="placeholder" :maxlength="-1"></textarea>
				
			</view>

			<view style="height: 140rpx;"></view>
			<view class="fixed">
				<view class="but2" @click="addWan()">确认修改</view>
				<view class="but2" @click="getCopy()">复制文案</view>
			</view>

		</template>


	</view>
</template>

<script>
	import {
		decodedString
	} from '../../../subPackages/subPackageA/utils/decodedString';
	export default {
		data() {
			return {

				id: '',

				obj: {},
				tabId: 1,
				titleData: {},
				isWhether: true,
				requestTask: null,
				ai_collect: {}
			}

		},

		watch: {

		},

		onLoad(options) {
			if (options.name) {
				this.$sun.title(options.name);
			}
			if (options.id) {
				this.id = options.id;
				if (options.tabId) {
					this.tabId = Number(options.tabId)
				}
				if (this.tabId === 2) {
					this.titleData = uni.getStorageSync('titleData')
				} else if (this.tabId === 3) {
					this.ai_collect = uni.getStorageSync('ai_collect')
				} else {
					this.getDetail();
				}

			}

			
		},

		onShow() {

		},
		onUnload() {
			this.stopStream()
		},
		methods: {
			// 中止请求
			stopStream() {
				if (this.requestTask) {
					this.requestTask.cancel();
					this.requestTask = null;
					console.log('已中止请求');
				}
			},
			add(text, type) {
				if (type === 1) {
					this.titleData.answer += text
				} else if (type === 2) {
					this.titleData.challenges += text
				} else if (type === 3) {
					this.obj.answer += text
				} else if (type === 4) {
					this.ai_collect.content += text
				}
			},
			del(type) {
				if (type === 1) {
					this.titleData.answer = ''
				} else if (type === 2) {
					this.titleData.challenges = ''
				} else if (type === 3) {
					this.obj.answer = ''
				} else if (type === 4) {
					this.ai_collect.content = ''
				}
			},
			// 一键仿写流式请求
			startStream(textMsg, type) {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中...", 'error');
					return;
				}
				if (!textMsg) {
					this.$sun.toast("请先输入文案", 'error');
					return;
				}

				this.isWhether = false

				uni.showLoading({
					title: '正在生成'
				})
				let url = this.$api.getAiSpread

				let data = {
					uid: uni.getStorageSync('uid'), // 请求参数
					content: textMsg,
					type
				}
				this.del(type)
				// 使用流式请求
				this.requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						uni.hideLoading()
						// 处理接收到的数据块
						try {

							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 确保换行符被保留（不进行额外处理，由computed属性处理格式化）
								this.add(text, type)
							} else if (typeof data === 'string') {
								// 如果没有结束标记，直接追加
								this.add(data, type)
							} else {
								console.warn("收到未知类型的数据块:", data);
							}
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						uni.hideLoading()
						this.isWhether = true
						console.log('文案生成完成');
					},
					onError: (err) => {
						uni.hideLoading()
						console.error("流式请求错误:", err);
					}
				});
			},
			// 确认修改标题
			async getEdit() {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中...", 'error');
					return;
				}
				// 调用服务器接口更新标题
				try {
					const result = await this.$http.post({
						url: this.$api.updateTitle,
						data: {
							uid: uni.getStorageSync('uid'),
							id: this.titleData.id,
							answer: this.titleData.answer,
							challenges: this.titleData.challenges
						}
					});

					if (result.errno == 0) {
						uni.showToast({
							title: '修改成功',
							icon: 'none'
						});
						uni.navigateBack()
					} else {
						uni.showToast({
							title: result.message || '修改失败',
							icon: 'none'
						});
					}
				} catch (e) {
					uni.showToast({
						title: '修改失败，请重试',
						icon: 'none'
					});
				}
			},
			// 确认修改标题
			async addWan() {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中...", 'error');
					return;
				}
				// 调用服务器接口更新标题
				try {
					const result = await this.$http.post({
						url: this.tabId === 3 ? this.$api.updateCollect : this.$api.seAiCreate,
						data: {
							uid: uni.getStorageSync('uid'),
							id: this.id,
							content: this.tabId === 3 ? this.ai_collect.content : this.obj.answer
						}
					});

					if (result.errno == 0) {
						uni.showToast({
							title: '修改成功',
							icon: 'none'
						});
						uni.navigateBack()
					} else {
						uni.showToast({
							title: result.message || '修改失败',
							icon: 'none'
						});
					}
				} catch (e) {
					uni.showToast({
						title: '修改失败，请重试',
						icon: 'none'
					});
				}
			},
			//复制
			getCopy() {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中...", 'error');
					return;
				}
				if (this.obj.answer || this.ai_collect.content) {
					uni.setClipboardData({
						data: this.tabId === 3 ? this.ai_collect.content : this.obj.answer,
						success: () => {
							// 复制成功的回调
							this.$sun.toast('复制成功');
							// uni.showToast({
							// 	title: '复制成功',
							// 	icon: 'success',
							// 	duration: 4000
							// });
						},
						fail: (err) => {
							console.log("复制失败原因===>", err);
							// 复制失败的回调
							uni.showToast({
								title: '复制失败：' + err,
								icon: 'none'
							});
						}
					});
				}
			},

			async getDetail() {
				const result = await this.$http.post({
					url: this.$api.aiCreateDetail,
					data: {
						uid: uni.getStorageSync('uid'),
						create_id: this.id
					}
				});
				if (result.errno == 0) {
					this.obj = result.data;
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},

		}
	}
</script>

<style lang="scss">
	.fixed {
		position: fixed;
		bottom: 50rpx;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-around;

		.but2 {
			width: 300rpx;
			text-align: center;
			border-radius: 100rpx;
			box-shadow: 2px 3px 14px 0px rgba(30, 156, 214, 0.81), inset 0px 0px 11px 0px rgba(204, 235, 255, 0.3);
			background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
			color: #FFF;
			font-size: 32rpx;
			font-weight: 600;
			padding: 22rpx 0;
		}

	}

	.display-a {
		position: relative;

		.btn {
			margin-left: auto;
			background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
			padding: 6rpx 8rpx;
			border-radius: 8rpx;
		}
	}

	.edit-popup-textarea {
		width: 710rpx;
		height: 360rpx;
		background-color: #111D37;
		border-radius: 10rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
		color: #FFFFFF;
		box-sizing: border-box;
		margin: 0 auto;
	}

	.but {
		position: fixed;
		bottom: 50rpx;
		left: 70rpx;
		width: 610rpx;
		text-align: center;
		border-radius: 100rpx;
		box-shadow: 2px 3px 14px 0px rgba(30, 156, 214, 0.81), inset 0px 0px 11px 0px rgba(204, 235, 255, 0.3);
		background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
		color: #FFF;
		font-size: 32rpx;
		font-weight: 600;
		padding: 22rpx 0;
	}

	.d-text {
		width: 710rpx;
		background-color: #111D37;
		border-radius: 10rpx;
		margin: 0 20rpx;
		padding: 20rpx;
		color: #FFF;
	}

	.d-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245), rgb(66, 72, 244) 100%);
		margin-right: 20rpx;
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
</style>