<template>
	<view>

		<!-- <view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">标题名称</view>
			<view class="red-dot">*</view>
		</view>
		<input type="text" v-model="name" class="r-input margin-bottom_40rpx" placeholder="请输入标题名称" placeholder-class="placeholder" /> -->
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">标题主题</view>
			<view class="red-dot">*</view>
		</view>
		<textarea class="margin-bottom_40rpx" v-model="title" placeholder="请输入你的产品和服务（可以输入多个用逗号隔开）"
			placeholder-class="placeholder"></textarea>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">标题附加</view>
		</view>
		<textarea class="margin-bottom_40rpx" v-model="question" placeholder="用一句话描述你的产品服务与特色"
			placeholder-class="placeholder"></textarea>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">生成条数</view>
			<view class="red-dot">*</view>
		</view>
		<input type="text" v-model="row" class="r-input margin-bottom_40rpx" placeholder="输入1，就是生成1条视频标题文案"
			placeholder-class="placeholder" />

		<view style="height: 160rpx;"></view>
		<view class="display-a-js pos-bott">
			<view class="look-ai" @click="getLook()">查看记录</view>
			<view class="r-but" @click="getAccountInfo()">
				点击立即生成
				<!-- <span class="margin-left_16rpx">
					{{tallySetObj.ai_title_create_deduct}}点/1次
				</span> -->
			</view>
		</view>



	</view>
</template>

<script>
	export default {
		data() {
			return {

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				name: '', //标题名称
				row: '', //条数
				title: '', //标题主题
				question: '', //标题附加

				answer: [], //返回

				isWhether: true, //判断重复点击

				back: false, //是否返回
			}
		},

		onLoad(options) {
			if (options.back) {
				this.back = true
			}
		},

		onShow() {

		},

		methods: {

			//查看记录
			getLook() {
				uni.redirectTo({
					url: '/pages/index/AICreation/record?type=2'
				})
			},

			//查询点数是否足够
			async getAccountInfo() {

				let content = '';

				// if (!this.name) {
				// 	this.$sun.toast("请输入标题名称",'none');
				// 	return;
				// }
				if (!this.title) {
					this.$sun.toast("请输入标题主题", 'none');
					return;
				}
				if (!this.row) {
					this.$sun.toast("请输入生成条数", 'none');
					return;
				}

				if (this.question) {
					content = '请根据' + this.title + '主题写一条长度50字以内的的短视频作品发布标题,并包含5个以内的话题,标题和话题中必须包含' + this.title +
						',并介绍一下我们的特色:' + this.question
				} else {
					content = '请根据' + this.title + '主题写一条长度50字以内的的短视频作品发布标题,并包含5个以内的话题,标题和话题中必须包含' + this.title
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.accountInfo,
					data: {
						type: 3,
						uid: uni.getStorageSync('uid'),
					}
				});
				if (result.errno == 0) {
					this.getCopyImitation(content);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//一键仿写
			async getCopyImitation(content) {
				const result = await this.$http.post({
					url: 'https://vapi.weijuyunke.com/api/doBao/copyImitation',
					data: {
						content: content,
						ipStatus: 2,
						count: this.row,
						// countType: 50,
						domainName: 'vapi.weijuyunke.com',
						requestIp: '**************'
					},
					mask: true,
					title: '请求中...'
				});
				if (result.code == 200) {
					// this.$sun.toast("操作成功");
					// setTimeout(() => {
					// 	this.msgText = result.data;
					// 	this.isWhether = true;
					// }, 1000);
					this.getAICreation(result.data);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//生成AI文案
			async getAICreation(list) {

				list = list.replace(/\s+/g, "");
				let arrlList = list.split('&');
				for (let i = 0; i < arrlList.length; i++) {
					// arrlList[i] = arrlList[i].slice(2);
					arrlList[i] = arrlList[i];
				}
				this.answer = arrlList;

				const result = await this.$http.post({
					url: this.$api.addTitle,
					data: {
						uid: uni.getStorageSync('uid'),
						// name: this.name,
						title: this.title,
						question: this.question,
						answer: this.answer, // 接口返回的文本
						row: this.row,
						desc: list //拼接的文本
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						if (this.back) {
							uni.navigateBack()
						} else {
							uni.redirectTo({
								url: '/pages/index/AICreation/record?type=2'
							})
						}

						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

		}
	}
</script>

<style lang="scss">
	.look-ai {
		width: 230rpx;
		text-align: center;
		background-color: #FFF;
		color: #000;
		font-size: 32rpx;
		padding: 30rpx 0;
		border-radius: 100rpx;
	}

	.placeholder {
		color: #999999;
	}

	.pos-bott {
		width: 710rpx;
		position: fixed;
		bottom: 50rpx;
		z-index: 9;
	}

	.r-but {
		font-size: 32rpx;
		color: #000;
		padding: 30rpx 0;
		width: 460rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118), rgb(0, 236, 255) 97.869%);
	}

	textarea {
		width: 670rpx;
		height: 200rpx;
		padding: 20rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.r-input {
		width: 670rpx;
		padding: 20rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.r-top {
		padding: 30rpx 20rpx;
	}

	.red-dot {
		color: #FF0000;
		margin-left: 8rpx;
	}

	.r-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245), rgb(66, 72, 244) 100%);
		margin-right: 14rpx;
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
		padding: 20rpx 20rpx 0;
	}
</style>