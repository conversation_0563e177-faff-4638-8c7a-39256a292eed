<template>
	<view>
		
		<view style="height: 20rpx;"></view>
		
		<view class="img-271 color_FFFFFF" :style="{'background-image': 'url('+imgUrl+'271.png'+')'}" v-if="indexType == 2 && brokerageUser.id">
			<view class="display-a-js margin-bottom_30rpx p-bo" style="padding: 46rpx 40rpx 20rpx;">
				<view>
					<view class="margin-bottom_10rpx">可提现金额</view>
					<view class="font-size_36rpx font-weight_bold">￥{{brokerageUser.brokerage}}</view>
				</view>
				<view class="withdrawal" v-if="cash_open == 1" @click="getWithdrawal()">提现</view>
			</view>
			<view class="display-a">
				<view class="width_236rpx-center">
					<view class="color_969696 margin-bottom_20rpx">今日收益(元)</view>
					<view class="font-weight_bold">{{Number(brokerageUser.todayGet).toFixed(2)}}</view>
				</view>
				<view class="width_236rpx-center">
					<view class="color_969696 margin-bottom_20rpx">本月收益(元)</view>
					<view class="font-weight_bold">{{Number(brokerageUser.monthGet).toFixed(2)}}</view>
				</view>
				<view class="width_236rpx-center">
					<view class="color_969696 margin-bottom_20rpx">累计收益(元)</view>
					<view class="font-weight_bold">{{Number(brokerageUser.allGet).toFixed(2)}}</view>
				</view>
			</view>
		</view>
		
		<view class="display-a margin-bottom_10rpx" style="padding: 24rpx;">
			<block v-for="(item,index) in arr" :key="index">
				<view @click="getType(item.id)" class="tabs" :class="type == item.id ? 'tabs-2' : 'tabs-1'">{{item.name}}</view>
			</block>
			
			<!-- <block v-if="indexType == 2">
				<view class="s-line"></view>
				<view class="font-size_36rpx font-weight_bold color_FFFFFF">收益列表</view>
			</block> -->
			<view class="display-a color_FFFFFF margin-left-auto" v-if="indexType == 2">
				<picker mode="date" fields="month" :value="birth" :start="startDate" :end="endDate"
					@change="bindDateChange">
					<input type="text" style="width: 120rpx;" disabled placeholder="请选择" v-model="birth"
						placeholder-class="font-size_28rpx" />
				</picker>
				<image class="img-99" :src="imgUrl+'267.png'"></image>
			</view>
		</view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<block v-if="indexType == 1">
						<view class="display-a margin-bottom_20rpx">
							<view class="font-weight_bold">{{item.cash_type == 1 ? '提现到支付宝' : '提现到微信零钱'}}</view>
							<view v-if="item.is_status == 1" class="label ">申请中</view>
							<view v-if="item.is_status == 2" class="label" style="border: 1px solid rgb(153, 153, 153);color: #999999;">已完成</view>
							<view v-if="item.is_status == 3" class="label" style="border: 1px solid rgb(255, 0, 0);color: #ff0000;">已驳回</view>
							<view class="margin-left-auto color_31A952 font-size_32rpx font-weight_bold">￥{{item.commission_wait}}</view>
						</view>
						<view class="display-a margin-bottom_20rpx">
							<view class="color_757575 font-size_26rpx">{{item.create_time}}</view>
							<view class="color_757575 font-size_26rpx margin-left-auto">实际到账 ￥{{item.commission_actual}}</span></view>
							<view class="color_757575 font-size_26rpx margin-left_16rpx">手续费 ￥{{item.service_charge}}</view>
						</view>
						<view class="display-a" v-if="item.cash_type == 1">
							<view></view>
							<view class="color_757575 font-size_26rpx margin-left-auto">{{item.nickname}}</view>
							<view class="color_757575 font-size_26rpx margin-left_20rpx">{{item.telphone}}</view>
						</view>
						<view v-if="item.is_status == 3" style="height: 20rpx;" class="p-bo-2"></view>
						<view v-if="item.is_status == 3" class="padding-top_20rpx color_FF0000">驳回原因: {{item.refuse}}</view>
					</block>
					<block v-if="indexType == 2">
						<block v-if="type == 1">
							<view class="display-a padding-bottom_20rpx p-bo-2 margin-bottom_30rpx">
								<image class="avatar" :src="item.avatar"></image>
								<view>
									<view class="font-weight_bold font-size_30rpx margin-bottom_10rpx">{{item.nickname}}</view>
									<view class="display-a">
										<view class="wi-level" :class="item.is_repurchase == 1 ? 'wi-level-2' : 'wi-level-3'">{{item.is_repurchase == 1 ? '复购' : '新购'}}</view>
										<view class="wi-level wi-level-1">{{item.commission_level == 2 ? '间推' : '直推'}}</view>
									</view>
								</view>
								<view class="margin-left-auto text-align_right">
									<view class="w-money">￥{{item.total}}</view>
									<view class="color_c7c7c7">获得<span class="color_FF0000">￥{{item.money}}</span>收益</view>
								</view>
								<!-- <view>
									<view class="font-size_32rpx font-weight_bold margin-bottom_10rpx" style="width: 440rpx;">
										<block v-if="item.type == 1">{{'邀请粉丝 '+item.nickname}}</block>
										<block v-if="item.type == 2">{{item.nickname + ' 粉丝购买VIP'}}</block>
										<block v-if="item.type == 3">{{item.nickname + ' 粉丝购买点数'}}</block>
									</view>
									<view class="color_757575 font-size_26rpx">{{item.create_time}}</view>
								</view>
								<view class="color_31A952 font-weight_bold margin-left-auto font-size_32rpx">￥{{item.money}}</view> -->
							</view>
							<view class="display-a-js ">
								<view class="color_757575">
									收益类型: 
									<span class="margin-left_10rpx">
										<block v-if="item.type == 1">邀请新人</block>
										<block v-if="item.type == 2">购买VIP</block>
										<block v-if="item.type == 3">购买点数</block>
										<block v-if="item.type == 4">资产交易</block>
									</span>
								</view>
								<view class="color_757575 font-size_26rpx">{{item.create_time}}</view>
							</view>
						</block>
						<block v-if="type == 4 || type == 3">
							<view class="display-a color_B7B7B7 padding-bottom_20rpx p-bo-2 margin-bottom_20rpx">
								<image class="img-268" :src="imgUrl+'268.png'"></image>
								<view>订单号: {{item.fro_log_no}}</view>
								<view class="copy" @click="getCopy(item.fro_log_no)">复制</view>
							</view>
							<view class="display" v-if="type == 4">
								<view class="frame">
									<!-- 标准 -->
									<image v-if="item.type == 2" class="r-video" mode="widthFix" :src="item.fca_url+'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"></image>
									<!-- 高级  -->
									<image v-if="item.type == 1" class="r-video" mode="widthFix" :src="item.fa_video_url+'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"></image>
								</view>
								<view class="color_FFFFFF">
									<view style="width: 510rpx;" class="font-size_32rpx font-overflow margin-bottom_20rpx">{{item.frg_title}}</view>
									<view class="color_B7B7B7 font-size_26rpx" style="margin-bottom: 100rpx;">下单时间: {{item.create_time}}</view>
									<view class="display-a-js">
										<view class="font-size_30rpx">实付: <span class="font-size_32rpx font-weight_bold color_FF0000 margin-left_10rpx">￥{{item.order_price}}</span></view>
										<view class="font-size_30rpx">收益: <span class="font-size_32rpx font-weight_bold margin-left_10rpx" style="color: #FFE500;">￥{{item.user_profit}}</span></view>
									</view>
								</view>
							</view>
							<view class="display" v-if="type == 3">
								<image class="img-266" :src="imgUrl+'266.png'"></image>
								<view class="color_FFFFFF">
									<view style="width: 490rpx;" class="font-size_32rpx font-overflow margin-bottom_20rpx">{{item.frg_title}}</view>
									<view class="color_B7B7B7 font-size_26rpx margin-bottom_20rpx">下单时间: {{item.create_time}}</view>
									<view class="display-a-js">
										<view class="font-size_30rpx">实付: <span class="font-size_32rpx font-weight_bold color_FF0000 margin-left_10rpx">￥{{item.order_price}}</span></view>
										<view class="font-size_30rpx">收益: <span class="font-size_32rpx font-weight_bold margin-left_10rpx" style="color: #FFE500;">￥{{item.user_profit}}</span></view>
									</view>
								</view>
							</view>
						</block>
						
					</block>
				</view>
			</block>
		</mescroll-body>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				brokerageUser: {},
				
				type: '', //1待审核 2已打款 3已驳回 //4形象 3声音 1分销
				
				indexType: '', //1提现记录 2分销记录
				
				arr: [],
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				birth: '',
				yeartime: '',
				monthtime: '',
				
				cash_open: '', //1开启
				
			}
		},
		
		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		
		onLoad(options) {
			//获取系统信息
			uni.getSystemInfo({ 
				success: res => {
					if (options.index == 1) {
						this.windowHeight = res.windowHeight * 2 - 130;
					}
					if (options.index == 2) {
						this.windowHeight = res.windowHeight * 2 - 490;
					}
				}
			})
			if (options.index) {
				this.indexType = options.index;
				if (this.indexType == 1) {
					this.arr = [
						{id:'',name: '全部'},
						{id:1,name:'待审核'},
						{id:2,name:'已打款'},
						{id:3,name:'已驳回'}
					]
					this.$sun.title("提现记录");
				}
				if (this.indexType == 2) {
					this.$sun.title("收益列表");
					this.arr = [
						{id:1,name:'分销收益'},
						{id:4,name:'形象收益'},
						{id:3,name:'声音收益'}
					];
					this.type = 1;
				}
				
			}
			this.getBrokerageSet();
		},
		
		onShow() {
			
			if (uni.getStorageSync('uid')) {
				var myDate = new Date();
				this.yeartime = myDate.getFullYear();
				this.monthtime = myDate.getMonth() + 1;
				if (this.monthtime.toString().length == 1) {
					this.monthtime = '0' + this.monthtime;
				}
				this.birth = this.yeartime + '-' + this.monthtime;
				setTimeout(()=> {
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
				}, 1000);
				this.getBrokerageIndex();
			}else {
				uni.showModal({
					content:"请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},
		
		methods: {
			
			//分销设置
			async getBrokerageSet() {
				const result = await this.$http.post({
					url: this.$api.brokerageSet,
				});
				if (result.errno == 0) {
					this.cash_open = result.data.cash_open;
				}
			},
			
			//提现
			getWithdrawal() {
				
				if (Number(this.brokerageUser.brokerage) <= 0) {
					this.$sun.toast("暂无提现佣金",'none');
					return;
				}
				
				uni.navigateTo({
					url: '/pages/my/distribution/withdrawal'
				})
			},
			
			//分销中心
			async getBrokerageIndex() {
				const result = await this.$http.post({
					url: this.$api.brokerageIndex,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.brokerageUser = result.data;
				}
			},
			
			/*  日期选择  */
			bindDateChange(e) {
				// console.log("选择的日期", e.target.value);
				this.birth = e.target.value;
				this.yeartime = e.target.value.split('-')[0];
				this.monthtime = e.target.value.split('-')[1];
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();
			
				if (type === 'start') {
					year = year - 100;
				} else if (type === 'end') {
					year = year;
				}
				month = month > 9 ? month : '0' + month;;
				// day = day > 9 ? day : '0' + day;
				return `${year}-${month}`;
			},
			
			//复制
			getCopy(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						// 复制成功的回调
						this.$sun.toast('复制成功');
						// uni.showToast({
						// 	title: '复制成功',
						// 	icon: 'success',
						// 	duration: 4000
						// });
					},
					fail: (err) => {
						console.log("复制失败原因===>",err);
						// 复制失败的回调
						uni.showToast({
							title: '复制失败：'+err,
							icon: 'none'
						});
					}
				});
			},
			
			//返回
			navig() {
				
				let pages = getCurrentPages();  //获取所有页面栈实例列表
				
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}else {
					uni.navigateBack();
				}
				
			},
			
			async upCallback(scroll) {
				
				let getUrl = '';
				let getData = '';
				
				if (this.indexType == 1) {
					getUrl = this.$api.commissionLog;
					getData = {
						uid: uni.getStorageSync("uid"),
						is_status: this.type,
						year: this.yeartime,
						month: this.monthtime,
						page: scroll.num,
						psize: 10
					}
				}
				
				if (this.indexType == 2 && this.type == 1) {
					getUrl = this.$api.brokerageLogList;
					getData = {
						uid: uni.getStorageSync("uid"),
						year: this.yeartime,
						month: this.monthtime,
						page: scroll.num,
						psize: 10
					}
				}
				
				if (this.indexType == 2 && (this.type == 3 || this.type == 4)) {
					getUrl = this.$api.resourcesProfitList;
					getData = {
						uid: uni.getStorageSync("uid"),
						type: this.type,
						year: this.yeartime,
						month: this.monthtime,
						page: scroll.num,
						psize: 10
					}
				}
				
				
				const result = await this.$http.post({
					url: getUrl,
					data: getData
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			//类型
			getType(type) {
				// if (this.indexType == 1) {
				this.type = type;
				this.list = [];
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
				// }
			},
			
		}
	}
</script>

<style lang="scss">
	
	.wi-level-3 {
		background-color: #FF0000;
		color: #FFF;
	}
	
	.wi-level-2 {
		background-color: #2CABE7;
		color: #FFF;
	}
	
	.wi-level-1 {
		background-color: #E6E6E6;
		color: #000;
	}
	
	.wi-level {
		width: 70rpx;
		border-radius: 10rpx;
		text-align: center;
		padding: 6rpx 0;
		margin-right: 14rpx;
		font-size: 24rpx;
	}
	
	.w-money {
		color: #00FF61;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 6rpx;
	}
	
	.s-line {
		width: 8rpx;
		height: 34rpx;
		background-color: #0072FF;
		border-radius: 10rpx;
		margin-right: 20rpx;
	}
	
	.img-266 {
		width: 160rpx;
		height: 160rpx;
		margin-right: 20rpx;
		border-radius: 10rpx;
	}
	
	.p-bo-2 {
		border-bottom: 1px solid rgb(48, 48, 48);
	}
	
	.r-video {
		width: 140rpx;
		border-radius: 10rpx;
		position: absolute;
		z-index: 2;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
	
	.frame {
		position: relative;
		width: 140rpx;
		height: 249rpx;
		border-radius: 10rpx;
		margin-right: 20rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: block;
		/* 避免底部空白 */
	}
	
	.copy {
		width: 104rpx;
		text-align: center;
		background-color: #585858;
		color: #FFF;
		font-size: 26rpx;
		padding: 6rpx 0;
		border-radius: 100rpx;
		margin-left: auto;
	}
	
	.img-268 {
		width: 30rpx;
		height: 30rpx;
		margin-right: 14rpx;
	}
	
	.withdrawal {
		width: 150rpx;
		text-align: center;
		background-color: #FFF;
		color: #2394FB;
		padding: 20rpx 0;
		font-size: 30rpx;
		border-radius: 100rpx;
	}
	
	.p-bo {
		border-bottom: 1px solid rgba(255, 255, 255, 0.19);
	}
	
	.img-271 {
		width: 710rpx;
		height: 330rpx;
		background-repeat: no-repeat;
		background-size: contain;
		margin: 0 20rpx 30rpx;
		border-radius: 10rpx;
	}
	
	.list-public {
		background-color: #1A1A1A;
		padding: 34rpx 24rpx;
		color: #FFF;
	}
	
	.avatar {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
		border-radius: 100rpx;
	}
	
	.label {
		width: 90rpx;
		font-size: 24rpx;
		border-radius: 10rpx;
		margin-left: 14rpx;
		padding: 4rpx 0;
		border: 1px solid rgb(40, 196, 69);
		color: #28C445;
		text-align: center;
	}
	
	.img-99 {
		width: 20rpx;
		height: 20rpx;
	}
	
	
	.color_31A952 {
		color: #31A952;
	}
	
	.tabs-2 {
		color: #FFFFFF;
		background-color: #1E6CEB;
	}
	
	.tabs-1 {
		color: #B2B1B1;
		background-color: #232323;
	}
	
	.tabs {
		width: 140rpx;
		text-align: center;
		padding: 6rpx 10rpx;
		margin-right: 30rpx;
		border-radius: 10rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
	
</style>


