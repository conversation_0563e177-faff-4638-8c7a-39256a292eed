<template>
	<text>{{ digit }}</text>
</template>

<script>
export default {
	data() {
		return {
			speed: 10,
			digit: 0,
			digitAsyn: 0
		};
	},
	props: {
		to: {
			type: Number,
			default: 1000
		},
		from: {
			type: Number,
			default: 0
		},
		time: {
			type: Number,
			default: 1000
		},
		fixed: {
			type: Number,
			default: 0
		},
		delay: {
			type: Number,
			default: 1000
		},
		mode: {
			type: String,
			default: 'RANDOM'
		}
	},
	methods: {
		start() {
			this.digit = this.digitAsyn;
			switch (this.mode) {
				case 'RANDOM': {
					let times = this.time / this.speed;
					let i = 0;
					let time = setInterval(() => {
						let addVal = 0;
						if (times == 0) {
							clearInterval(time);
						} else if (times == 1) {
							let dVal = (this.to - this.digit) / times;
							addVal = dVal;
						} else {
							let dVal = (this.to - this.digit) / times;
							addVal = parseFloat(Math.random() * dVal).toFixed(this.fixed);
						}

						this.digit = (parseFloat(this.digit) + parseFloat(addVal)).toFixed(this.fixed);
						times--;
					}, this.speed);
					break;
				}
				case 'AVERAGE': {
					let times = this.time / this.speed;
					let dValue = (this.to - this.digitAsyn) / times;
					let i = 0;
					let time = setInterval(() => {
						this.digit = parseFloat(this.digitAsyn + dValue * i).toFixed(this.fixed);
						if (times == i) {
							clearInterval(time);
						}
						i++;
					}, this.speed);
					break;
				}
			}
		}
	},
	created() {
		setTimeout(() => {
			this.start();
		}, this.delay);
	},
	watch: {
		from(newVal) {
			this.digitAsyn = newVal;
		},
		to(newVal, oldVal) {
			this.digitAsyn = oldVal;
			this.start();
		}
	}
};
</script>

<style></style>
