<template>
	<view class="step-info">
		<view class="step-number">
			<block v-for="(item,index) in stepList" :key="index">
				<view @click.stop="openItem(item)" class="item-state">
					<image class="state-img" :src="getImg(index) || (item.unCheckedImg || unCheckedImg)"></image>
					<view class="step-name">
						<view class="name" :style="{color:getColor(index) || (item.unCheckedColor || unCheckedColor)}">
							{{item.name}}
						</view>
					</view>
				</view>
				<view class="line" :style="{background:getLine(index) || (item.unCheckedLine || unCheckedLine)}"
					v-if="index < stepList.length-1"></view>
			</block>
		</view>
	</view>
</template>

<script>
	import checkedImg from '../../static/checkedImg.png'
	import unCheckedImg from '../../static/unCheckedImg.png'
	export default {
		props: {
			//当前步骤
			step: {
				type: Number,
				default: 1
			},
			//步骤列表
			stepList: {
				type: Array,
				default: null
			},
			//已完成的图片路径
			checkedImg: {
				type: String,
				default: checkedImg
			},
			//未完成的图片路径
			unCheckedImg: {
				type: String,
				default: unCheckedImg
			},
			//已完成的字体颜色
			checkedColor: {
				type: String,
				default: '#3FA8FE'
			},
			//未完成的字体颜色
			unCheckedColor: {
				type: String,
				default: '#FFF'
			},
			//已完成的线条颜色
			checkedLine: {
				type: String,
				default: '#3FA8FE'
			},
			//未完成的线条颜色
			unCheckedLine: {
				type: String,
				default: '#8C8C8C'
			}
		},
		data() {
			return {}
		},
		methods: {
			//获取图片
			getImg(e) {
				let index = Number(e)
				if (this.step > index) return this?.stepList?.[index].checkedImg || this.checkedImg
			},
			//获取线
			getLine(e) {
				let index = Number(e)
				if (this.step > index) return this?.stepList?.[index].checkedLine || this.checkedLine
			},
			//获取颜色
			getColor(e) {
				let index = Number(e)
				if (this.step > index) return this?.stepList?.[index].checkedColor || this.checkedColor
			},
			openItem(item) {
				this.$emit('clickStep', item)
			}
		}
	}
</script>

<style>
	.step-info {
		width: 100%;
		height: auto;
	}

	.step-number {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.item-state {
		height: 64rpx;
	}

	.state-img {
		width: 64rpx;
		height: 64rpx;
		display: flex;
		margin: auto;
	}

	.line {
		width: 100%;
		height: 6rpx;
		margin: 0 8rpx;
	}


	.step-name {
		margin-top: 16rpx;
		width: 160rpx;
		align-items: center;
		justify-content: space-between;
		font-weight: bold;
		text-align: center
	}

	.name {
		font-size: 30rpx;
		/* line-height: 28rpx; */
		color: #333333;
	}
</style>