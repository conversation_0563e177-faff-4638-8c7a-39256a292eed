<template>
	<view>

		<view class="h_30rpx"></view>

		<view class="list-public">
			<view class="name">{{name}}</view>
			<textarea v-model="msgText" maxlength="520" :cursor-spacing="50" placeholder="请输入文案"
				placeholder-class="placeholder">
			</textarea>
			<view class="display-a margin-bottom_20rpx">
				<view class="msg-tips">{{msgText.length}}/500字</view>
			</view>
			<view class="display-a" style="color: #EBEAEA;">
				<view class="display-a margin-right_40rpx" @click="startStream()">
					<image class="img-409" :src="imgUrl+'410.png'"></image>
					<view class="font-size_26rpx">一键仿写</view>
				</view>
				<!-- <view class="display-a">
					<image class="img-409" :src="imgUrl+'411.png'"></image>
					<view class="font-size_26rpx">AI翻译</view>
				</view> -->
				<view class="clear-clip" @click="getClear()">清空</view>
			</view>
		</view>

		<view style="height: 160rpx;"></view>
		<view class="pos-bott">
			<view class="but-next" @click="getClip()">去创作</view>
		</view>

	</view>
</template>

<script>
	import {
		decodedString
	} from '../../../subPackages/subPackageA/utils/decodedString';
	export default {
		data() {
			return {
				isWhether: true,
				imgUrl: this.$imgUrl,

				name: '',
				msgText: '', //文案

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				requestTask: null,

			}
		},

		onLoad(options) {
			if (options.name) {
				this.name = options.name;
				this.msgText = uni.getStorageSync('answer');
			}
		},
		onUnload() {
			this.stopStream();
		},

		methods: {

			getClip() {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中，请稍后再试", 'none');
					return;
				}
				if (!this.msgText) {
					this.$sun.toast("请输入文案", 'none');
					return;
				}

				if (this.msgText.length > 500) {
					this.$sun.toast("请控制文案在500字以内!", 'none');
					return;
				}

				uni.navigateTo({
					url: '/pages/index/clip/clip?answerType=1'
				})
			},

			// 中止请求
			stopStream() {
				if (this.requestTask) {
					this.requestTask.cancel();
					this.requestTask = null;
					console.log('已中止请求');
				}
			},
			// 一键仿写流式请求
			startStream() {
				if (!this.msgText) {
					this.$sun.toast("请先输入文案", 'error');
					return;
				}
				if (!this.isWhether) {
					this.$sun.toast("正在生成中，请稍后再试", 'none');
					return;
				}
				this.isWhether = false

				uni.showLoading({
					title: '正在生成'
				})
				let url = this.$api.rewriteHotText

				let data = {
					uid: uni.getStorageSync('uid'), // 请求参数
					content: this.msgText
				}
				this.msgText = ''
				// 使用流式请求
				this.requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						uni.hideLoading()
						// 处理接收到的数据块
						try {

							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 确保换行符被保留（不进行额外处理，由computed属性处理格式化）
								this.msgText += text;
							} else if (typeof data === 'string') {
								// 如果没有结束标记，直接追加
								this.msgText += data;
							} else {
								console.warn("收到未知类型的数据块:", data);
							}
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						uni.hideLoading()
						uni.setStorageSync("answer", this.msgText);
						this.isWhether = true
						console.log('文案生成完成');
					},
					onError: (err) => {
						uni.hideLoading()
						console.error("流式请求错误:", err);
					}
				});
			},

			//查询点数是否足够
			async getAccountInfo() {

				if (!this.msgText) {
					this.$sun.toast("请先输入文案", 'error');
					return;
				}

				// uni.showModal({
				// 	content: this.tallySetObj.ai_create_deduct + '点/1次,一键仿写会覆盖当前文案,是否确认?',
				// 	cancelText: "取消",
				// 	confirmText: "确认",
				// 	success: async (res) => {
				// 		if (res.confirm) {
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				const result = await this.$http.post({
					url: this.$api.accountInfo,
					data: {
						type: 1,
						uid: uni.getStorageSync('uid'),
					}
				});
				if (result.errno == 0) {
					this.getCopyImitation();
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
				// 		} else if (res.cancel) {

				// 		}
				// 	}
				// })

			},

			//一键仿写
			async getCopyImitation() {
				const result = await this.$http.post({
					url: 'https://vapi.weijuyunke.com/api/doBao/copyImitation',
					data: {
						content: '请根据【' + this.msgText + '】热点内容写1条长度480字以内的文案，不要附加话题，只生成纯文案。',
						ipStatus: 2,
						domainName: 'vapi.weijuyunke.com',
						requestIp: '**************'
					},
					mask: true,
					title: '请求中...'
				});
				if (result.code == 200) {
					this.getAICreation(result.data);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//生成AI文案
			async getAICreation(answer) {

				const result = await this.$http.post({
					url: this.$api.aiCreateAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: "热点追踪一键仿写",
						type: 17,
						question: this.msgText, //拼接的文本
						answer: answer, // 接口返回的文本
						words: answer.length
					}
				});
				if (result.errno == 0) {
					this.$sun.toast("操作成功");
					setTimeout(() => {
						this.msgText = answer;
						uni.setStorageSync("answer", answer);
						this.isWhether = true;
					}, 1000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//清空文本
			getClear() {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中，请稍后再试", 'none');
					return;
				}
				this.msgText = '';
			},

		}
	}
</script>

<style lang="scss">
	.clear-clip {
		width: 90rpx;
		text-align: center;
		background-color: #2D2E33;
		border-radius: 10rpx;
		margin-left: auto;
		color: #EBEAEA;
		font-size: 24rpx;
		padding: 8rpx 0;
	}

	.img-409 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
	}

	.msg-tips {
		// text-align: right;
		// padding-right: 20rpx;
		// padding-bottom: 20rpx;
		// margin-bottom: 20rpx;
		margin-left: auto;
		font-size: 24rpx;
		color: #C9CACA;
	}

	textarea {
		width: 650rpx;
		margin-bottom: 20rpx;
		color: #ACACAC;
		height: 400rpx;
	}

	.name {
		color: #FFF;
		font-size: 40rpx;
		font-weight: 600;
		margin-bottom: 20rpx;
	}

	.list-public {
		background-color: #171717;
		padding: 30rpx;
	}

	.but-next {
		width: 710rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgba(89, 238, 82, 1), rgba(130, 255, 242, 1) 100%);
		padding: 30rpx 0;
		font-size: 32rpx;
		font-weight: 600;
		color: #000;
	}

	.pos-bott {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		padding: 20rpx 20rpx 30rpx;
		z-index: 99;
		background: #1E1E1E;
	}

	page {
		border: none;
		background: #1E1E1E;
		width: 100%;
		overflow-x: hidden !important;
	}
</style>