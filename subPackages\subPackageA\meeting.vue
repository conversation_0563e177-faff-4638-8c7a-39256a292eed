<template>
	<view class="main">
		<!-- 会议录音列表 -->
		<view class="title-container">
			<view class="title-text">会议录音列表</view>
			<view class="upload-btn" @click="uploadAll">一键上传</view>
			<view class="upload-btn" @click="getMeetingList">历史会议</view>
		</view>
		<view class="list">
			<view class="item" v-for="(item, index) in list" :key="index">
				<view class="item-left">
					<view class="item-left-time">{{ item.audioName }}</view>
				</view>
				<!-- 状态 -->
				<view class="item-right">
					<view class="item-right-status" v-if="item.status === 3">
						<view class="item-right-status-icon upload">上传完成</view>
					</view>
					<view class="item-right-status" v-else-if="item.status === 2">
						<view class="item-right-status-icon uploading-now">上传中</view>
					</view>
					<view class="item-right-status" v-else-if="item.status === 4">
						<view class="item-right-status-icon upload-failed">上传失败</view>
					</view>
					<view class="item-right-status" v-else-if="item.status === 5">
						<view class="item-right-status-icon upload-success">已提交</view>
					</view>
					<view class="item-right-status" v-else>
						<view class="item-right-status-icon uploading">等待上传</view>
					</view>
				</view>
			</view>
		</view>再

		<view class="voice">
			<sound-recording v-show="!isShowHistory" :maximum="600" @confirm="getConfirm" @start="getStart"
				:autoConfirm="true" @stopAll="stopAll"></sound-recording>
		</view>

		<!-- 修改名称弹窗 -->
		<view class="edit-name-modal" v-if="showEditModal">
			<view class="edit-name-mask" @click="cancelEdit"></view>
			<view class="edit-name-content">
				<view class="edit-name-header">
					<text class="edit-name-title">修改会议名称</text>
				</view>
				<view class="edit-name-body">
					<input class="edit-name-input" v-model="editNameValue" placeholder="请输入会议名称" maxlength="50" />
				</view>
				<view class="edit-name-footer">
					<view class="edit-name-btn cancel-btn" @click="cancelEdit">取消</view>
					<view class="edit-name-btn confirm-btn" @click="confirmEdit">确认</view>
				</view>
			</view>
		</view>
		<!-- 历史记录容器 - 带动画效果 -->
		<view class="history-mask" v-if="isShowHistory" @click="hideHistory"></view>
		<view class="history-container" v-if="isShowHistory" :class="{'history-container-active': isShowHistory}">
			<view class="history-header">
				<text class="history-title">历史记录</text>
				<view class="history-close" @click="hideHistory">×</view>
			</view>
			<view class="history-content">
				<view class="history-item" :class="{'history-item-active': meeting_id == item.meeting_id}"
					v-for="(item, index) in meet_list" :key="item.meeting_id" @click="selectHistoryItem(item)">
					<view class="history-item-info">
						<view class="history-item-content">{{ item.name }}</view>
						<!-- 状态0正在生成1完成 -->
						<view class="history-item-status history-item-status-0" v-if="item.state == 0">
							<text class="status-dot status-dot-pending"></text>正在生成
						</view>
						<view class="history-item-status history-item-status-1" v-else-if="item.state == 1">
							<text class="status-dot status-dot-complete"></text>完成
						</view>
					</view>
					<view class="history-item-actions">
						<view class="edit-btn" @click.stop="showEditNameDialog(item)">
							<text class="edit-icon">✎</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				startTime: '',
				isUploading: false, // 添加标记，表示是否有上传任务正在进行中
				isStopAll: true,
				meeting_id: null, // 会议id
				meet_list: [], // 会议列表
				isShowHistory: false, // 是否显示历史记录
				showEditModal: false, // 是否显示修改名称弹窗
				editNameValue: '', // 修改名称的值
				currentEditItem: null // 当前正在编辑的项目
			}
		},
		methods: {
			hideHistory() {
				this.isShowHistory = false;
			},
			// 显示修改名称弹窗
			showEditNameDialog(item) {
				this.currentEditItem = item;
				this.editNameValue = item.name;
				this.showEditModal = true;
			},
			// 取消修改名称
			cancelEdit() {
				this.showEditModal = false;
				this.editNameValue = '';
				this.currentEditItem = null;
			},
			// 确认修改名称
			async confirmEdit() {
				if (!this.editNameValue.trim()) {
					this.$sun.toast('名称不能为空', 'none');
					return;
				}

				// 如果名称没有变化，直接关闭弹窗
				if (this.editNameValue === this.currentEditItem.name) {
					this.cancelEdit();
					return;
				}

				try {
					uni.showLoading({
						title: '修改中...',
						mask: true
					});

					// 调用修改名称接口
					const res = await this.$http.post({
						url: this.$api.updateMeetingName,
						data: {
							meeting_id: this.currentEditItem.meeting_id,
							name: this.editNameValue,
							uid: uni.getStorageSync("uid"),
						}
					});

					if (res.errno === 0) {
						// 修改成功，更新本地数据
						this.currentEditItem.name = this.editNameValue;
						this.$sun.toast('修改成功', 'success');
					} else {
						this.$sun.toast(res.message || '修改失败', 'none');
					}
				} catch (error) {
					console.error('修改名称失败:', error);
					this.$sun.toast('修改失败，请重试', 'none');
				} finally {
					uni.hideLoading();
					this.cancelEdit();
				}
			},
			selectHistoryItem(item) {
				if (item.state == 0) {
					this.$sun.toast('正在生成', 'none');
					return;
				}
				uni.navigateTo({
					url: `/subPackages/subPackageA/report?type=5&meeting_id=${item.meeting_id}`
				})
			},
			stopAll() {
				this.isStopAll = true;
			},
			getStart() {
				this.isStopAll = false;
				// 年月日时分秒
				this.startTime = this.formatTime(new Date().getTime());
				console.log(this.startTime, '===');
			},
			// 格式化时间为年月日时分秒， 传入时间戳
			formatTime(timestamp) {
				const date = new Date(timestamp);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');

				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			},
			//录音完成
			getConfirm(e) {
				this.list.push({
					// 状态：1等待上传 2上传中 3上传完成 4上传失败
					status: 1,
					// 录音文件
					audioUrl: e,
					// 录音文件名
					audioName: this.startTime
				})
				console.log("录音文件==>", e);
				// this.upload(e);
			},
			// 上传单个文件，并更新对应的列表项
			upload(path, index) {
				return new Promise((resolve, reject) => {
					// 更新状态为上传中
					this.list[index].status = 2;

					uni.showLoading({
						title: '上传中...'
					});

					// 调用uni.uploadFile将文件上传至服务器
					uni.uploadFile({
						url: this.$api.upload, // 设置上传接口地址
						filePath: path, // 需要上传的文件路径
						name: 'file', // 后台接收文件时对应的字段名称
						fileType: 'audio', // 指定文件类型
						header: {
							// "Content-Type": "multipart/form-data",
						},
						success: async (response) => {
							console.log("response----->", response);

							// 处理上传成功后的操作
							const data = JSON.parse(response.data);

							if (data.errno != 0) {
								// 上传失败
								this.list[index].status = 4; // 标记为上传失败
								uni.showToast({
									title: '文件上传失败',
									icon: 'none'
								});
								reject(new Error('上传失败'));
							} else {
								// 上传成功
								// 更新列表中的audioUrl为服务器返回的链接
								this.list[index].audioUrl = data.data;
								this.list[index].status = 3; // 标记为上传完成
								await this.uploadMeetingVoice(data.data, index);

								let serverUrl = data.data;
								let serverFileName = serverUrl;

								// 如果返回的是URL，提取文件名
								if (typeof serverUrl === 'string') {
									let index = serverUrl.lastIndexOf('/');
									if (index !== -1) {
										serverFileName = serverUrl.substring(index + 1);
									}
								}



								// 可选：更新显示的文件名
								// this.list[index].audioName = serverFileName;

								resolve(data.data);
							}
						},
						fail: (error) => {
							console.log("error---------->", error);
							this.list[index].status = 4; // 标记为上传失败
							reject(error);
						},
						complete: () => {
							uni.hideLoading();
						}
					});
				});
			},
			// 获取会议列表
			async getMeetingList() {
				let res = await this.$http.post({
					url: this.$api.getMeetingList,
					data: {
						uid: uni.getStorageSync("uid"),
					}
				});
				if (res.errno === 0) {
					this.meet_list = res.data;
					this.isShowHistory = true;
				} else {
					this.$sun.toast(res.message, 'none');
				}
			},
			// 上传会议音频
			async uploadMeetingVoice(url, index) {
				console.log(url, index, '===');

				let res = await this.$http.post({
					url: this.$api.updateMeetVoice,
					data: {
						meeting_id: this.meeting_id,
						url,
						uid: uni.getStorageSync("uid"),
					}
				});
				console.log(res, '===');

				if (res.errno === 0) {
					this.list[index].status = 5;
				} else {
					this.$sun.toast(res.message, 'none');
				}
			},
			// 获取会议id
			async getMeetingId() {
				let res = await this.$http.post({
					url: this.$api.generateMeetingId,
					data: {
						uid: uni.getStorageSync("uid"),
					}
				});
				if (res.errno === 0) {
					this.meeting_id = res.data.meeting_id;
				} else {
					this.$sun.toast(res.message, 'none');
				}
			},
			// 一键上传所有等待上传的录音
			async uploadAll() {
				if (!this.isStopAll) {
					uni.showToast({
						title: '请先停止录音',
						icon: 'none'
					});
					return;
				}
				if (this.list.length === 0) {
					uni.showToast({
						title: '没有录音文件',
						icon: 'none'
					});
					return;
				}
				// 如果已经有上传任务在进行，则不再启动新的上传
				if (this.isUploading) {
					uni.showToast({
						title: '有上传任务正在进行中',
						icon: 'none'
					});
					return;
				}

				if (!this.meeting_id) {
					this.getMeetingId();
				}

				// 找出所有等待上传的录音（状态为1的项目）
				const pendingUploads = this.list.filter(item => item.status === 1);

				if (pendingUploads.length === 0) {
					uni.showToast({
						title: '没有等待上传的录音',
						icon: 'none'
					});
					return;
				}

				uni.showModal({
					title: '批量上传',
					content: `确定要上传${pendingUploads.length}个录音文件吗？`,
					success: (res) => {
						if (res.confirm) {
							// 用户点击确定，开始顺序上传
							this.uploadSequentially();
						}
					}
				});
			},
			// 顺序上传文件
			async uploadSequentially() {
				// 设置上传中状态
				this.isUploading = true;

				try {
					// 找到所有待上传的文件索引
					const pendingIndices = this.list
						.map((item, index) => item.status === 1 ? index : -1)
						.filter(index => index !== -1);

					if (pendingIndices.length === 0) {
						uni.showToast({
							title: '没有待上传的文件',
							icon: 'none'
						});
						return;
					}

					// 显示初始化提示
					uni.showToast({
						title: `准备上传${pendingIndices.length}个文件`,
						icon: 'none'
					});

					let successCount = 0;
					let failCount = 0;

					// 顺序上传每个文件
					for (let i = 0; i < pendingIndices.length; i++) {
						const index = pendingIndices[i];
						const item = this.list[index];

						try {
							// 等待当前文件上传完成
							await this.upload(item.audioUrl, index);
							successCount++;
						} catch (error) {
							console.error(`文件 ${item.audioName} 上传失败:`, error);
							failCount++;
						}
					}

					// 显示上传结果
					uni.showToast({
						title: `上传完成：${successCount}个成功，${failCount}个失败`,
						icon: 'none',
						duration: 2000
					});

					console.log(this.list);


				} catch (error) {
					console.error('上传过程中出错:', error);
				} finally {
					// 无论成功失败，重置上传状态
					this.isUploading = false;
				}
			},
		}
	}
</script>

<style lang="scss">
	.history {
		position: fixed;
		top: 20rpx;
		right: 0;
		padding: 12rpx 24rpx 12rpx 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(90deg, rgba(68, 65, 253, 0.85), rgba(105, 229, 253, 0.85));
		border-radius: 30rpx 0 0 30rpx;
		color: #fff;
		font-size: 28rpx;
		z-index: 10;
		box-shadow: 0 4rpx 15rpx rgba(68, 65, 253, 0.3),
			inset 0 1rpx 3rpx rgba(255, 255, 255, 0.4);
		transition: all 0.3s ease;
		border: 1rpx solid rgba(255, 255, 255, 0.15);
		backdrop-filter: blur(5rpx);

		&:active {
			transform: translateX(-5rpx) scale(0.98);
			box-shadow: 0 2rpx 8rpx rgba(68, 65, 253, 0.2),
				inset 0 1rpx 2rpx rgba(255, 255, 255, 0.3);
		}
	}

	.history-item-info {
		flex: 1;
		overflow: hidden;
	}

	.history-item-status {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		margin-top: 8rpx;

		.status-dot {
			width: 8rpx;
			height: 8rpx;
			border-radius: 50%;
			margin-right: 8rpx;
		}

		.status-dot-pending {
			background-color: #ffcc00;
			box-shadow: 0 0 5rpx rgba(255, 204, 0, 0.5);
		}

		.status-dot-complete {
			background-color: #00ff00;
			box-shadow: 0 0 5rpx rgba(0, 255, 0, 0.5);
		}

		&.history-item-status-0 {
			color: #ffcc00;
		}

		&.history-item-status-1 {
			color: #00ff00;
		}
	}

	.history-item-actions {
		display: flex;
		align-items: center;
		margin-left: 15rpx;

		.edit-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgba(255, 255, 255, 0.1);
			border-radius: 50%;
			transition: all 0.3s ease;

			&:active {
				background-color: rgba(255, 255, 255, 0.2);
				transform: scale(0.9);
			}

			.edit-icon {
				font-size: 32rpx;
				color: #4441fd;
				text-shadow: 0 0 5rpx rgba(68, 65, 253, 0.3);
			}
		}
	}

	.history-icon {
		margin-right: 10rpx;
		font-size: 36rpx;
		font-weight: bold;
		text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
	}

	.history-text {
		font-weight: 500;
		letter-spacing: 1rpx;
	}

	.history-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 98;
	}

	.history-container {
		position: fixed;
		top: 0;
		left: 0;
		height: 100vh;
		width: 70%;
		background-color: #192236;
		z-index: 99;
		transform: translateX(-100%);
		transition: transform 0.3s ease;
		display: flex;
		flex-direction: column;
		box-shadow: 2rpx 0 10rpx rgba(0, 0, 0, 0.3);
	}

	.history-item-active {
		background-color: rgba(68, 65, 253, 0.15);
		border: 1rpx solid rgba(68, 65, 253, 0.4);
		box-shadow: 0 4rpx 15rpx rgba(68, 65, 253, 0.2);
	}

	.history-container-active {
		transform: translateX(0);
	}

	.history-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
	}

	.history-title {
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
	}

	.history-close {
		color: #fff;
		font-size: 40rpx;
		font-weight: bold;
		padding: 0 10rpx;
	}

	.history-content {
		flex: 1;
		overflow-y: auto;
		padding: 10rpx 0;
	}

	.history-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 24rpx;
		margin: 15rpx 20rpx;
		border-radius: 16rpx;
		background-color: rgba(255, 255, 255, 0.05);
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
		cursor: pointer;
		transition: all 0.3s ease;
		border: 1rpx solid rgba(255, 255, 255, 0.08);
	}

	.history-item:active {
		transform: scale(0.98);
		background-color: rgba(255, 255, 255, 0.08);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.history-item-content {
		color: #fff;
		font-size: 28rpx;
		font-weight: 500;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-bottom: 6rpx;
	}

	/* 修改名称弹窗样式 */
	.edit-name-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.edit-name-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		backdrop-filter: blur(5rpx);
	}

	.edit-name-content {
		position: relative;
		width: 80%;
		background-color: #1a1c22;
		border-radius: 20rpx;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
		overflow: hidden;
		border: 1rpx solid rgba(255, 255, 255, 0.1);
		animation: modalFadeIn 0.3s ease;
	}

	@keyframes modalFadeIn {
		from {
			opacity: 0;
			transform: scale(0.9);
		}

		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	.edit-name-header {
		padding: 30rpx;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
		text-align: center;
	}

	.edit-name-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #ffffff;
	}

	.edit-name-body {
		padding: 30rpx;
	}

	.edit-name-input {
		width: 100%;
		height: 80rpx;
		background-color: rgba(255, 255, 255, 0.1);
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #ffffff;
		box-sizing: border-box;
		border: 1rpx solid rgba(255, 255, 255, 0.2);
	}

	.edit-name-footer {
		display: flex;
		border-top: 1rpx solid rgba(255, 255, 255, 0.1);
	}

	.edit-name-btn {
		flex: 1;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		font-weight: 500;
		transition: all 0.3s ease;
	}

	.cancel-btn {
		color: #999999;
		border-right: 1rpx solid rgba(255, 255, 255, 0.1);

		&:active {
			background-color: rgba(255, 255, 255, 0.05);
		}
	}

	.confirm-btn {
		color: #4441fd;

		&:active {
			background-color: rgba(68, 65, 253, 0.1);
		}
	}

	page {
		border-top: none;
		background-color: #111317;
	}

	.main {
		width: 100%;
		height: 100%;
		padding-bottom: 160rpx;
		/* 为底部录音控件留出空间 */
		box-sizing: border-box;

		.title-container {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 20rpx;
			border-bottom: 2rpx solid #222428;
			margin-bottom: 20rpx;
			letter-spacing: 2rpx;

			.title-text {
				font-size: 36rpx;
				font-weight: bold;
				color: #ffffff;
			}

			.upload-btn {
				padding: 8rpx 16rpx;
				border-radius: 30rpx;
				font-size: 24rpx;
				font-weight: 500;
				color: #ffffff;
				background-color: rgba(255, 255, 255, 0.1);
				border: 1rpx solid rgba(255, 255, 255, 0.3);
			}
		}

		.list {
			padding: 0 20rpx;
			max-height: calc(100vh - 300rpx);
			/* 减去标题和底部控件的高度 */
			overflow-y: auto;
			padding-bottom: 300rpx;

			/* 自定义滚动条样式 */
			&::-webkit-scrollbar {
				width: 6rpx;
			}

			&::-webkit-scrollbar-track {
				background: rgba(255, 255, 255, 0.05);
				border-radius: 10rpx;
			}

			&::-webkit-scrollbar-thumb {
				background: rgba(255, 255, 255, 0.2);
				border-radius: 10rpx;
			}

			.item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 24rpx 20rpx;
				margin-bottom: 16rpx;
				border-radius: 12rpx;
				background-color: #1a1c22;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
					box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.1);
				}

				.item-left {
					font-size: 28rpx;

					.item-left-time {
						color: #ffffff;
						/* 文件名称显示为白色 */
					}
				}

				.item-right {
					.item-right-status {
						.item-right-status-icon {
							padding: 8rpx 16rpx;
							border-radius: 30rpx;
							font-size: 24rpx;
							font-weight: 500;

							&.upload-success {
								color: #00ff00;
								background-color: rgba(0, 255, 0, 0.1);
								border: 1rpx solid rgba(0, 255, 0, 0.3);
								/* 上传完成显示为绿色 */
							}

							&.upload {
								color: #00ff00;
								background-color: rgba(0, 255, 0, 0.1);
								border: 1rpx solid rgba(0, 255, 0, 0.3);
								/* 上传完成显示为绿色 */
							}

							&.uploading {
								color: #999999;
								background-color: rgba(153, 153, 153, 0.1);
								border: 1rpx solid rgba(153, 153, 153, 0.3);
								/* 等待上传显示为灰色 */
							}

							&.uploading-now {
								color: #ffcc00;
								background-color: rgba(255, 204, 0, 0.1);
								border: 1rpx solid rgba(255, 204, 0, 0.3);
								/* 上传中显示为黄色 */
							}

							&.upload-failed {
								color: #ff0000;
								background-color: rgba(255, 0, 0, 0.1);
								border: 1rpx solid rgba(255, 0, 0, 0.3);
								/* 上传失败显示为红色 */
							}
						}
					}
				}
			}
		}

		.voice {
			position: fixed;
			bottom: 0;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #111317;
			padding: 20rpx 0;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.3);
			z-index: 10;
		}
	}
</style>