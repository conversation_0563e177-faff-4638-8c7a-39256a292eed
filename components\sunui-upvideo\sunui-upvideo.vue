<template>
	<view class="sunui-add-list">
		<view :class="itemsColor" v-for="(item, index) in imgLists" :key="index">
			
			<video :class="itemsImgColor" :id="'myVideo_' + index" :src="item.url" @play="videoPlay" @error="videoError"
				:controls="true" :enable-play-gesture="true"></video>

			<view class="sunui-add-list-remove sunui-icons icon-close" @tap.stop="removeImg"
				:id="'sunui-items-img-' + index">
				<icon type="clear" :color="closeBtnColor"></icon>
			</view>
			<view class="upload-progress" v-if="updatting"><progress :percent="item.progress" :stroke-width="progressSize"
					:activeColor="progressColor" :backgroundColor="progressBGColor"  /></view>
			<view class="sunui-add-list-reup" :class="listReup" @tap.stop="retry" :data-index="index" v-if="item.error">
				<text class="sunui-add-list-reup-icon sunui-icons icon-retry"></text>
				<text class="sunui-add-list-reup-text">失败重试</text>
			</view>
		</view>
		<view :class="itemsColor" class="sunui-add-list-btn" @tap="addImg" v-if="imgLists.length < maxFileNumber">
			<image :class="bgImgColor" :src="bgImg" ></image>
			<view class="sunui-add-list-btn-text">{{ btnName }}</view>
			<!-- <view class="sunui-add-list-btn-text margin-bottom_10rpx">上传视频,支持以下尺寸</view>
			<view class="sunui-add-list-btn-text margin-bottom_10rpx">3:4(1440*1080)和4:3(1080*1440)</view>
			<view class="sunui-add-list-btn-text">16:9(1920*1080)和9:16(1080*1920)和1:1(800以上)</view> -->
		</view>
		<!-- 上传视频(支持16:9(1920*1080),9:16(1080*1920),3:4(1440*1080),4:3(1080*1440),1:1(800以上)) -->
	</view>
</template>
<script>
	export default {
		props: {
			maxFileNumber: {
				type: Number,
				default: 9
			},
			itemsImgColor: {
				type: String,
				default: 'sunui-add-list-items-img'
			},
			itemsColor: {
				type: String,
				default: 'sunui-add-list-items'
			},
			bgImg: {
				type: String,
				default: ''
			},
			bgImgColor: {
				type: String,
				default: 'sunui-add-list-btn-icon'
			},
			listReup: {
				type: String,
				default: 'sunui-add-list-reup-1'
			},
			btnName: {
				type: String,
				default: '上传视频'
			},
			// 上传前钩子
			beforeUpload: {
				type: Function
			},
			items: {
				type: Array,
				default: function() {
					return [];
				}
			},
			closeBtnColor: {
				type: String,
				default: '#666666'
			},
			uploadServerUrl: {
				type: String,
				default: ''
			},
			progressSize: {
				type: Number,
				default: 1
			},
			progressColor: {
				type: String,
				default: '#27BD81'
			},
			progressBGColor: {
				type: String,
				default: '#F8F8F8'
			},
			fileName: {
				type: String,
				default: 'img'
			},
			formData: {
				type: Object,
				default: function() {
					return {};
				}
			},
			imgMode: {
				type: String,
				default: 'widthFix'
			},
			header: {
				type: Object,
				default: function() {
					return {};
				}
			}
		},
		data() {
			return {
				imgLists: [],
				videoContextList: [],
				updatting: false,
				
			};
		},
		watch: {
			imgLists(newVal, oldVal) {
				if (!this.updatting) {
					this.$emit('change', newVal);
				}
			}
		},
		methods: {
			videoPlay(e) {
				let id = e.target.id;
				let index = id.replace(/[^0-9]/g, '');
				for (let i = 0; i < this.imgLists.length; i++) {
					if (id == this.videoContextList[i].id) {
						this.videoContextList[i].play();
						// this.videoContextList[i].requestFullScreen();
					} else {
						this.videoContextList[i].pause();
					}
				}
			},
			videoError(e) {
				uni.showModal({
					content: '网络错误，请稍后重试！',
					showCancel: false
				});
			},
			clearAllImgs() {
				this.imgLists = [];
			},
			addImg() {
				var num = this.maxFileNumber - this.imgLists.length;
				if (num < 1) {
					return false;
				}
				// uni.showLoading({title:""});
				uni.chooseVideo({
					count: num,
					compressed: false,
					sourceType: ['album'],
					success: async res => {
						let file = res.tempFilePath;
						let suffix = 'mp4';
						if (res.tempFilePath) {
							suffix = res.tempFilePath.split(".");
							// console.log("suffix--->",suffix[suffix.length-1]);
						}else {
							this.$sun.toast("视频资源异常,请重新选择!",'none');
							return;
						}
						if (this.beforeUpload) {
							const valid = await this.beforeUpload(res, 1);
							if (valid === false) {
								return false;
							}
						}
						this.imgLists.push({
							url: file,
							progress: 0,
							error: false,
							suffix: suffix[suffix.length-1]
						});
						this.videoContextList.push(uni.createVideoContext('myVideo_' + this.imgLists.length,
							this));
						//uni.hideLoading();
					},
					complete: function() {
						//uni.hideLoading();
					},
					fail: function(err) {
						console.log("err---->",err);
						//uni.hideLoading();
					}
				});
			},
			removeImg(e) {
				var index = e.currentTarget.id.replace('sunui-items-img-', '');
				var removeImg = this.imgLists.splice(index, 1);
				var removeContent = this.videoContextList.splice(index, 1);
				this.$emit('removeImg', removeImg[0]);
			},
			showImgs(e) {
				var currentImg = e.currentTarget.dataset.imgurl;
				var imgs = [];
				for (let i = 0; i < this.imgLists.length; i++) {
					imgs.push(this.imgLists[i].url);
				}
				uni.previewImage({
					urls: imgs,
					current: currentImg
				});
			},
			upload(index) {
				if (this.updatting) {
					return;
				}
				this.updatting = true;
				if (!index) {
					index = 0;
				}
				uni.showLoading({
					title: '视频上传中',
					mask: true
				});

				this.uploadBase(index);
			},
			retry(e) {
				var index = e.currentTarget.dataset.index;
				this.upload(index);
			},
			uploadBase(index) {
				// 全部上传完成
				if (index > this.imgLists.length - 1) {
					uni.hideLoading();
					this.updatting = false;
					this.$emit('uploaded', this.imgLists);
					return;
				}
				// 验证后端
				if (this.uploadServerUrl == '') {
					uni.showToast({
						title: '请配置阿里云',
						icon: 'none'
					});
					return;
				}
				// 检查是否是默认值
				if (this.imgLists[index].progress >= 1) {
					this.uploadBase(index + 1);
					return;
				}
				this.imgLists[index].error = false;
				
				this.formData.key = new Date().getTime() + Math.floor(Math.random() * 150)+'.'+this.imgLists[index].suffix;
				
				console.log("formData.key---->",this.formData.key);
				
				// 创建上传对象
				const task = uni.uploadFile({
					url: this.uploadServerUrl,
					filePath: this.imgLists[index].url,
					fileType: 'video/mp4',
					name: 'file' || this.fileName,
					formData: this.formData,
					header: this.header,
					success: uploadRes => {
						
						// uploadRes = JSON.parse(uploadRes.data);
						
						console.log('uploadRes', uploadRes);

						if (uploadRes.statusCode != 200) {
							uni.showToast({
								title: '上传失败 : ' + uploadRes.data,
								icon: 'none'
							});
							this.error(index);
						} else {
							//上传图片成功
							// this.imgLists[index].url = uploadRes.data;
							// this.imgLists[index].result = uploadRes;
							this.imgLists[index].progress = 100;
							this.imgLists[index].url = this.uploadServerUrl+'/'+this.formData.key;
							this.imgLists[index].result = uploadRes;
							this.uploadBase(index + 1);
						}
					},
					fail: e => {
						uni.showToast({
							title: '上传失败，请点击重试',
							icon: 'none'
						});
						this.error(index);
					}
				});
				task.onProgressUpdate(res => {
					// console.log("------进度条",res);
					if (res.progress > 0) {
						this.imgLists[index].progress = res.progress;
						this.imgLists.splice(index, 1, this.imgLists[index]);
					}
				});
			},
			// 上传错误
			error(index) {
				this.updatting = false;
				setTimeout(() => {
					this.imgLists[index].progress = 0;
					this.imgLists[index].error = true;
					this.$emit('uploaderror');
				}, 500);
			},
			// 设置默认值
			setItems(items) {
				this.imgLists = [];
				for (let i = 0; i < items.length; i++) {
					this.imgLists.push({
						url: items[i],
						progress: 100
					});
				}
			}
		}
	};
</script>
<style scoped>
	.sunui-add-list {
		display: flex;
		flex-wrap: wrap;
	}

	.sunui-add-list-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.sunui-add-list-btn-text {
		font-size: 26rpx;
		line-height: 36rpx;
		text-align: center;
		color: #999999;
		width: 100%;
		padding: 0 20rpx;
		word-break: break-all;
	}

	.sunui-add-list-btn-icon {
		width: 68rpx;
		height: 68rpx;
	}
	
	.sunui-add-list-items-img {
		width: 686rpx;
		height: 400rpx;
		overflow: hidden;
		position: relative;
		border-radius: 10rpx;
	}
	
	.sunui-add-list-items {
		width: 686rpx;
		height: 400rpx;
		overflow: hidden;
		border: 1px dashed rgb(87, 86, 86);
		position: relative;
		border-radius: 10rpx;
	}

	.sunui-add-list-image {
		width: 222rpx;
	}

	.sunui-add-list-remove {
		position: absolute;
		z-index: 15;
		right: 6rpx;
		top: 6rpx;
		color: #888888;
	}

	.upload-progress {
		position: absolute;
		z-index: 2;
		left: 0;
		bottom: 10rpx;
		width: 666rpx;
		padding: 0 20rpx;
	}

	.sunui-add-list-reup {
		position: absolute;
		z-index: 3;
		left: 0;
		top: 0rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.3);
		flex-direction: column;
	}
	
	.sunui-add-list-reup-1 {
		width: 222rpx;
		height: 222rpx;
	}
	
	.sunui-add-list-reup-2 {
		width: 686rpx;
		height: 400rpx;
	}
	

	.sunui-add-list-reup-icon {
		text-align: center;
		width: 100%;
		color: #ffffff;
		display: block;
		font-size: 80rpx;
		line-height: 100rpx;
	}

	.sunui-add-list-reup-text {
		text-align: center;
		width: 100%;
		color: #ffffff;
		display: block;
		font-size: 20rpx;
		line-height: 30rpx;
	}

	.sunui-add-list-img {
		width: 222rpx;
	}
</style>