<!-- 
* <sunui-grand height="300px" :showShink="false">
			<view>
				默认0(一个页面可能存在多个计数器,依靠index识别),也可以传id等标识信息默认0(一个页面可能存在多个计数器,依靠index识别),也可以传id等标识信息默认0(一个页面可能存在多个计数器,依靠index识别),也可以传id等标识信息默认0(一个页面可能存在多个计数器,依靠index识别),也可以传id等标识信息默认0(一个页面可能存在多个计数器,依靠index识别),也可以传id等标识信息默认0(一个页面可能存在多个计数器,依靠index识别),也可以传id等标识信息默认0(一个页面可能存在多个计数器,依靠index识别),也可以传id等标识信息默认0(一个页面可能存在多个计数器,依靠index识别),也可以传id等标识信息
			</view>
		</sunui-grand>
* 
*  -->

<template name="sunui-grand">
	<view class="sunui-grand" :style="{height:pageHeight}">
		<slot></slot>
		<view :hidden="!articleBtn" class="btn" @tap="grandExpend" :style="{color:color,border:border,textAlign:textAlign}">{{expandText}}<text
			 class="iconfont icon-zhankai" :style="{color:color}"></text></view>
		<view v-if="!articleBtn && showShink" :style="{color:color,textAlign:textAlign}" @tap="grandShrink">{{shinkText}}<text
			 class="iconfont icon-shouqi" :style="{color:color}"></text></view>
	</view>
</template>
<script>
	export default {
		name: 'sunui-grand',
		props: {
			textAlign: {
				type: String,
				default: 'center'
			},
			border: {
				type: String,
				default: '1rpx solid #eee'
			},
			height: {
				type: String,
				default: "300rpx"
			},
			color: {
				type: String,
				default: '#000'
			},
			showShink: {
				type: Boolean,
				default: false
			},
			expandText: {
				type: String,
				default: "展开阅读全文"
			},
			shinkText: {
				type: String,
				default: "点击收起全文"
			}
		},
		data() {
			return {
				pageHeight: "",
				articleBtn: true,
				shickWidth:'100%'
			}
		},
		created() {
			this.pageHeight = this.height;
		},
		methods: {
			grandExpend() {
				this.pageHeight = 'auto';
				this.articleBtn = false;
			},
			grandShrink() {
				this.pageHeight = this.height;
				this.articleBtn = true;
			}
		},
	}
</script>
<style lang="scss">
	@import url('iconfont');

	.sunui-grand {
		overflow: hidden;
		position: relative;
	}

	.sunui-grand>.btn {
		width: 100%;
		height: 35px;
		line-height: 35px;
		background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, #fff 80%);
		background-color: rgba(255, 255, 255, .5);
		// padding-top: 30upx;
		// padding-bottom: 52upx;
		position: absolute;
		z-index: 99;
		left: 0;
		bottom: 0;
		text-align: center;
	}
</style>
