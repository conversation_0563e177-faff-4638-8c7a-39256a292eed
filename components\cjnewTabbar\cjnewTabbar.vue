<template>

		<cover-view class="tabbar" :items="list" :style="{ 'padding-bottom': paddingBottomHeight + 'rpx' }"
			style="z-index: 9;">
			<cover-view class="tabbar-item" v-for="(item, index) in dibuNvaList" :key="index" @click="tabbarChange(item.url)">
				<cover-image class="item-img" :src="item.icon_checked" v-if="current == index"></cover-image>
				<cover-image class="item-img" :src="item.icon" v-else></cover-image>
				<cover-view class="item-name" :class="current == index ? className : ''"   v-if="item.name">
					{{ item.name }}
				</cover-view>
			</cover-view>
		</cover-view>
</template>

<script>

	export default {
		// 接收父组件穿过来的list数组和current下标

		props: ['current','dibuNvaList','className'],

		data() {
			return {
				paddingBottomHeight: 0 ,//苹果X以上手机底部适配高度
				cpsUrl:"",
				cpsType:1
			};
		},

		created() {
			let that = this;
			uni.getSystemInfo({
				success: function(res) {
					let model = ['X', 'XR', 'XS', '11', '12', '13', '14', '15'];
					model.forEach(item => {
						//适配iphoneX以上的底部，给tabbar一定高度的padding-bottom
						if (res.model.indexOf(item) != -1 && res.model.indexOf('iPhone') != -1) {
							that.paddingBottomHeight = 20;
						}
					});
				}
			});
		},
		watch: {},

		onLoad() {
			
		},
		onShow() {
			if(this.cpsType==1){
				// console.log(this.listss[2].path,'===============================================================================================')
				this.cpsUrl = '/wjyk_zqds/pages/shang/shang'
			}
		},
		methods: {
			tabbarChange(path) {
				// console.log(path);
				uni.redirectTo({
					url: path
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.color81A1F7 {
		color: #81A1F7  !important;
	}
	
	.color000000 {
		color: #000000  !important;
	}

	.tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-around;
		align-items: center;
		width: 100%;
		height: 110rpx;
		border-top: 1px solid #C4C4C4;
		background-color: #ffffff;
		.tabbar-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 100rpx;

			.item-img {
				margin-bottom: 4rpx;
				width: 48rpx;
				height: 48rpx;
			}

			.item-name {
				font-size: 26rpx;
				color: #666;
			}
		}
	}
</style>
