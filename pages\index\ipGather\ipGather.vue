<template>
	<view>
		
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'405.png'+')'}">
		
			<view :style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_30rpx" @click="navig()">
					账户主页地址
				</view>
			</view>
			
			<view class="list-public">
				<textarea v-model="accountUrl" placeholder="复制短视频D音主页账号地址链接" :cursor-spacing="50" maxlength="-1" placeholder-class="#BDBDBD"></textarea>
			</view>
			
			<view class="display-a padding_20rpx">
				<block v-if="systems.link_require">
					<view class="se-line"></view>
					<view class="color_FFFFFF font-size_32rpx">链接要求</view>
				</block>
				<image @click="getList()" class="img-419" :src="imgUrl+'419.png'"></image>
				<view @click="getList()" style="color: rgba(32, 255, 134, 1)">任务列表</view>
			</view>
			<view class="padding_0_20rpx color_FFFFFF" v-if="systems.link_require">
				<rich-parser :html="systems.link_require" domain="https://6874-html-foe72-**********.tcb.qcloud.la" lazy-load
					ref="article" selectable show-with-animation use-anchor>
					<!-- 加载中... -->
				</rich-parser>
			</view>
			
			<view style="height: 160rpx;"></view>
			
		</view>
		
		<view class="pos-bott">
			
			<view class="but-next" @click="getHomepageCount()">
				解析IP作品
			</view>
			
		</view>
		
		<sunui-popup ref="pop4">
			<template v-slot:content>
				<view class="pop-bg">
					<image @click="closeRequirement()" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title">抓取配置</view>
					<view class="display-a pop-top" @click="getMember()">
						<view class="pop-balance">剩余点数: {{user.balance}}</view>
						<view class="margin-left-auto color_C9CACA">去充值</view>
						<image class="img-21" :src="imgUrl+'21.png'"></image>
					</view>
					<view class="pop-name">任务标题</view>
					<view class="pop-top">
						<input  type="text" v-model="name" placeholder="请输入任务标题" placeholder-class="#BDBDBD" />
					</view>
					
					<view class="display-a margin-bottom_20rpx">
						<view class="font-size_30rpx">作品数量({{accountNum}}条)</view>
						<image @click="getType(1)" class="img-280 margin-left-auto" :src="type == 1 ? imgUrl+'366.png' : imgUrl+'374.png'"></image>
						<view @click="getType(1)" class="font-size_30rpx margin-right_40rpx">全部</view>
						<image @click="getType(2)" class="img-280" :src="type == 2 ? imgUrl+'366.png' : imgUrl+'374.png'"></image>
						<view @click="getType(2)" class="font-size_30rpx">最新</view>
					</view>
					<view class="pop-top" v-if="type == 2">
						<input type="number" @input="inputChange" v-model="setNum" placeholder="请输入作品数量" placeholder-class="#BDBDBD" />
					</view>
					
					
					<view class="but-next" style="margin-top: 50rpx;" @click="save()">一键分析: ({{tallyTotal}}点数)</view>
					
				</view>
			</template>
		</sunui-popup>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				heightSystemss: '',
				statusBarHeightss: '',
				windowHeight: '',
				
				accountUrl: '', //账户主页链接
				accountNum: '', //账号作品数量
				
				type: '1', //1全部  2最新
				
				name: '', //标题
				setNum: '', //作品数量
				
				tallyTotal: 0, //扣点总数
				
				
				user: {}, //用户信息
				
				isWhether: true, //判断重复点击
				
				systems: uni.getStorageSync("system"), //系统设置
				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置
				
				
			}
		},
		
		onLoad() {
			this.getSystemInfo();
		},
		
		onShow() {
			this.userInfo();
		},
		
		methods: {
			
			// 输入事件
			inputChange(e) {
				
				if (this.accountNum < e.detail.value) {
					this.setNum = this.accountNum;
				}else {
					this.setNum = e.detail.value;
				}
				this.getTotal();
			},
			
			//任务列表
			getList() {
				
				uni.navigateTo({
					url: '/pages/index/ipGather/list'
				})
				
			},
			
			async save() {
				
				if (!this.accountUrl) {
					this.$sun.toast("请先输入D音主页账号地址链接", 'none');
					return;
				}
				
				if (!this.name) {
					this.$sun.toast("请先输入任务标题", 'none');
					return;
				}
				
				if (this.type == 2) {
					if (!this.setNum) {
						this.$sun.toast("请先输入采集作品数量", 'none');
						return;
					}
					if (this.setNum > this.accountNum) {
						this.$sun.toast("请正确输入采集作品数量", 'none');
						return;
					}
				}
							
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				
				const result = await this.$http.post({
					url: this.$api.saveHomepage,
					data: {
						share_text: this.accountUrl,
						uid: uni.getStorageSync('uid'),
						name: this.name,
						gather_count: this.setNum
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.getList();
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
				
			},
			
			getMember() {
				uni.navigateTo({
					url: '/pages/my/member'
				})
			},
			
			getType(type) {
				this.type = type;
			},
			
			//总扣点数
			getTotal() {
				
				let douyin_homepage = Number(this.tallySetObj.douyin_homepage); 
				
				if (douyin_homepage) {
					this.tallyTotal = douyin_homepage*this.setNum;
				}else {
					this.tallyTotal = 0;
				}
				
			},
			
			//获取账号下作品数量
			async getHomepageCount() {
				
				if (!this.accountUrl) {
					this.$sun.toast("请先输入D音主页账号地址链接",'none');
					return;
				}
				
				if (uni.getStorageSync('uid')) {
				
					const result = await this.$http.post({
						url: this.$api.HomepageCount,
						data: {
							share_text: this.accountUrl
						}
					});
					if (result.errno == 0) {
						this.accountNum = result.data.aweme_count;
						this.setNum = this.accountNum;
						if (this.accountNum && this.accountNum > 0) {
							this.getTotal();
							this.openRequirement();
						}else {
							this.$sun.toast("该账号没有作品数据!", 'none');
						}
					} else {
						this.$sun.toast(result.message, 'none');
					}
				
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								this.navig();
							}
						}
					})
				}
			},
			
			//用户信息
			async userInfo() {
				const result = await this.$http.post({
					url: this.$api.userInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.user = result.data;
				}
			},
			
			//
			openRequirement() {
				this.$refs.pop4.show({
					style: 'background-color:#000;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeRequirement() {
				this.$refs.pop4.close();
			},
			
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表
			
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			
			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
						this.windowHeight = res.windowHeight * 2;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},
			
		}
	}
</script>

<style lang="scss">
	
	.pop-name {
		font-size: 30rpx;
		margin-bottom: 20rpx;
	}
	
	.pop-balance {
		font-size: 30rpx;
		color: #ff0000;
		font-weight: 600;
	}
	
	.pop-top {
		width: 710rpx;
		border-radius: 10rpx;
		background: rgba(35, 35, 35, 1);
		padding: 24rpx 30rpx;
		margin-bottom: 30rpx;
	}
	
	.img-280 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 14rpx;
	}
	
	.img-21 {
		width: 26rpx;
		height: 26rpx;
		margin-left: 6rpx;
	}
	
	.p-title {
		color: #FFF;
		font-size: 36rpx;
		font-weight: 600;
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 42rpx;
		right: 30rpx;
	}
	
	.pop-bg {
		position: relative;
		padding: 34rpx 20rpx;
		color: #FFF;
	}
	
	.img-419 {
		width: 36rpx;
		height: 36rpx;
		margin-left: auto;
		margin-right: 8rpx;
	}
	
	.se-line {
		background: rgba(32, 255, 134, 1);
		width: 8rpx;
		height: 28rpx;
		border-radius: 4rpx;
		margin-right: 8rpx;
	}
	
	.but-next {
		width: 710rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgba(89, 238, 82, 1),rgba(130, 255, 242, 1) 100%);
		padding: 30rpx 0;
		font-size: 32rpx;
		font-weight: 600;
		color: #000;
	}
	
	.pos-bott {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		padding: 20rpx 20rpx 30rpx;
		z-index: 99;
		background: rgba(48, 48, 48, 1);
	}
	
	.list-public {
		background: rgba(17, 17, 17, 1);
		padding: 30rpx 20rpx;
	}
	
	textarea {
		width: 670rpx;
		height: 560rpx;
		color: #FFF;
	}
	
	
	.bg {
		width: 750rpx;
		height: 580rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 200rpx 0 0;
	}
	
	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}
	
	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		display: flex;
		align-items: center;
	}
	
	page {
		border: none;
		background: rgba(48, 48, 48, 1);
		width: 100%;
		overflow-x: hidden !important;
	}
	
</style>
