/**
 * 将Uint8Array或ArrayBuffer转换为字符串
 * @param {uint8Array|ArrayBuffer} data - Uint8Array或ArrayBuffer
 * @returns {string} - 转换后的普通字符串
 */
export const decodedString = (data) => {
	let txt;
	// 进行判断返回的对象是Uint8Array（开发者工具）或者ArrayBuffer（真机）
	const type = Object.prototype.toString.call(data);
	if (type === "[object Uint8Array]") {
		txt = decodeURIComponent(escape(String.fromCharCode(...data)));
	} else if (data instanceof ArrayBuffer) {
		// 将ArrayBuffer转换为Uint8Array
		const arr = new Uint8Array(data);
		txt = decodeURIComponent(escape(String.fromCharCode(...arr)));
	}
	return txt;
}

// 将Markdown转换为纯文本
export const markdownToText = (markdown) => {
	if (!markdown) return '';

	// 处理标题: # 标题 -> 标题
	let text = markdown.replace(/^#{1,6}\s+(.+)$/gm, '$1');

	// 处理加粗: **文本** -> 文本
	text = text.replace(/\*\*(.*?)\*\*/g, '$1');

	// 处理斜体: *文本* -> 文本
	text = text.replace(/\*(.*?)\*/g, '$1');

	// 处理列表: - 项目 -> 项目, 1. 项目 -> 项目
	text = text.replace(/^[\-\*]\s+(.+)$/gm, '$1');
	text = text.replace(/^\d+\.\s+(.+)$/gm, '$1');

	// 处理链接: [文本](链接) -> 文本
	text = text.replace(/\[(.*?)\]\(.*?\)/g, '$1');

	// 处理图片: ![alt](url) -> alt
	text = text.replace(/!\[(.*?)\]\(.*?\)/g, '$1');

	// 处理代码块: ```code``` -> code
	text = text.replace(/```([\s\S]*?)```/g, '$1');

	// 处理行内代码: `code` -> code
	text = text.replace(/`(.*?)`/g, '$1');

	// 处理引用: > 文本 -> 文本
	text = text.replace(/^>\s+(.+)$/gm, '$1');

	// 处理水平线: --- -> [空行]
	text = text.replace(/^[\-=_]{3,}$/gm, '');

	// 保留段落间的换行
	text = text.replace(/\n{2,}/g, '\n\n');

	return text;
}