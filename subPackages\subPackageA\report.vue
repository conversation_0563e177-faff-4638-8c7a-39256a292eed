<template>
	<view class="r-top">
		<view class="step-indicator" v-if="optype === 1">
			<view v-for="(step, index) in steps" :key="index" class="step-item" @click="goToStep(index)"
				:class="{ 'active': currentStep === index }">
				<view class="step-circle">{{ index + 1 }}</view>
				<view class="step-title">{{ step.title }}</view>
			</view>
		</view>
		<view v-if="currentStep === 0">

			<view class="display-a margin-bottom_40rpx">
				<view>
					<view class="display-a margin-bottom_20rpx">
						<view class="r-line"></view>
						<view class="color_FFFFFF font-size_30rpx">姓名</view>
						<view class="red-dot">*</view>
					</view>
					<input type="text" v-model="name" maxlength="20" class="r-input" style="width: 300rpx;"
						placeholder="请输入姓名" placeholder-class="placeholder" />
				</view>
				<view class="margin-left-auto">
					<view class="display-a margin-bottom_20rpx">
						<view class="r-line"></view>
						<view class="color_FFFFFF font-size_30rpx">年龄</view>
						<view class="red-dot">*</view>
					</view>
					<input type="number" v-model="age" maxlength="3" class="r-input" style="width: 300rpx;"
						placeholder="请输入年龄" placeholder-class="placeholder" />
				</view>
			</view>

			<view class="margin-bottom_40rpx">
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">性别</view>
					<view class="red-dot">*</view>
				</view>
				<view class="display-a">
					<view @click="setSex('男')" class="font-size-1"
						:class="gender == '男' ? 'font-size-3' : 'font-size-2'">男
					</view>
					<view @click="setSex('女')" class="font-size-1"
						:class="gender == '女' ? 'font-size-3' : 'font-size-2'">女
					</view>
				</view>
			</view>

			<view class="display-a margin-bottom_40rpx">
				<view>
					<view class="display-a margin-bottom_20rpx">
						<view class="r-line"></view>
						<view class="color_FFFFFF font-size_30rpx">城市</view>
						<view class="red-dot">*</view>
					</view>
					<input type="text" v-model="site" maxlength="200" class="r-input" style="width: 300rpx;"
						placeholder="江苏省苏州市" placeholder-class="placeholder" />
				</view>
				<view class="margin-left-auto">
					<view class="display-a margin-bottom_20rpx">
						<view class="r-line"></view>
						<view class="color_FFFFFF font-size_30rpx">从业年限</view>
						<view class="red-dot">*</view>
					</view>
					<input type="text" v-model="experience" maxlength="120" class="r-input" style="width: 300rpx;"
						placeholder="10年行业经验" placeholder-class="placeholder" />
				</view>
			</view>
			<block>
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">行业</view>
					<view class="red-dot">*</view>
				</view>
				<input type="text" v-model="industry" maxlength="200" class="r-input margin-bottom_40rpx"
					placeholder="家居建材" placeholder-class="placeholder" />
			</block>
			<block>
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">企业名称</view>
					<view class="red-dot">*</view>
				</view>
				<input type="text" v-model="company_name" maxlength="60" class="r-input margin-bottom_40rpx"
					placeholder="请输入" placeholder-class="placeholder" />
			</block>
			<block>
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">主营产品</view>
					<view class="red-dot">*</view>
				</view>
				<textarea type="text" v-model="product" maxlength="2000" class="r-input margin-bottom_40rpx"
					placeholder="软体家具、装修" placeholder-class="placeholder" />
			</block>
			<block>
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">您的客户画像</view>
					<view class="red-dot">*</view>
					<view class="color_FFFFFF font-size_30rpx aibut" @click="aiR(1)">
						AI生成</view>
				</view>
				<textarea type="text" v-model="customer" :maxlength="-1" class="r-input margin-bottom_40rpx"
					placeholder="25-40岁有装修需求的女性" placeholder-class="placeholder" />
			</block>

			<block>
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">核心竞争优势</view>
					<view class="red-dot">*</view>
					<view class="color_FFFFFF font-size_30rpx aibut" @click="aiR(2)">
						AI扩写</view>
				</view>
				<textarea type="text" v-model="advantage" :maxlength="-1" class="r-input margin-bottom_40rpx"
					placeholder="提供全屋定制、软装设计、材料配送、安装维修、空间规划、环保检测、智能家居集成、旧房改造、一站式采购及售后保障等服务，满足消费者从选材、设计到安装的一站式需求，兼顾个性化与实用性，并提供专业咨询与长期维护支持"
					placeholder-class="placeholder" />
			</block>
			<view class="display-a margin-bottom_40rpx">
				<view>
					<view class="display-a margin-bottom_20rpx">
						<view class="r-line"></view>
						<view class="color_FFFFFF font-size_30rpx">团队人数</view>
						<view class="red-dot">*</view>
					</view>
					<input type="text" v-model="company_scale" maxlength="60" class="r-input" style="width: 300rpx;"
						placeholder="50人" placeholder-class="placeholder" />
				</view>
				<view class="margin-left-auto">
					<view class="display-a margin-bottom_20rpx">
						<view class="r-line"></view>
						<view class="color_FFFFFF font-size_30rpx">去年营收金额(万元)</view>
						<view class="red-dot">*</view>
					</view>
					<input type="text" v-model="turnover" maxlength="60" class="r-input" style="width: 300rpx;"
						placeholder="100万" placeholder-class="placeholder" />
				</view>
			</view>




			<view style="height: 160rpx;"></view>

			<view class="r-but" @click="saveInfo()">
				点击保存
			</view>
		</view>
		<view v-else-if="currentStep === 1">
			<!-- <view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">1.您的行业是什么?</view>
				<view class="color_FFFFFF font-size_30rpx aibut" @click="startStream(1)">AI重写</view>
			</view>
			<view class="ipt-box">
				<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value1" placeholder="请输入"
					placeholder-class="placeholder"></textarea>
				<view class="tip">
					<view class="text"></view>
					<view class="text">
						{{value1.length}} / {{maxLength}}
					</view>
				</view>
			</view> -->
			<!-- <view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">2.您的客户群体是谁?</view>
				<view class="color_FFFFFF font-size_30rpx aibut" @click="startStream(2)">AI重写</view>
			</view>
			<view class="ipt-box">
				<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value2" placeholder="请输入"
					placeholder-class="placeholder"></textarea>
				<view class="tip">
					<view class="text"></view>
					<view class="text">
						{{value2.length}} / {{maxLength}}
					</view>
				</view>
			</view> -->
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">1.您的痛点是什么?</view>
				<view class="color_FFFFFF font-size_30rpx aibut" @click="startStream(3)">AI重写</view>
			</view>
			<view class="ipt-box">
				<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value3" placeholder="请输入"
					placeholder-class="placeholder"></textarea>
				<view class="tip">
					<view class="text"></view>
					<view class="text">
						{{value3.length}} / {{maxLength}}
					</view>
				</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">2.为您的AI员工介绍一下您的行业优势吧!</view>
				<view class="color_FFFFFF font-size_30rpx aibut" @click="startStream(4)">AI重写</view>
			</view>
			<view class="ipt-box">
				<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value4"
					:placeholder="placeholder4" placeholder-class="placeholder"></textarea>
				<view class="tip">
					<view hover-class="h-class" class="text" @click="setVal(4)">
						<!-- 使用案例 -->
					</view>
					<view class="text">
						{{value4.length}} / {{maxLength}}
					</view>
				</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">3.您通过什么解决方案满足客户需求?</view>
				<view class="color_FFFFFF font-size_30rpx aibut" @click="startStream(5)">AI重写</view>
			</view>
			<view class="ipt-box">
				<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value5"
					:placeholder="placeholder5" placeholder-class="placeholder"></textarea>
				<view class="tip">
					<view hover-class="h-class" class="text" @click="setVal(5)">
						<!-- 使用案例 -->
					</view>
					<view class="text">
						{{value5.length}} / {{maxLength}}
					</view>
				</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">4.有客户自发的在帮您做介绍吗?</view>
			</view>
			<view class="display-a margin-bottom_40rpx">
				<view @click="value6 = '是'" class="font-size-1" :class="value6 == '是' ? 'font-size-3' : 'font-size-2'">是
				</view>
				<view @click="value6 = '否'" class="font-size-1" :class="value6 == '否' ? 'font-size-3' : 'font-size-2'">否
				</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">5.您每天发布多少条企业内容到公域平台?</view>
				<view class="color_FFFFFF font-size_30rpx aibut" @click="startStream(6)">AI重写</view>
			</view>
			<view class="ipt-box">
				<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value7"
					:placeholder="placeholder7" placeholder-class="placeholder"></textarea>
				<view class="tip">
					<view hover-class="h-class" class="text" @click="setVal(7)">
						<!-- 使用案例 -->
					</view>
					<view class="text">
						{{value7.length}} / {{maxLength}}
					</view>
				</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">6.您的私域池塘里沉淀了多少客户量?</view>
				<view class="color_FFFFFF font-size_30rpx aibut" @click="startStream(7)">AI重写</view>
			</view>
			<view class="ipt-box">
				<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value8"
					:placeholder="placeholder8" placeholder-class="placeholder"></textarea>
				<view class="tip">
					<view hover-class="h-class" class="text" @click="setVal(8)">
						<!-- 使用案例 -->
					</view>
					<view class="text">
						{{value8.length}} / {{maxLength}}
					</view>
				</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">7.您去年新增客户量是多少人?</view>
				<view class="color_FFFFFF font-size_30rpx aibut" @click="startStream(8)">AI重写</view>
			</view>
			<view class="ipt-box">
				<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value9"
					:placeholder="placeholder9" placeholder-class="placeholder"></textarea>
				<view class="tip">
					<view hover-class="h-class" class="text" @click="setVal(9)">
						<!-- 使用案例 -->
					</view>
					<view class="text">
						{{value9.length}} / {{maxLength}}
					</view>
				</view>
			</view>
			<!-- <view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">10.您去年的营业额是多少?</view>
				<view class="color_FFFFFF font-size_30rpx aibut" @click="startStream(9)">AI重写</view>
			</view>
			<view class="ipt-box">
				<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value10"
					:placeholder="placeholder10" placeholder-class="placeholder"></textarea>
				<view class="tip">
					<view hover-class="h-class" class="text" @click="setVal(10)">
					</view>
					<view class="text">
						{{value10.length}} / {{maxLength}}
					</view>
				</view>
			</view> -->

			<view style="height: 160rpx;"></view>

			<view class="r-but" @click="saveInfo2()">
				点击保存
			</view>
		</view>
	</view>
</template>

<script>
	import {
		decodedString
	} from './utils/decodedString.js'
	export default {
		data() {

			return {
				optype: null,
				currentStep: 0, // 0 = first step, 1 = second step, etc.
				steps: [{
						title: '个人信息'
					},
					{
						title: '立项数据'
					}
				],
				// 姓名
				name: '',
				// 年龄
				age: '',
				// 性别
				gender: '男',
				// 地址
				site: '',
				// 行业
				industry: '',
				// 从业经验
				experience: '',
				// 服务
				advantage: '',
				// 企业名称
				company_name: '',
				// 用户画像
				customer: '',
				// 主营产品
				product: '',
				// 人数
				company_scale: '',
				// 金额
				turnover: '',
				// 用户信息
				userData: '',
				// 内容
				value1: '',
				value2: '',
				value3: '',
				value4: '',
				value5: '',
				value6: '是',
				value7: '',
				value8: '',
				value9: '',
				value10: '',
				// 提示内容
				placeholder4: '请输入',
				placeholder5: '请输入',
				placeholder7: '请输入',
				placeholder8: '请输入',
				placeholder9: '请输入',
				placeholder10: '请输入',
				maxLength: 1500,
				requestList: [],
				// 请求队列管理
				requestQueue: [], // 待处理的请求队列
				activeRequests: 0, // 当前活跃的请求数
				completedRequests: {}, // 已完成的请求记录
				activeRequestTasks: [], // 当前活跃的请求任务数组
				isPageUnloaded: false // 页面是否已卸载的标志
			}
		},

		methods: {
			aiR(type) {
				let content = ''
				let n = ''
				if (type === 1) {
					if (!this.product) {
						this.$sun.toast('请输入你的主营产品', 'none');
						return
					}
					content = this.product
					this.customer = ''
					n = 'customer'
				} else if (type === 2) {
					if (!this.advantage) {
						this.$sun.toast('请输入你的核心竞争优势', 'none');
						return
					}
					content = this.advantage
					this.advantage = ''
					n = 'advantage'
				}
				// 使用流式请求
				const requestTask = this.$http.requestStream(this.$api.getAiTip, "POST", {
					uid: uni.getStorageSync('uid'),
					advantage: content,
					pro: content,
					type
				}, {
					onChunk: (data) => {
						// 处理接收到的数据块
						try {
							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 追加到正确的目标属性
								this[n] += text;
							} else if (typeof data === 'string') {
								// 如果是字符串，直接追加到目标属性
								this[n] += data;
							} else {
								console.warn("收到未知类型的数据块:", data);
							}
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						// this.$sun.toast('生成完成', 'none');
					},
					onError: (err) => {
						uni.hideLoading();
						this.$sun.toast('生成出错，请重试', 'none');
					}
				});
			},
			goToStep(index) {
				if (index === 1) {
					if (!this.userData) {
						this.$sun.toast('请先填写个人信息', 'none');
						return
					}
					this.getInfo2()

				}
				this.currentStep = index;
			},
			// 设置性别
			setSex(type) {
				this.gender = type
			},
			// 获取数据
			async getInfo2() {
				const result = await this.$http.post({
					url: this.$api.getProject,
					data: {
						uid: uni.getStorageSync('uid'),

					}
				});
				if (result.errno == 0) {
					if (!result.data) {
						[3, 4, 5].forEach(item => {
							this.startStream(item);
						})
						return
					}
					this.value1 = result.data.industry
					this.value2 = result.data.target_customers
					this.value3 = result.data.pain_points
					this.value4 = result.data.industry_advantages
					this.value5 = result.data.solution_requirements
					this.value6 = result.data.custom_promotion
					this.value7 = result.data.launch_recommendations
					this.value8 = result.data.private_user_summary
					this.value9 = result.data.new_users_last_year
					this.value10 = result.data.annual_revenue
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},
			//提交数据
			async saveInfo2() {
				if (!this.isPost) {
					this.$sun.toast('请先完善数据', 'none');
					return
				}
				const result = await this.$http.post({
					url: this.$api.saveProject,
					data: {
						uid: uni.getStorageSync('uid'),
						industry: this.value1,
						target_customers: this.value2,
						pain_points: this.value3,
						industry_advantages: this.value4,
						solution_requirements: this.value5,
						custom_promotion: this.value6,
						launch_recommendations: this.value7,
						private_user_summary: this.value8,
						new_users_last_year: this.value9,
						annual_revenue: this.value10,
					}
				});
				if (result.errno == 0) {
					this.$sun.toast('保存成功');
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},
			//提交数据
			async saveInfo() {
				if (!this.name) {
					this.$sun.toast('请输入你的姓名', 'none');
					return
				}
				if (!this.age) {
					this.$sun.toast('请输入你的年龄', 'none');
					return
				}
				if (!this.site) {
					this.$sun.toast('请输入你的城市', 'none');
					return
				}
				if (!this.industry) {
					this.$sun.toast('请输入你的行业', 'none');
					return
				}
				if (!this.experience) {
					this.$sun.toast('请输入你的从业年限', 'none');
					return
				}
				if (!this.site) {
					this.$sun.toast('请输入你的所在地', 'none');
					return
				}
				if (!this.company_name) {
					this.$sun.toast('请输入你的企业名称', 'none');
					return
				}
				if (!this.customer) {
					this.$sun.toast('请输入你的客户画像', 'none');
					return
				}
				if (!this.product) {
					this.$sun.toast('请输入你的主营产品', 'none');
					return
				}
				if (!this.advantage) {
					this.$sun.toast('请输入你的核心竞争优势', 'none');
					return
				}
				if (!this.company_scale) {
					this.$sun.toast('请输入你的团队人数', 'none');
					return
				}
				if (!this.turnover) {
					this.$sun.toast('请输入你的去年营收金额', 'none');
					return
				}

				const result = await this.$http.post({
					url: this.$api.addUserInfo,
					data: {
						uid: uni.getStorageSync('uid'),
						site: this.site,
						industry: this.industry,
						experience: this.experience,
						advantage: this.advantage,
						name: this.name,
						age: this.age,
						gender: this.gender,
						company_name: this.company_name,
						customer: this.customer,
						product: this.product,
						company_scale: this.company_scale,
						turnover: this.turnover
					}
				});
				if (result.errno == 0) {
					this.$sun.toast('保存成功');
					// 拼接字符串
					this.userData =
						`姓名：${this.name}，年龄：${this.age}，性别：${this.gender}，城市：${this.site}，行业：${this.industry}，从业年限：${this.experience}，企业名称：${this.company_name}，用户画像：${this.customer}，主营产品：${this.product}，核心竞争优势：${this.advantage}，团队人数：${this.company_scale}，去年营收金额：${this.turnover}`
				} else {
					this.$sun.toast(result.message, 'none');
				}

			},
			//获取数据
			async getInfo() {
				const result = await this.$http.get({
					url: this.$api.getUserInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					if (result.data) {
						let data = JSON.parse(result.data)
						this.site = data.site
						this.name = data.name
						this.gender = data.gender
						this.age = data.age
						this.experience = data.experience
						this.industry = data.industry
						this.company_name = data.company_name
						this.customer = data.customer
						this.product = data.product
						this.advantage = data.advantage
						this.company_scale = data.company_scale
						this.turnover = data.turnover

						// 拼接字符串
						this.userData =
							`姓名：${this.name}，年龄：${this.age}，性别：${this.gender}，城市：${this.site}，行业：${this.industry}，从业年限：${this.experience}，企业名称：${this.company_name}，用户画像：${this.customer}，主营产品：${this.product}，核心竞争优势：${this.advantage}，团队人数：${this.company_scale}，去年营收金额：${this.turnover}`
					}

				}

			},
			// 根据type获取对应的属性名
			getTargetPropertyByType(type) {
				// type 1-3 对应 value1-3
				if (type >= 1 && type <= 3) {
					return `value${type}`;
				}
				// type 4-5 对应 placeholder4-5
				else if (type >= 4 && type <= 5) {
					// return `placeholder${type}`;
					return `value${type}`;
				}
				// type 6-9 对应 placeholder7-10
				else if (type >= 6 && type <= 9) {
					// return `placeholder${type + 1}`; // 注意这里的映射关系
					return `value${type + 1}`; // 注意这里的映射关系
				}
				return null;
			},
			startStream(type) {
				let url = this.$api.getAIProject

				// 增加活跃请求计数
				this.activeRequests++;

				let data = {
					uid: uni.getStorageSync('uid'), // 请求参数
					type,
					content: this.userData
				}

				// 根据type获取正确的目标属性名
				const targetProperty = this.getTargetPropertyByType(type);
				if (targetProperty) {
					// 初始化目标属性
					this[targetProperty] = '';
				}

				// 使用流式请求
				const requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						// 处理接收到的数据块
						try {
							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 追加到正确的目标属性
								if (targetProperty) {
									this[targetProperty] += text;
								}
							} else if (typeof data === 'string') {
								// 如果是字符串，直接追加到目标属性
								if (targetProperty) {
									this[targetProperty] += data;
								}
							} else {
								console.warn("收到未知类型的数据块:", data);
							}
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						console.log(`类型${type}请求完成`);
						// 标记请求完成，使用数字类型的键
						this.completedRequests[type] = true;
						// 减少活跃请求计数
						this.activeRequests--;
						// 从活跃请求任务数组中移除
						this.activeRequestTasks = this.activeRequestTasks.filter(task => task.type !== type);
						// 处理队列中的下一个请求
						this.processQueue();
					},
					onError: (err) => {
						uni.hideLoading();
						console.error(`类型${type}流式请求错误:`, err);
						// 减少活跃请求计数
						this.activeRequests--;
						// 从活跃请求任务数组中移除
						this.activeRequestTasks = this.activeRequestTasks.filter(task => task.type !== type);
						// 尽管出错，也继续处理队列
						this.processQueue();
					}
				});

				// 保存请求任务到活跃任务数组
				this.activeRequestTasks.push({
					type: type,
					task: requestTask
				});
			},
			// 使用案例
			setVal(type) {
				return
				// 需要检查对应的placeholder是否已经生成完成
				let requestType = type;

				// 根据type找到对应的请求类型
				// type 4-5 直接对应请求type 4-5
				// type 7-10 对应请求type 6-9
				if (type >= 7 && type <= 10) {
					requestType = type - 1;
				}

				// 检查该请求是否已完成
				if (!this.completedRequests[requestType]) {
					this.$sun.toast('案例正在生成中，请稍后再试', 'none');
					return;
				}

				// 请求已完成，可以使用案例
				this[`value${type}`] = this[`placeholder${type}`]
			},
			// 中止请求
			stopStream() {
				// 取消所有活跃的请求
				if (this.activeRequestTasks.length > 0) {
					console.log(`正在取消${this.activeRequestTasks.length}个活跃请求`);
					this.activeRequestTasks.forEach(item => {
						if (item.task && typeof item.task.cancel === 'function') {
							item.task.cancel();
							console.log(`已取消类型${item.type}的请求`);
						}
					});
					// 清空活跃请求数组
					this.activeRequestTasks = [];
					this.activeRequests = 0;
					console.log('已中止所有请求');
				} else {
					console.log('没有活跃的请求需要取消');
				}
			},
			// 处理请求队列
			processQueue() {
				// 如果页面已卸载，不再处理队列
				if (this.isPageUnloaded) {
					console.log('页面已卸载，终止队列处理');
					return;
				}

				// 检查队列中是否有待处理的请求
				if (this.requestQueue.length === 0) {
					console.log('请求队列为空，没有更多请求需要处理');
					return;
				}

				// 检查当前活跃请求数量
				if (this.activeRequests >= 3) {
					console.log('当前活跃请求已达到上限(3)，等待请求完成');
					return;
				}

				// 从队列中获取下一个待处理的请求
				const nextRequest = this.requestQueue.shift();
				console.log(`开始处理队列中的下一个请求: type=${nextRequest}`);

				// 开始处理这个请求
				this.startStream(nextRequest);
			},
			// 生成定位
			postData() {
				if (this.value1 == '') {
					this.$sun.toast('请输入您的行业', 'none');
					return
				} else if (this.value2 == '') {
					this.$sun.toast('请输入您的客户群体', 'none');
					return
				} else if (this.value3 == '') {
					this.$sun.toast('请输入您的客户痛点', 'none');
					return
				} else if (this.value4 == '') {
					this.$sun.toast('请输入您的行业优势', 'none');
					return
				} else if (this.value5 == '') {
					this.$sun.toast('请输入您的解决方案', 'none');
					return
				} else if (this.value6 == '') {
					this.$sun.toast('请选择您是否有客户自发的在帮您做介绍', 'none');
					return
				} else if (this.value7 == '') {
					this.$sun.toast('请输入您每天发布多少条企业内容到公域平台', 'none');
					return
				} else if (this.value8 == '') {
					this.$sun.toast('请输入您的私域池塘里沉淀了多少客户量', 'none');
					return
				} else if (this.value9 == '') {
					this.$sun.toast('请输入您去年新增客户量是多少人', 'none');
					return
				} else if (this.value10 == '') {
					this.$sun.toast('请输入您去年的营业额', 'none');
					return
				}
				let userData = `行业:${this.value1}
			客户群体:${this.value2}
			客户痛点:${this.value3}
			行业优势:${this.value4}
			解决方案:${this.value5}
			客户自发的在帮您做介绍:${this.value6}
			每天发布多少条企业内容到公域平台:${this.value7}
			私域池塘里沉淀了多少客户量:${this.value8}
			去年新增客户量是多少人:${this.value9}
			去年营业额是多少:${this.value10}`
				uni.setStorageSync('userData', userData)
				uni.navigateTo({
					url: '/subPackages/subPackageA/report?type=2'
				})
			},
		},
		onLoad(options) {
			if (options.type) {
				this.optype = Number(options.type)
			}
			this.isPageUnloaded = false;
			this.requestList = []
			this.getInfo()
			// 初始化请求队列 - 所有需要请求的类型
			this.requestQueue = [6, 7, 8]; // 剩余的请求将放入队列
			this.activeRequests = 0;
			this.completedRequests = {};
		},
		computed: {
			isPost() {
				return this.value1 != '' && this.value2 != '' && this.value3 != '' && this.value4 != '' && this.value5 !=
					'' && this.value6 != '' && this.value7 != '' && this.value8 != '' && this.value9 != '' && this
					.value10 != ''
			}
		},
		onUnload() {
			this.stopStream();
			this.isPageUnloaded = true;
		}
	}
</script>

<style lang="scss">
	.aibut {
		margin-left: auto;
		background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
		padding: 6rpx 8rpx;
		border-radius: 8rpx;
	}

	.font-size-3 {
		background-color: #0084FF;
	}

	.font-size-2 {
		background-color: #343434;
	}

	.font-size-1 {
		width: 184rpx;
		text-align: center;
		border-radius: 10rpx;
		margin-right: 30rpx;
		color: #FFF;
		padding: 20rpx 0;
	}

	.r-but {
		position: fixed;
		bottom: 50rpx;
		z-index: 9;
		font-size: 32rpx;
		color: #FFF;
		padding: 30rpx 0;
		width: 710rpx;
		text-align: center;
		border-radius: 10rpx;
		box-shadow: 4rpx 6rpx 28rpx 0 rgba(30, 156, 214, 0.81), inset 0 0 22rpx 0 rgba(204, 235, 255, 0.3);
		background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
	}

	textarea {
		width: 670rpx;
		height: 300rpx;
		padding: 20rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.r-input {
		width: 670rpx;
		padding: 20rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.r-top {
		padding: 30rpx 20rpx;
	}

	.r-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245), rgb(66, 72, 244) 100%);
		margin-right: 14rpx;
	}

	.red-dot {
		color: #FF0000;
		margin-left: 8rpx;
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}

	.step-indicator {
		display: flex;
		justify-content: space-between;
		margin-bottom: 40rpx;
		padding: 30rpx;
		background-color: #111D37;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
		position: relative;
	}

	.step-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		flex: 1;
		z-index: 1;
	}

	.step-circle {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background: #2A324A;
		display: flex;
		align-items: center;
		justify-content: center;
		color: rgba(255, 255, 255, 0.7);
		font-weight: bold;
		margin-bottom: 10rpx;
		transition: all 0.3s ease;
	}

	.step-item.active .step-circle,
	.step-item.completed .step-circle {
		background: linear-gradient(96.34deg, rgb(109, 221, 245), rgb(66, 72, 244))
	}

	.step-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 6rpx;
		text-align: center;
		color: rgba(255, 255, 255, 0.85);
	}

	.step-status {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.5);
	}

	.step-item.active .step-status {
		color: rgb(66, 72, 244);
		font-weight: 500;
	}

	.step-item.completed .step-status {
		color: #5fe6ae;
	}

	.step-line {
		position: absolute;
		top: 30rpx;
		right: -50%;
		width: 100%;
		height: 4rpx;
		background-color: #2A324A;
		z-index: -1;
	}

	.step-item.completed .step-line {
		background: linear-gradient(96.34deg, rgb(109, 221, 245), rgb(66, 72, 244));
	}
</style>