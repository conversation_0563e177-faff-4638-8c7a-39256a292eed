<template>
	<view>
		
		<view class="h_20rpx"></view>
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'392.png'+')'}">
			<view class="display-fw-a">
				<view class="width_236rpx-center margin-bottom_20rpx">
					<view class="font-size_32rpx margin-bottom_4rpx">{{countObj.published_task}}</view>
					<view>发布成功</view>
				</view>
				<view class="width_236rpx-center margin-bottom_20rpx">
					<view class="font-size_32rpx margin-bottom_4rpx">{{countObj.published_wait_task}}</view>
					<view>待发布</view>
				</view>
				<view class="width_236rpx-center margin-bottom_20rpx">
					<view class="font-size_32rpx margin-bottom_4rpx">{{countObj.published_fail_task}}</view>
					<view>发布失败</view>
				</view>
				<view class="width_236rpx-center margin-bottom_20rpx">
					<view class="font-size_32rpx margin-bottom_4rpx">{{countObj.exposure_count}}</view>
					<view>曝光量</view>
				</view>
				<view class="width_236rpx-center margin-bottom_20rpx">
					<view class="font-size_32rpx margin-bottom_4rpx">{{countObj.like_count}}</view>
					<view>点赞量</view>
				</view>
				<view class="width_236rpx-center margin-bottom_20rpx">
					<view class="font-size_32rpx margin-bottom_4rpx">{{countObj.comment}}</view>
					<view>评论数</view>
				</view>
			</view>
		</view>
		
		<view class="display-a padding-right_20rpx margin-bottom_20rpx">
			<block v-for="(item,index) in tabs" :key="index">
				<view @click="getTabs(item.id)" class="tabs-list" :class="tabId == item.id ? 'tabs-list-2' : 'tabs-list-1'">{{item.name}}</view>
			</block>
		</view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display padding-bottom_20rpx">
						<image class="avater" :src="item.avatar"></image>
						<view class="list-name">
							<view class="font-size_32rpx margin-bottom_10rpx">{{item.account_name}}</view>
							<view class="color_999999 font-size_26rpx">(待)发布时间：{{item.release_time}}</view>
						</view>
						<view v-if="item.status == 1 || item.status == 2 || item.status == 5 || item.status == 6" class="list-label">待发布</view>
						<view v-if="item.status == 3" class="list-label" style="background: #FFA82D;">发布成功</view>
						<view v-if="item.status == 4" class="list-label" style="background: #FF0000;">发布失败</view>
						<view v-if="item.status == 7" class="list-label" style="background: #A1A1A1;">任务终止</view>
					</view>
					<block v-if="item.status == 3">
						<view class="display-a padding_20rpx_0">
							<view class="width_236rpx-center">
								<view class="font-size_32rpx margin-bottom_10rpx">{{item.exposure_count?item.exposure_count:0}}</view>
								<view class="color_c6c6c6">曝光量</view>
							</view>
							<view class="width_236rpx-center">
								<view class="font-size_32rpx margin-bottom_10rpx">{{item.like_count?item.like_count:0}}</view>
								<view class="color_c6c6c6">点赞数</view>
							</view>
							<view class="width_236rpx-center">
								<view class="font-size_32rpx margin-bottom_10rpx">{{item.comment?item.comment:0}}</view>
								<view class="color_c6c6c6">评论数</view>
							</view>
						</view>
						
						<view class="display-a padding_30rpx_0" v-if="selLabel == 1">
							<view class="comment color_00FFCA" @click="getComment(item.id)">查看评论</view>
							<view class="task-line"></view>
							<view class="comment color_FFC100" @click="getVideo(item.video_src)">查看作品</view>
						</view>
						<view class="display-a-jc padding_30rpx_0" v-if="selLabel == 2 || selLabel == 4">
							<view class="comment color_FFC100" style="width: 710rpx;" @click="getVideo(item.video_src)">查看作品</view>
						</view>
					</block>
					<block v-if="item.status == 1">
						<view class="display-a-jc padding_30rpx_0">
							<view class="comment color_FF0000" style="width: 710rpx;" @click="reissue(item.id)">立即发布</view>
						</view>
					</block>
					<block v-if="item.status == 4">
						<view class="padding_30rpx">失败原因: <span class="color_FF0000 margin-left_10rpx">{{item.hint}}</span></view>
						<view class="display-a-jc padding_30rpx_0">
							<view class="comment color_FF0000" style="width: 710rpx;" @click="reissue(item.id)">立即补发</view>
						</view>
					</block>
				</view>
			</block>
		</mescroll-body>
		
		<image class="matrix-8" @click="getUserVideoData()" :src="imgUrl+'397.png'"></image>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				tabs: [
					{id:'',name:'全部'},
					{id:'1',name:'待发布'},
					{id:'3',name:'发布成功'},
					{id:'4',name:'发布失败'},
				],
				tabId: '',
				
				countObj: {}, //数据统计
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				selLabel: '', //1。D音 2。K手 3。视频号 4.小红薯 5.B站
				taskId: '', //任务ID/账户ID
				type: '', //1账户 2任务
				
				ms: 1000*60,
				lastClick: Date.now() - (1000*60),
				
				isWhether: true, //判断重复点击
				
			}
		},
		
		onLoad(options) {
			uni.getSystemInfo({
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 400;
				}
			})
			if (options.type) {
				this.taskId = options.taskId;
				this.selLabel = options.selLabel;
				this.type = options.type;
				this.getCount();
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}
		},
		
		onShow() {
			
		},
		
		methods: {
			
			getTabs(id) {
				this.tabId = id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			//统计数据
			async getCount() {
				const result = await this.$http.post({
					url: this.$api.VideoTaskStatistics,
					data: {
						id: this.type == 2 ? this.taskId : '',
						three_account_id: this.type == 1 ? this.taskId : '',
					}
				});
				if (result.errno == 0) {
					this.countObj = result.data;
				}
			},
			
			//立即发布
			async reissue(id) {
				
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				
				let getUrl = '';
				
				if (this.selLabel == 1) {
					getUrl = this.$api.publishDouyin;
				}
				if (this.selLabel == 2) {
					getUrl = this.$api.publisheKuaishou;
				}
				if (this.selLabel == 4) {
					getUrl = this.$api.releaseXiaohongshu;
				}
				
				const result = await this.$http.post({
					url: getUrl,
					data: {
						uid: uni.getStorageSync("uid"),
						id: id,
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.isWhether = true;
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
					}, 2000);
				}else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
				
			},
			
			/*  手动刷新  */
			async getUserVideoData() {
					
				if (Date.now() - this.lastClick >= this.ms) {
					this.lastClick = Date.now();
				}else{
					this.$sun.toast("请勿频繁操作,一分钟后在刷新!",'none');
					return;
				}
				
				let getUrl = '';
				
				if (this.selLabel == 1) {
					getUrl = this.$api.douyinVideoInfo;
				}
				if (this.selLabel == 2) {
					getUrl = this.$api.kuaishuoVideoInfo;
				}
				if (this.selLabel == 4) {
					getUrl = this.$api.xiaohongshuVideoInfo;
				}
				
				const result = await this.$http.post({
					url: getUrl,
					data: {
						three_account_id: this.type == 1 ? this.taskId : '',
						id: this.type == 2 ? this.taskId : '',
					}
				});
				if (result.errno == 0) {
					this.$sun.toast("刷新成功");
					setTimeout(() => {
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
						this.getCount();
					}, 2000);
					
				}else {
					this.$sun.toast("因有异常数据,刷新失败",'none');
				}
				
			},
			
			//查看评论
			getComment(id) {
				uni.navigateTo({
					url: '/pages/matrix/comment?id='+id+'&tabId='+this.selLabel
				})
			},
			
			//查看作品
			getVideo(url) {
				uni.navigateTo({
					url: '/pages/matrix/playVideo?url='+url
				})
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.accountVideoList,
					data: {
						uid: uni.getStorageSync("uid"),
						task_id: this.type == 2 ? this.taskId : '',
						three_account_id: this.type == 1 ? this.taskId : '',
						status  : this.tabId,
						type: this.selLabel,
						page: scroll.num,
						limit: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			
		}
	}
</script>

<style lang="scss">
	
	.task-line {
		width: 2rpx;
		height: 34rpx;
		background-color: #3B3B3B;
	}
	
	.padding_30rpx_0 {
		padding: 30rpx 0;
		border-top: 1px solid #292929;
	}
	
	.list-public {
		background-color: #0F0F0F;
		padding: 0;
		color: #FFF;
	}
	
	.matrix-8 {
		width: 96rpx;
		height: 96rpx;
		position: fixed;
		right: 14rpx;
		bottom: 180rpx;
		z-index: 99;
	}
	
	.color_888 {
		font-size: 26rpx;
		color: #888;
	}
	
	.comment {
		width: 354rpx;
		text-align: center;
	}
	
	.list-label {
		display: inline-block;
		padding: 0 14rpx;
		border-radius: 0 10rpx;
		color: #FFFFFF;
		font-size: 26rpx;
		margin-left: auto;
		background-color: #2097EC;
		height: 46rpx;
		line-height: 46rpx;
	}
	
	.list-name {
		width: 420rpx;
		margin-top: 26rpx;
	}
	
	.avater {
		width: 84rpx;
		height: 84rpx;
		border-radius: 50%;
		margin: 26rpx 20rpx 0;
	}
	
	.tabs-list-2 {
		background: #166DFD;
		color: #FFF;
	}
	
	.tabs-list-1 {
		background: #444444;
		color: #E8E8E8;
	}
	
	.tabs-list {
		width: 132rpx;
		text-align: center;
		border-radius: 10rpx;
		padding: 8rpx 0;
		margin-left: 20rpx;
	}
	
	.bg {
		width: 710rpx;
		height: 265rpx;
		border-radius: 10rpx;
		margin: 0 20rpx 30rpx;
		box-shadow: 0px 4px 4px 0px rgba(18, 84, 254, 0.47);
		background-repeat: no-repeat;
		background-size: contain;
		color: #FFF;
		padding: 36rpx 0;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}
	
</style>
