<template>
	<view class="loading-container" v-if="show">
		<view class="tech-grid"></view>
		<view class="tech-overlay"></view>
		
		<!-- 光点元素 -->
		<view class="tech-dots">
			<view v-for="n in 5" :key="n" class="tech-dot"></view>
		</view>
		
		<!-- 粒子元素 -->
		<view class="floating-particles">
			<view v-for="n in 10" :key="n" class="particle"></view>
		</view>
		
		<!-- 加载动画 -->
		<view class="loading-spinner">
			<view class="spinner-ring"></view>
			<view class="spinner-core"></view>
		</view>
		
		<!-- 加载文本 -->
		<view class="loading-text" v-if="text" :style="textColorStyle">{{ text }}</view>
	</view>
</template>

<script>
export default {
	name: 'FullScreenLoading',
	props: {
		show: {
			type: Boolean,
			default: true
		},
		text: {
			type: String,
			default: '加载中...'
		},
		textColor: {
			type: String,
			default: '' // 默认为空，使用CSS中定义的渐变色
		}
	},
	computed: {
		textColorStyle() {
			if (!this.textColor) {
				return {}; // 返回空对象，使用默认样式
			}
			
			// 如果提供了自定义颜色，则覆盖默认的渐变色
			return {
				'background': 'none',
				'-webkit-background-clip': 'initial',
				'-webkit-text-fill-color': 'initial',
				'background-clip': 'initial',
				'text-fill-color': 'initial',
				'color': this.textColor
			};
		}
	}
}
</script>

<style lang="scss">
.loading-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: #080E1E;
	z-index: 9999;
	overflow: hidden;
}

/* 网格背景 */
.tech-grid {
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background-image:
		linear-gradient(rgba(20, 40, 100, 0.08) 1px, transparent 1px),
		linear-gradient(90deg, rgba(20, 40, 100, 0.08) 1px, transparent 1px);
	background-size: 30px 30px;
	z-index: 1;
	animation: gridMove 30s linear infinite;
	transform: rotate(15deg);
}

/* 渐变叠加层 */
.tech-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background:
		radial-gradient(circle at 20% 10%, rgba(127, 27, 255, 0.15) 0%, transparent 30%),
		radial-gradient(circle at 80% 90%, rgba(65, 88, 208, 0.15) 0%, transparent 30%),
		radial-gradient(circle at 50% 50%, rgba(8, 14, 30, 0.1) 0%, rgba(8, 14, 30, 0.5) 100%);
	z-index: 2;
}

@keyframes gridMove {
	0% {
		transform: translateY(0) rotate(15deg);
	}
	100% {
		transform: translateY(-100px) rotate(15deg);
	}
}

/* 光点元素 */
.tech-dots {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	pointer-events: none;
	z-index: 3;
}

.tech-dot {
	position: absolute;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.4);
	box-shadow: 0 0 15px 4px rgba(127, 27, 255, 0.4);
	animation: pulse 2s infinite;
}

.tech-dot:nth-child(1) {
	top: 10%;
	left: 20%;
	animation-delay: 0s;
}

.tech-dot:nth-child(2) {
	top: 70%;
	left: 10%;
	animation-delay: 0.5s;
}

.tech-dot:nth-child(3) {
	top: 30%;
	left: 85%;
	animation-delay: 1s;
}

.tech-dot:nth-child(4) {
	top: 60%;
	left: 75%;
	animation-delay: 1.5s;
}

.tech-dot:nth-child(5) {
	top: 20%;
	left: 50%;
	animation-delay: 2s;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 0.2;
		box-shadow: 0 0 15px 2px rgba(127, 27, 255, 0.2);
	}
	50% {
		transform: scale(2);
		opacity: 0.8;
		box-shadow: 0 0 20px 5px rgba(127, 27, 255, 0.6);
	}
	100% {
		transform: scale(1);
		opacity: 0.2;
		box-shadow: 0 0 15px 2px rgba(127, 27, 255, 0.2);
	}
}

/* 粒子动画 */
.floating-particles {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	z-index: 3;
	pointer-events: none;
}

.particle {
	position: absolute;
	width: 2px;
	height: 2px;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 50%;
	box-shadow: 0 0 4px 1px rgba(127, 27, 255, 0.3);
	animation: float 15s linear infinite;
}

.particle:nth-child(1) {
	top: 10%;
	left: 20%;
	animation-duration: 10s;
}

.particle:nth-child(2) {
	top: 20%;
	left: 80%;
	animation-duration: 12s;
}

.particle:nth-child(3) {
	top: 30%;
	left: 40%;
	animation-duration: 9s;
}

.particle:nth-child(4) {
	top: 40%;
	left: 60%;
	animation-duration: 14s;
}

.particle:nth-child(5) {
	top: 50%;
	left: 10%;
	animation-duration: 11s;
}

.particle:nth-child(6) {
	top: 60%;
	left: 30%;
	animation-duration: 13s;
}

.particle:nth-child(7) {
	top: 70%;
	left: 70%;
	animation-duration: 8s;
}

.particle:nth-child(8) {
	top: 80%;
	left: 50%;
	animation-duration: 15s;
}

.particle:nth-child(9) {
	top: 90%;
	left: 90%;
	animation-duration: 10s;
}

.particle:nth-child(10) {
	top: 15%;
	left: 45%;
	animation-duration: 11s;
}

@keyframes float {
	0% {
		transform: translateY(0) translateX(0) scale(1);
		opacity: 0;
	}
	10% {
		opacity: 0.8;
		transform: translateY(-10px) translateX(10px) scale(1.2);
	}
	50% {
		transform: translateY(-50px) translateX(-20px) scale(0.8);
		opacity: 0.4;
	}
	90% {
		transform: translateY(-100px) translateX(30px) scale(1.5);
		opacity: 0.1;
	}
	100% {
		transform: translateY(-120px) translateX(20px) scale(0);
		opacity: 0;
	}
}

/* 加载动画 */
.loading-spinner {
	position: relative;
	width: 80rpx;
	height: 80rpx;
	z-index: 4;
	margin-bottom: 30rpx;
}

.spinner-ring {
	position: absolute;
	width: 100%;
	height: 100%;
	border: 4rpx solid transparent;
	border-top-color: rgba(127, 27, 255, 0.8);
	border-right-color: rgba(127, 27, 255, 0.6);
	border-bottom-color: rgba(127, 27, 255, 0.4);
	border-left-color: rgba(127, 27, 255, 0.2);
	border-radius: 50%;
	animation: spinnerRotate 1.5s linear infinite;
}

.spinner-core {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 40%;
	height: 40%;
	background: rgba(127, 27, 255, 0.6);
	border-radius: 50%;
	transform: translate(-50%, -50%);
	box-shadow: 0 0 20rpx 5rpx rgba(127, 27, 255, 0.4);
	animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes spinnerRotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* 加载文本 */
.loading-text {
	margin-top: 20rpx;
	font-size: 28rpx;
	color: #FFF;
	background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	text-fill-color: transparent;
	z-index: 4;
	animation: textPulse 2s ease-in-out infinite alternate;
}

@keyframes textPulse {
	0% {
		opacity: 0.7;
	}
	100% {
		opacity: 1;
	}
}
</style> 