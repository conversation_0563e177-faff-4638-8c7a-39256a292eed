<template>
	<view class="sunui-tabbar" :class="[fixed ? 'fixed' : '']">
		<view class="tablist flexbox flex_alignc sunui-bottom" :style="[{ 'background-color': backgroundColor ? backgroundColor : '' }]">
			<block v-for="(item, index) in tabList" :key="index">
				<view class="navigator flex-column-center" :class="current == index ? 'on' : ''" @tap="switchTab(index,item.url)">
					<image style="width: 56rpx;height: 56rpx;margin-top: 4rpx;margin-bottom: 2rpx;" v-if="current == index" :src="item.icon_checked"></image>
					<image style="width: 56rpx;height: 56rpx;margin-top: 4rpx;margin-bottom: 2rpx;" v-if="current != index" :src="item.icon"></image>
					<view class="text name-text" :style="[current == index ? { color: tintColor } : { color: color }]">{{ item.name }}</view>
				</view>
			</block>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				tabList: [
					{
						icon: this.$imgUrl + 'admin/statistics_gray.png',
						icon_checked: this.$imgUrl + 'admin/statistics.png',
						name: "平台统计",
						url: "/wjyk_recycle/pages/index/my/admin/index",
					},{
						icon: this.$imgUrl + 'admin/manage_gray.png',
						icon_checked: this.$imgUrl + 'admin/manage.png',
						name: "管理审核",
						url: "/wjyk_recycle/pages/index/my/admin/examine",
					},{
						icon: this.$imgUrl + 'admin/check_gray.png',
						icon_checked: this.$imgUrl + 'admin/check.png',
						name: "审核提现",
						url: "/wjyk_recycle/pages/index/my/admin/withdrawal",
					},{
						icon: this.$imgUrl + 'admin/recharge-gray.png',
						icon_checked: this.$imgUrl + 'admin/recharge-center.png',
						name: "充值记录",
						url: "/wjyk_recycle/pages/index/my/admin/recharge",
					},{
						icon: this.$imgUrl + 'admin/withdraw-gray.png',
						icon_checked: this.$imgUrl + 'admin/withdraw-center.png',
						name: "提现记录",
						url: "/wjyk_recycle/pages/index/my/admin/withdrawalRecord",
					}
				],
				platform: ''
			};
		},
		name: 'sunui-controller-tabbar',
		props: {
			current: {
				type: [Number, String],
				default: 0
			},
			backgroundColor: {
				type: String,
				default: '#fff'
			},
			color: {
				type: String,
				default: '#A1A1A1'
			},
			tintColor: {
				type: String,
				default: '#00B388'
			},
			fixed: {
				type: [Boolean, String],
				default: false
			}
		},
		created() {
			// console.log('this.current', this.current);
			//首页底部菜单
			this.indexMenu();
		},
		methods: {
			switchTab(index, url) {
				// this.currentTabIndex = index;
				this.$emit('click', index);
				// console.log('this.currentTabIndex', url + '&index=' + index, url);
				uni.redirectTo({
					url: url + '?index=' + index
				});
			},
			// 首页底部菜单
			async indexMenu() {

			}
		}
	};
</script>
<style scoped>
	/* view {
		line-height: 1.5;
	} */

	.name-text {
		/* width: 120rpx; */
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		text-align: center;
	}

	.flexbox {
		display: flex;
	}

	.sunui-position {
		position: relative;
	}

	.sunui-badge {
		background-color: #ff3e3e;
		border-radius: 50upx;
		box-sizing: border-box;
		color: #fff;
		font-size: 12px;
		font-family: arial;
		padding: 6upx 12upx;
		line-height: 1.08;
	}

	.sunui-badge_dot {
		border-radius: 100%;
		font-size: 0;
		overflow: hidden;
		padding: 0;
		height: 18upx;
		width: 18upx;
	}

	.sunui-bottom,
	.sunui-bottombar {
		position: relative;
	}

	.sunui-bottom:before {
		content: '';
		background: #dbdbdb;
		height: 1px;
		width: 100%;
		position: absolute;
		left: 0;
		top: 0;
		transform: scaleY(0.5);
		transform-origin: 0 0;
	}

	.sunui-bottombar:after {
		content: '';
		background: #dbdbdb;
		height: 1px;
		width: 100%;
		position: absolute;
		left: 0;
		bottom: 0;
		transform: scaleY(0.5);
		transform-origin: 0 100%;
	}

	.sunui-tabbar {
		display: flex;
		width: 100%;
	}

	.sunui-tabbar .tablist {
		background-color: #fff;
		height: 120upx;
		width: 750upx;
		position: relative;
		z-index: 99;
	}

	.sunui-tabbar .tablist.sunui-bottom:before {
		background: #bbb;
	}

	.sunui-tabbar.fixed {
		padding-top: 116upx;
	}

	.sunui-tabbar.fixed .tablist {
		position: fixed;
		bottom: 0;
		left: 0;
	}

	.sunui-tabbar .tablist .navigator {
		flex: 1;
		text-align: center;
		/* height: 100upx; */
		padding-top: 10rpx;
	}

	.sunui-tabbar .tablist .icon {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto;
		margin-top: 10upx;
		height: 50upx;
		width: 50upx;
		position: relative;
	}

	.sunui-tabbar .tablist .icon .iconfont {
		color: #999;
		font-size: 44upx;
	}

	.sunui-tabbar .tablist .text {
		color: #999;
		font-size: 24upx;
	}

	.sunui-tabbar .tablist .navigator.on .icon .iconfont {
		color: #ff592e;
	}

	.sunui-tabbar .tablist .navigator.on .text {
		color: #ff592e;
	}

	.sunui-tabbar .tablist .sunui-badge {
		position: absolute;
		top: -3upx;
		left: 32upx;
	}

	.sunui-tabbar .tablist .sunui-badge_dot {
		left: 36upx;
	}
</style>
