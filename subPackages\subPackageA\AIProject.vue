<template>
	<view class="r-top">
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">1.您的行业是什么?</view>
		</view>
		<view class="ipt-box">
			<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value1" placeholder="请输入"
				placeholder-class="placeholder"></textarea>
			<view class="tip">
				<view class="text"></view>
				<view class="text">
					{{value1.length}} / {{maxLength}}
				</view>
			</view>
		</view>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">2.您的客户群体是谁?</view>
		</view>
		<view class="ipt-box">
			<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value2" placeholder="请输入"
				placeholder-class="placeholder"></textarea>
			<view class="tip">
				<view class="text"></view>
				<view class="text">
					{{value2.length}} / {{maxLength}}
				</view>
			</view>
		</view>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">3.客户痛点是什么?</view>
		</view>
		<view class="ipt-box">
			<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value3" placeholder="请输入"
				placeholder-class="placeholder"></textarea>
			<view class="tip">
				<view class="text"></view>
				<view class="text">
					{{value3.length}} / {{maxLength}}
				</view>
			</view>
		</view>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">4.为您的AI员工介绍一下您的行业优势吧!</view>
		</view>
		<view class="ipt-box">
			<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value4" :placeholder="placeholder4"
				placeholder-class="placeholder"></textarea>
			<view class="tip">
				<view hover-class="h-class" class="text" @click="setVal(4)">
					<!-- 使用案例 -->
				</view>
				<view class="text">
					{{value4.length}} / {{maxLength}}
				</view>
			</view>
		</view>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">5.您通过什么解决方案满足客户需求?</view>
		</view>
		<view class="ipt-box">
			<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value5" :placeholder="placeholder5"
				placeholder-class="placeholder"></textarea>
			<view class="tip">
				<view hover-class="h-class" class="text" @click="setVal(5)">
					<!-- 使用案例 -->
				</view>
				<view class="text">
					{{value5.length}} / {{maxLength}}
				</view>
			</view>
		</view>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">6.有客户自发的在帮您做介绍吗?</view>
		</view>
		<view class="display-a margin-bottom_40rpx">
			<view @click="value6 = '是'" class="font-size-1" :class="value6 == '是' ? 'font-size-3' : 'font-size-2'">是
			</view>
			<view @click="value6 = '否'" class="font-size-1" :class="value6 == '否' ? 'font-size-3' : 'font-size-2'">否
			</view>
		</view>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">7.您每天发布多少条企业内容到公域平台?</view>
		</view>
		<view class="ipt-box">
			<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value7" :placeholder="placeholder7"
				placeholder-class="placeholder"></textarea>
			<view class="tip">
				<view hover-class="h-class" class="text" @click="setVal(7)">
					<!-- 使用案例 -->
				</view>
				<view class="text">
					{{value7.length}} / {{maxLength}}
				</view>
			</view>
		</view>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">8.您的私域池塘里沉淀了多少客户量?</view>
		</view>
		<view class="ipt-box">
			<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value8" :placeholder="placeholder8"
				placeholder-class="placeholder"></textarea>
			<view class="tip">
				<view hover-class="h-class" class="text" @click="setVal(8)">
					<!-- 使用案例 -->
				</view>
				<view class="text">
					{{value8.length}} / {{maxLength}}
				</view>
			</view>
		</view>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">9.您去年新增客户量是多少人?</view>
		</view>
		<view class="ipt-box">
			<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value9" :placeholder="placeholder9"
				placeholder-class="placeholder"></textarea>
			<view class="tip">
				<view hover-class="h-class" class="text" @click="setVal(9)">
					<!-- 使用案例 -->
				</view>
				<view class="text">
					{{value9.length}} / {{maxLength}}
				</view>
			</view>
		</view>
		<view class="display-a margin-bottom_20rpx">
			<view class="r-line"></view>
			<view class="color_FFFFFF font-size_30rpx">10.您去年的营业额是多少?</view>
		</view>
		<view class="ipt-box">
			<textarea class="margin-bottom_40rpx r-input" maxlength="1500" v-model="value10"
				:placeholder="placeholder10" placeholder-class="placeholder"></textarea>
			<view class="tip">
				<view hover-class="h-class" class="text" @click="setVal(10)">
					<!-- 使用案例 -->
				</view>
				<view class="text">
					{{value10.length}} / {{maxLength}}
				</view>
			</view>
		</view>

		<view style="height: 160rpx;"></view>

		<view class="m-but" :class="!isPost ? 'm-but-disable' : ''" @click="postData">精准定位</view>
	</view>
</template>

<script>
	import {
		decodedString
	} from './utils/decodedString.js'
	export default {
		data() {
			return {
				// 用户信息
				userData: '',
				// 内容
				value1: '',
				value2: '',
				value3: '',
				value4: '',
				value5: '',
				value6: '是',
				value7: '',
				value8: '',
				value9: '',
				value10: '',
				// 提示内容
				placeholder4: '请输入',
				placeholder5: '请输入',
				placeholder7: '请输入',
				placeholder8: '请输入',
				placeholder9: '请输入',
				placeholder10: '请输入',
				maxLength: 1500,
				requestList: [],
				// 请求队列管理
				requestQueue: [], // 待处理的请求队列
				activeRequests: 0, // 当前活跃的请求数
				completedRequests: {}, // 已完成的请求记录
				activeRequestTasks: [], // 当前活跃的请求任务数组
				isPageUnloaded: false // 页面是否已卸载的标志
			}
		},
		methods: {
			// 根据type获取对应的属性名
			getTargetPropertyByType(type) {
				// type 1-3 对应 value1-3
				if (type >= 1 && type <= 3) {
					return `value${type}`;
				}
				// type 4-5 对应 placeholder4-5
				else if (type >= 4 && type <= 5) {
					// return `placeholder${type}`;
					return `value${type}`;
				}
				// type 6-9 对应 placeholder7-10
				else if (type >= 6 && type <= 9) {
					// return `placeholder${type + 1}`; // 注意这里的映射关系
					return `value${type + 1}`; // 注意这里的映射关系
				}
				return null;
			},
			startStream(type) {
				let url = this.$api.getAIProject

				// 增加活跃请求计数
				this.activeRequests++;

				let data = {
					uid: uni.getStorageSync('uid'), // 请求参数
					type,
					content: this.userData
				}

				// 根据type获取正确的目标属性名
				const targetProperty = this.getTargetPropertyByType(type);
				if (targetProperty) {
					// 初始化目标属性
					this[targetProperty] = '';
				}

				// 使用流式请求
				const requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						// 处理接收到的数据块
						try {
							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 追加到正确的目标属性
								if (targetProperty) {
									this[targetProperty] += text;
								}
							} else if (typeof data === 'string') {
								// 如果是字符串，直接追加到目标属性
								if (targetProperty) {
									this[targetProperty] += data;
								}
							} else {
								console.warn("收到未知类型的数据块:", data);
							}
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						console.log(`类型${type}请求完成`);
						// 标记请求完成，使用数字类型的键
						this.completedRequests[type] = true;
						// 减少活跃请求计数
						this.activeRequests--;
						// 从活跃请求任务数组中移除
						this.activeRequestTasks = this.activeRequestTasks.filter(task => task.type !== type);
						// 处理队列中的下一个请求
						this.processQueue();
					},
					onError: (err) => {
						uni.hideLoading();
						console.error(`类型${type}流式请求错误:`, err);
						// 减少活跃请求计数
						this.activeRequests--;
						// 从活跃请求任务数组中移除
						this.activeRequestTasks = this.activeRequestTasks.filter(task => task.type !== type);
						// 尽管出错，也继续处理队列
						this.processQueue();
					}
				});

				// 保存请求任务到活跃任务数组
				this.activeRequestTasks.push({
					type: type,
					task: requestTask
				});
			},
			// 使用案例
			setVal(type) {
				return
				// 需要检查对应的placeholder是否已经生成完成
				let requestType = type;

				// 根据type找到对应的请求类型
				// type 4-5 直接对应请求type 4-5
				// type 7-10 对应请求type 6-9
				if (type >= 7 && type <= 10) {
					requestType = type - 1;
				}

				// 检查该请求是否已完成
				if (!this.completedRequests[requestType]) {
					this.$sun.toast('案例正在生成中，请稍后再试', 'none');
					return;
				}

				// 请求已完成，可以使用案例
				this[`value${type}`] = this[`placeholder${type}`]
			},
			// 中止请求
			stopStream() {
				// 取消所有活跃的请求
				if (this.activeRequestTasks.length > 0) {
					console.log(`正在取消${this.activeRequestTasks.length}个活跃请求`);
					this.activeRequestTasks.forEach(item => {
						if (item.task && typeof item.task.cancel === 'function') {
							item.task.cancel();
							console.log(`已取消类型${item.type}的请求`);
						}
					});
					// 清空活跃请求数组
					this.activeRequestTasks = [];
					this.activeRequests = 0;
					console.log('已中止所有请求');
				} else {
					console.log('没有活跃的请求需要取消');
				}
			},
			// 处理请求队列
			processQueue() {
				// 如果页面已卸载，不再处理队列
				if (this.isPageUnloaded) {
					console.log('页面已卸载，终止队列处理');
					return;
				}

				// 检查队列中是否有待处理的请求
				if (this.requestQueue.length === 0) {
					console.log('请求队列为空，没有更多请求需要处理');
					return;
				}

				// 检查当前活跃请求数量
				if (this.activeRequests >= 3) {
					console.log('当前活跃请求已达到上限(3)，等待请求完成');
					return;
				}

				// 从队列中获取下一个待处理的请求
				const nextRequest = this.requestQueue.shift();
				console.log(`开始处理队列中的下一个请求: type=${nextRequest}`);

				// 开始处理这个请求
				this.startStream(nextRequest);
			},
			// 生成定位
			postData() {
				if (this.value1 == '') {
					this.$sun.toast('请输入您的行业', 'none');
					return
				} else if (this.value2 == '') {
					this.$sun.toast('请输入您的客户群体', 'none');
					return
				} else if (this.value3 == '') {
					this.$sun.toast('请输入您的客户痛点', 'none');
					return
				} else if (this.value4 == '') {
					this.$sun.toast('请输入您的行业优势', 'none');
					return
				} else if (this.value5 == '') {
					this.$sun.toast('请输入您的解决方案', 'none');
					return
				} else if (this.value6 == '') {
					this.$sun.toast('请选择您是否有客户自发的在帮您做介绍', 'none');
					return
				} else if (this.value7 == '') {
					this.$sun.toast('请输入您每天发布多少条企业内容到公域平台', 'none');
					return
				} else if (this.value8 == '') {
					this.$sun.toast('请输入您的私域池塘里沉淀了多少客户量', 'none');
					return
				} else if (this.value9 == '') {
					this.$sun.toast('请输入您去年新增客户量是多少人', 'none');
					return
				} else if (this.value10 == '') {
					this.$sun.toast('请输入您去年的营业额', 'none');
					return
				}
				let userData = `行业:${this.value1}
客户群体:${this.value2}
客户痛点:${this.value3}
行业优势:${this.value4}
解决方案:${this.value5}
客户自发的在帮您做介绍:${this.value6}
每天发布多少条企业内容到公域平台:${this.value7}
私域池塘里沉淀了多少客户量:${this.value8}
去年新增客户量是多少人:${this.value9}
去年营业额是多少:${this.value10}`
				uni.setStorageSync('userData', userData)
				uni.navigateTo({
					url: '/subPackages/subPackageA/report?type=2'
				})
			}
		},
		computed: {
			isPost() {
				return this.value1 != '' && this.value2 != '' && this.value3 != '' && this.value4 != '' && this.value5 !=
					'' && this.value6 != '' && this.value7 != '' && this.value8 != '' && this.value9 != '' && this
					.value10 != ''
			}
		},
		onLoad(options) {
			this.userData = uni.getStorageSync('prdjectData')
			if (!this.userData) {
				uni.redirectTo({
					url: '/pages/my/userInfo?zIndex=2'
				})
				return
			}
			this.requestList = []
			console.log(this.userData);

			// 重置页面卸载标志
			this.isPageUnloaded = false;

			// 初始化请求队列 - 所有需要请求的类型
			this.requestQueue = [4, 5, 6, 7, 8, 9]; // 剩余的请求将放入队列
			this.activeRequests = 0;
			this.completedRequests = {};

			// 首先启动前三个请求（类型1-3）
			for (let type = 1; type <= 3; type++) {
				this.startStream(type);
			}
		},
		// 页面卸载时中止所有请求
		onUnload() {
			this.stopStream();
			console.log('页面卸载，中止请求');
			this.isPageUnloaded = true;
		}
	}
</script>

<style lang="scss">
	.m-but {
		position: fixed;
		bottom: 50rpx;
		z-index: 9;
		font-size: 32rpx;
		color: #FFF;
		padding: 30rpx 0;
		width: 710rpx;
		text-align: center;
		border-radius: 10rpx;
		box-shadow: 0px -1px 11px 4px rgba(127, 27, 255, 0.2);
		background: linear-gradient(96.34deg, rgb(210, 174, 255), rgb(154, 93, 228));
	}

	.m-but-disable {
		background: #ccc;
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}

	.font-size-3 {
		background-color: #0084FF;
	}

	.font-size-2 {
		background-color: #343434;
	}

	.font-size-1 {
		width: 184rpx;
		text-align: center;
		border-radius: 10rpx;
		margin-right: 30rpx;
		color: #FFF;
		padding: 20rpx 0;
	}

	.r-top {
		padding: 30rpx 20rpx;
	}

	.placeholder {
		color: #999999;
	}

	.ipt-box {
		position: relative;
		width: 670rpx;

		.tip {
			position: absolute;
			display: flex;
			justify-content: space-between;
			bottom: 0;
			right: -20rpx;
			left: 0;
			color: #ccc;
			z-index: 3;
			padding: 10rpx 20rpx;
			background: #192236;
			border-radius: 0 0 10rpx 10rpx;
		}

		.h-class {
			color: #0084FF;
		}
	}


	.r-input {
		width: 100%;
		padding: 20rpx;
		padding-bottom: 50rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.r-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245), rgb(66, 72, 244) 100%);
		margin-right: 14rpx;
	}
</style>