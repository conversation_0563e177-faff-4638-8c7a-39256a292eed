<template>
	<view>
		
		<view style="height: 30rpx;"></view>
		
		<view class="o-search display-a-jc">
			<view style="width: 80rpx;"></view>
			<icon type="search" size="16"></icon>
			<input type="text" class="o-input" @input="inputChange2" @confirm="confirmChange" v-model="log_no" placeholder="请输入订单编号" placeholder-class="placeholder"/>
		</view>
		
		<view class="display-a margin-bottom_30rpx">
			<block v-for="(item,index) in arr" :key="index">
				<view @click="getTab(item)" class="o-arr" :class="arrId == item.id ? 'o-arr-2' : 'o-arr-1'">{{item.name}}</view>
			</block>
		</view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a padding-bottom_20rpx p-bo margin-bottom_20rpx">
						<view class="color_ABABAB">订单号: {{item.log_no}}</view>
						<view class="o-copy" @click="getCopy(item.log_no)">复制</view>
					</view>
					<view class="display margin-bottom_40rpx">
						<image class="img-pic" :src="item.goods_pic"></image>
						<view style="width: 470rpx;">
							<view class="o-name">{{item.goods_name}}</view>
							<view class="color_cdcdcd display-a margin-bottom_40rpx">
								<view class="font-size_26rpx">已激活: {{item.already}}/{{item.quantity}}</view>
								<view class="font-size_26rpx margin-left-auto">数量: x{{item.count}}</view>
							</view>
							<view class="o-money"><span class="font-size_26rpx">￥</span>{{Number(item.money)}}</view>
						</view>
					</view>
					<view class="display-a">
						<view class="o-but o-but-del" @click="delOrder(item.id,item.goods_name)">删除卡密</view>
						<view class="o-but o-but-see" @click="getSee(item.id,item.goods_name)">查看卡密</view>
					</view>
				</view>
			</block>
		</mescroll-body>
		
		<sunui-tabbar :fixed="true" :current="tabIndex" :types="2" tintColor="#23E9FF" backgroundColor="#323232"></sunui-tabbar>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				tabIndex: 3,
				
				arr: [
					{id:'',name:'全部'},
					{id:2,name:'已完成'},
					{id:1,name:'使用中'},
				],
				arrId: '',
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				log_no: '', //订单编号
				
			}
		},
		
		onLoad() {
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 380;
				},
			})
		},
		
		onShow() {
			this.$nextTick(() => {
				this.mescroll.resetUpScroll();
			});
		},
		
		methods: {
			
			// 输入事件
			inputChange2(e) {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			
			},
			confirmChange(e) {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			//查看
			getSee(id,name) {
				uni.navigateTo({
					url: '/pages/my/partner/orderDetail?logId='+id+'&logName='+name
				})
			},
			
			/*  删除  */
			delOrder(id,name) {
			
				uni.showModal({
					title: '提示',
					content: '确认删除' + name + '该卡密?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.partnerCardLogDel,
								data: {
									partner_id: uni.getStorageSync("partnerId"),
									log_id: id
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				});
			},
			
			//复制
			getCopy(text) {
				if (text) {
					uni.setClipboardData({
						data: text,
						success: () => {
							// 复制成功的回调
							this.$sun.toast('复制成功');
						},
						fail: (err) => {
							console.log("复制失败原因===>",err);
							// 复制失败的回调
							uni.showToast({
								title: '复制失败：'+err,
								icon: 'none'
							});
						}
					});
				}
			},
			
			getTab(obj) {
				this.arrId = obj.id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.partnerCardLogList,
					data: {
						partner_id: uni.getStorageSync("partnerId"),
						log_no: this.log_no,
						is_status: this.arrId,
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.o-but-see {
		border: 1px solid rgb(126, 233, 255);
		color: #7EE9FF;
		margin-left: 40rpx;
	}
	
	.o-but-del {
		border: 1px solid rgb(171, 171, 171);
		color: #ABABAB;
		margin-left: auto;
	}
	
	.o-but {
		width: 170rpx;
		text-align: center;
		padding: 10rpx 0;
		border-radius: 100rpx;
	}
	
	.o-money {
		color: #FF0000;
		font-size: 36rpx;
		font-weight: bold;
	}
	
	.color_cdcdcd {
		color: #CDCDCD;
	}
	
	.o-name {
		font-weight: bold;
		color: #FFF;
		margin-bottom: 20rpx;
		font-size: 32rpx;
	}
	
	.o-copy {
		width: 74rpx;
		text-align: center;
		color: #686767;
		padding: 4rpx 0;
		font-size: 24rpx;
		margin-left: 18rpx;
		background-color: #C4C4C4;
		border-radius: 10rpx;
	}
	
	.color_ABABAB {
		color: #ABABAB;
	}
	
	.img-pic {
		width: 180rpx;
		height: 180rpx;
		margin-right: 20rpx;
	}
	
	.p-bo {
		border-bottom: 1px solid rgb(79, 78, 78);
	}
	
	.list-public {
		background-color: #373737;
	}
	
	.o-arr-2 {
		background-color: #008EFF;
		color: #FFF;
	}
	
	.o-arr-1 {
		background-color: #454545;
		color: #C4BDBD;
	}
	
	.o-arr {
		width: 120rpx;
		text-align: center;
		padding: 6rpx 0;
		border-radius: 10rpx;
		margin-left: 26rpx;
	}
	
	.o-input {
		width: 300rpx;
		margin-left: 10rpx;
	}
	
	.o-search {
		width: 710rpx;
		background-color: #FFF;
		border-radius: 100rpx;
		margin: 0 20rpx 30rpx;
		padding: 20rpx 0;
	}
	
	page {
		background-color: #1D1D1D;
		border-top: 1px solid rgb(56, 56, 56);;
	}
	
</style>
