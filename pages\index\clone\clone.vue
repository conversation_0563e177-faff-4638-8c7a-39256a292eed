<template>
	<view>

		<view class="h_20rpx"></view>

		<view class="display-a color_FFFFFF padding_20rpx margin-bottom_30rpx" v-if="oneLine == 1 && fourLine == 1">
			<view class="font-weight_bold font-size_36rpx">
				训练模式
				<span class="color_FF0000 margin-left_10rpx">*</span>
			</view>
			<view class="display-a margin-left-auto">
				<image @click="getComp(1)" class="img-375" :src="compressed == 1 ? imgUrl+'375.png' : imgUrl+'374.png'">
				</image>
				<view @click="getComp(1)" class="margin-right_40rpx">标清</view>
				<image @click="getComp(2)" class="img-375" :src="compressed == 2 ? imgUrl+'375.png' : imgUrl+'374.png'">
				</image>
				<view @click="getComp(2)">高清</view>
			</view>
		</view>

		<view class="c-frame">

			<view class="font-weight_bold font-size_36rpx margin-bottom_20rpx color_FFFFFF">上传视频<span
					class="color_FF0000 margin-left_10rpx">*</span></view>
			<view class="display-ac-jc" @click="openRequirement()">
				<view class="bg" :style="{'background-image': 'url('+imgUrl+'223.png'+')'}">
					<view class="video-play" v-if="video_url">
						<video class="video-play" style="margin: 18rpx 0 0 10rpx;" :src="video_url"></video>
					</view>
					<view class="display-ac-jc m-pa" v-else>
						<image class="img-226" :src="imgUrl+'226.png'"></image>
						<view class="color_64F2FB">点击上传视频(必选)</view>
						<view class="font-size_24rpx color_999999">'上传一段15秒以上视频，一键合成真人出镜的标清口播视频'</view>
					</view>
				</view>
			</view>

			<view style="height: 270rpx;"></view>

			<view class="post-bott">

				<view class="display-a-jc margin-bottom_20rpx" v-if="cloneSet.protocol_name">
					<image @click="getIsProtocol()" class="img-219"
						:src="isProtocol ? imgUrl+'220.png' : imgUrl+'219.png'">
					</image>
					<view @click="openProtocol()" class="color_CBCACA">同意并且确认<span
							class="color_4BA2FF">《{{cloneSet.protocol_name}}》</span></view>
				</view>
				<view class="c-but" @click="but()">确认上传</view>
				<view class="display-a-jc color_FFFFFF margin-bottom_10rpx">
					<image @click="getRecord()" class="img-229" :src="imgUrl+'229.png'"></image>
					<view @click="getRecord()">克隆记录</view>
					<view class="c-line"></view>
					<image @click="addVideoImg()" class="img-229" :src="imgUrl+'230.png'"></image>
					<view @click="addVideoImg()">重新上传</view>
				</view>

			</view>



		</view>

		<view class="h_20rpx"></view>

		<sunui-popup ref="pop5">
			<template v-slot:content>
				<image class="img-qr-code" :src="customerConfig.customer_qr_code"></image>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop">
			<template v-slot:content>
				<view style="overflow:auto;padding:10rpx 30rpx 20rpx;">
					<scroll-view :scroll-y="true" style="height: 700rpx;">
						<rich-parser :html="cloneSet.protocol" domain="https://6874-html-foe72-1259071903.tcb.qcloud.la"
							lazy-load ref="article" selectable show-with-animation use-anchor>
							<!-- 加载中... -->
						</rich-parser>
					</scroll-view>

					<view class="display-a-js margin-top_20rpx">
						<view class="c-close" @click="close()">关闭</view>
						<view class="c-agree" @click="but()">同意并上传</view>
					</view>

				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop3">
			<template v-slot:content>
				<!-- <pick-color @callback='getPickerColor' /> -->
				<color-picker ref="colorPicker" :color="agbaColor" @close="getClose" @confirm="confirm"></color-picker>
				<view class="h_20rpx"></view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop4">
			<template v-slot:content>
				<view class="pop-bg" :style="{'background-image': 'url('+imgUrl+'224.png'+')'}">
					<image @click="closeRequirement()" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title">数字人视频要求</view>
					<view class="font-size_32rpx margin-bottom_20rpx">
						请在光线清楚的环境下，使用主摄像头，保持镜头平视按下方要求录制上半身和全身视频。
					</view>
					<view class="margin-bottom_20rpx">
						<image class="img-225" mode="widthFix" :src="imgUrl+'326.png'"></image>
					</view>

					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">人脸大小必须小于视频宽度1/2，推荐1/4左右</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">视频全程必须要有人脸和嘴巴、且不得有遮挡</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">请使用原始视频，不要进行二剪增加字幕音乐等</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">视频全程只能有一个人物和嘴型不得多人出镜</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">
							<block v-if="compressed == 1">
								支持MP4/MOV格式，像素480P-1080P以内
							</block>
							<block v-if="compressed == 2">
								支持MP4/MOV格式，像素480P-4K以内
							</block>
						</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">
							<block v-if="compressed == 1">
								上传的视频要求15秒以上，容量100M以内
							</block>
							<block v-if="compressed == 2">
								上传的视频要求15秒以上，容量500M以内
							</block>
						</view>
					</view>
					<view class="display-a-js margin-top_30rpx">
						<view class="c-agree" style="width: 690rpx;" @click="addVideoImg()">立即上传视频</view>
					</view>
				</view>
			</template>
		</sunui-popup>

	</view>
</template>

<script>
	const base64 = require('@/utils/ali-oos/base64.js'); //Base64,hmac,sha1,crypto相关算法
	require('@/utils/ali-oos/hmac.js');
	require('@/utils/ali-oos/sha1.js');
	const Crypto = require('@/utils/ali-oos/crypto.js');
	export default {
		data() {
			return {

				agbaColor: {
					r: 18,
					g: 190,
					b: 119,
					a: 1
				},

				imgUrl: this.$imgUrl,

				system: uni.getStorageSync('system'),

				isMember: uni.getStorageSync("isMember"), //1开启会员

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				isProtocol: false, //

				video_url: '', //视频路径

				name: '', //克隆标题

				speedId: '', //极速形象ID 极速形象不需要审核时用

				is_skip_rs: false,

				video_backgroud_image: '', //背景图片链接

				buttonColor: '', //背景颜色

				uploadUrl: this.$api.upload,

				isWhether: true, //判断重复点击

				cloneSet: uni.getStorageSync("cloneSet"), //克隆开关设置

				setLineList: uni.getStorageSync('indexWay'), //已开启的线路

				upPicUrl2: '',

				progress: 0, //上传视频进度条

				formData: {
					'key': '',
					'policy': '',
					'OSSAccessKeyId': '',
					'signature': '',
					'success_action_status': '200',
				},

				policyText: {
					"expiration": "2030-01-01T12:00:00.000Z", //设置该Policy的失效时间，超过这个失效时间之后，就没有办法通过这个policy上传文件了
					"conditions": [
						["content-length-range", 0, 524288000] // 设置上传文件的大小限制  104857600 209715200
					]
				},

				videoImgWid: 0,
				videoImgHeig: 0,
				vType: 0,



				compressed: '', //1 标清 2高清

				oneLine: '', //1 123号线路 
				fourLine: '', // 1 4号线路

				back_size: '', //1竖 2横

				isKefu: true, //true隐藏 false展开

				customerConfig: uni.getStorageSync('customerConfig'), //客服配置

			}
		},

		onLoad(options) {

			for (let i = 0; i < this.setLineList.length; i++) {
				let setId = this.setLineList[i].id;
				if (setId == 1 || setId == 2 || setId == 3) {
					this.oneLine = 1;
				}
				if (setId == 4) {
					this.fourLine = 1;
				}
			}

			if (options.compressed) {
				this.compressed = options.compressed;
			} else {
				if (this.oneLine == 1 && this.fourLine == 1) {
					this.compressed = 1;
				} else if (this.oneLine == 1) {
					this.compressed = 1;
				} else if (this.fourLine == 1) {
					this.compressed = 2;
				}
			}

			this.getAliyunConfig();
			this.$sun.title("形象克隆");
		},

		onShow() {

		},

		onHide() {

		},

		methods: {

			getComp(type) {
				this.compressed = type;
			},

			//上传要求
			openRequirement() {

				if (this.video_url) {
					return;
				}

				this.$refs.pop4.show({
					style: 'background-color:#222127;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeRequirement() {
				this.$refs.pop4.close();
			},


			getClose() {
				this.$refs.pop3.close();
			},

			open(item) {
				// 打开颜色选择器
				this.$refs.colorPicker.open();
			},
			confirm(e) {
				this.buttonColor = e;
				console.log('颜色选择器返回值：' + e);
				this.$refs.pop3.close();
			},

			//视频/图片上传
			addVideoImg() {

				// 验证后端
				if (!this.upPicUrl2) {
					uni.showToast({
						title: '请配置阿里云',
						icon: 'none'
					});
					return;
				}

				uni.chooseVideo({
					count: 1,
					compressed: false,
					sourceType: ['album'],
					success: res => {
						let file = res.tempFilePath;
						let suffix = 'mp4';
						if (res.tempFilePath) {
							suffix = res.tempFilePath.split(".");
						} else {
							this.$sun.toast("视频资源异常,请重新选择!", 'none');
							return;
						}
						let maxSize = '';
						if (this.compressed == 1) {
							maxSize = 100;
						}
						if (this.compressed == 2) {
							maxSize = 500;
						}
						if (res.size / 1024 / 1024 > maxSize) {
							this.$sun.toast(`视频不能超过${maxSize}M`, 'none');
							return;
						}
						console.log("res.height--->", res.height, res.width);

						this.closeRequirement();
						this.uploadBaseVideo(file, suffix[suffix.length - 1]);

					},
					complete: function() {

					},
					fail: function(err) {
						console.log("uni.chooseVideo err---->", err);
					}
				});

			},

			//上传视频
			uploadBaseVideo(file, suffix) {

				this.video_url = '';

				// 设置一个变量来存储数字
				let count = 0;
				// 设置一个变量来存储定时器
				let timer = null;

				uni.showLoading({
					title: '上传中...' + count + '%',
					mask: true
				});

				// 设置定时器更新数字
				timer = setInterval(() => {
					// count = (count + 1) % 100; // 这里模拟0-99的变化
					uni.showLoading({
						title: `上传中... ${count}%`, // 使用字符串模板并格式化
					});
				}, 300); // 每0.1秒更新一次

				this.formData.key = new Date().getTime() + Math.floor(Math.random() * 150) + '.' + suffix;

				console.log("this.formData.key---->", this.formData.key);

				// 创建上传对象
				const task = uni.uploadFile({
					url: this.upPicUrl2,
					filePath: file,
					fileType: 'video/mp4',
					name: 'file',
					formData: this.formData,
					header: {},
					success: uploadRes => {

						console.log('uploadRes', uploadRes);

						if (uploadRes.statusCode != 200) {
							uni.showToast({
								title: '上传失败 : ' + uploadRes.data,
								icon: 'none'
							});
							clearInterval(timer);
							uni.hideLoading();
						} else {
							count = 100;
							this.video_url = this.upPicUrl2 + '/' + this.formData.key;
							clearInterval(timer);
							uni.hideLoading();
						}
					},
					fail: e => {
						uni.showToast({
							title: '上传失败,' + e,
							icon: 'none'
						});
						clearInterval(timer);
						uni.hideLoading();
					}
				});
				task.onProgressUpdate(res => {
					if (res.progress > 0 && count < 100) {
						count = res.progress;
					}
				});
			},

			/*  阿里云设置  */
			async getAliyunConfig() {
				const result = await this.$http.post({
					url: this.$api.aliyunConfig
				});
				if (result.errno == 0) {
					this.upPicUrl2 = 'https://' + result.data.alioss_domain;
					this.formData.OSSAccessKeyId = result.data.alioss_access_key_id;

					this.formData.policy = base64.encode(JSON.stringify(this.policyText));
					let message = this.formData.policy;
					let bytes = Crypto.HMAC(Crypto.SHA1, message, result.data.alioss_access_key_secret, {
						asBytes: true
					});
					this.formData.signature = Crypto.util.bytesToBase64(bytes);
				}
			},

			//克隆记录
			getRecord() {

				if (uni.getStorageSync('uid')) {

					uni.navigateTo({
						url: '/pages/assets/digital-assets?tabsId=1&tabsNextId=1'
					})

				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								this.navig();
							}
						}
					})
				}
			},

			//协议
			getIsProtocol() {
				this.isProtocol = !this.isProtocol;
			},

			/*  克隆协议  */
			close() {
				this.$refs.pop.close();
			},
			openProtocol() {
				this.isProtocol = true;
				this.$refs.pop.show({
					title: this.cloneSet.protocol_name,
					style: 'background-color:#fff;width:700rpx;border-radius:10rpx;',
					// bottomClose: true,
					shadeClose: false,
				});

			},

			//克隆训练
			async but() {

				if (uni.getStorageSync('uid')) {
					// if (!this.name) {
					// 	this.$sun.toast("请输入标题", 'error');
					// 	return;
					// }

					if (!this.video_url) {
						this.$sun.toast("请先上传视频", 'error');
						return;
					}

					if (!this.isProtocol) {
						this.$sun.toast("请阅读并同意克隆协议", 'error');
						return;
					}

					if (!this.isWhether) {
						return;
					}
					this.isWhether = false;

					this.$refs.pop.close();

					this.getStartTraining();

				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								this.navig();
							}
						}
					})
				}

			},

			//形象开始训练
			async getStartTraining() {

				let getUrl = '';
				let getData = '';

				getUrl = this.$api.uploadAvatar;
				getData = {
					uid: uni.getStorageSync('uid'),
					video_url: this.video_url,
					definition: this.compressed
				}

				const result = await this.$http.post({
					url: getUrl,
					data: getData
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {

						if (this.compressed == 2) {
							uni.navigateTo({
								url: '/pages/assets/digital-assets?tabsId=1&tabsNextId=4'
							})
						} else {
							if (this.setLineList[0].id != 4) {
								uni.navigateTo({
									url: '/pages/assets/digital-assets?tabsId=1&tabsNextId=' + this
										.setLineList[0].id
								})
							} else {
								uni.navigateTo({
									url: '/pages/assets/digital-assets?tabsId=1&tabsNextId=' + this
										.setLineList[1].id
								})
							}
						}

						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

		}
	}
</script>

<style lang="scss">
	.img-375 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 10rpx;
	}

	.img-qr-code {
		width: 500rpx;
		height: 500rpx;
		margin: 50rpx;
	}

	.kefu-2 {
		right: -50rpx;
		opacity: 0.5;
		transition: all 1s linear;
	}

	.kefu-1 {
		right: 10rpx;
		transition: all 1s linear;
	}

	.kefu {
		width: 100rpx;
		height: 100rpx;
		position: fixed;
		z-index: 99;
		// right: -50rpx;
		bottom: 240rpx;
	}

	.video-play {
		width: 480rpx;
		height: 854rpx;
		border-radius: 30rpx;
	}

	.img-229 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 6rpx;
	}

	.color_4BA2FF {
		color: #4BA2FF;
		font-size: 26rpx;
	}

	.color_CBCACA {
		color: #cbcaca;
		font-size: 26rpx;
	}

	.img-219 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 12rpx;
	}

	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 42rpx;
		right: 30rpx;
	}

	.pop-bg {
		position: relative;
		width: 750rpx;
		// height: 912rpx;
		height: 960rpx;
		background-repeat: no-repeat;
		background-size: contain;
		padding: 34rpx 30rpx;
		color: #FFF;
	}

	.img-225 {
		width: 690rpx;
		// height: 211rpx;
	}

	.p-title {
		background: linear-gradient(102.95deg, rgb(29, 130, 255), rgb(226, 79, 250));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-weight: 600;
		letter-spacing: 4%;
		font-size: 40rpx;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.img-227 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
	}

	.m-pa {
		padding: 340rpx 50rpx 0;
	}

	.color_64F2FB {
		color: #64f2fb;
		font-size: 32rpx;
		font-weight: 600;
		margin-bottom: 20rpx;
	}

	.img-226 {
		width: 84rpx;
		height: 84rpx;
	}

	.bg {
		width: 500rpx;
		height: 890rpx;
		background-repeat: no-repeat;
		background-size: contain;
	}

	.color_8B34FF {
		color: #8B34FF;
	}

	.color_A693FF {
		color: #A693FF;
	}

	.img-video {
		width: 710rpx;
		height: 360rpx;
	}

	.upload-but {
		width: 706rpx;
		border: 1px solid #BA76F8;
		border-radius: 10rpx;
		padding: 22rpx 0;
		color: #FFF;
		margin-bottom: 20rpx;
	}

	.img-56 {
		width: 50rpx;
		height: 50rpx;
		margin-right: 10rpx;
	}

	.post-bott {
		position: fixed;
		bottom: 0;
		background-color: #111317;
		width: 750rpx;
		padding: 20rpx 0 30rpx;
		z-index: 9;
	}

	.clone-line {
		width: 8rpx;
		height: 28rpx;
		background: linear-gradient(180.00deg, rgb(187, 119, 248), rgb(135, 80, 242) 100%);
		border-radius: 2rpx;
		margin-right: 10rpx;
	}

	.recommend {
		position: absolute;
		z-index: 9;
		top: -20rpx;
		right: 62rpx;
		width: 60rpx;
		background-color: #FF0000;
		font-size: 24rpx;
		color: #FFF;
		text-align: center;
		padding: 4rpx 0;
		border-radius: 10rpx;
	}

	.look {
		color: #FFF;
		margin-left: 10rpx;
		text-decoration: underline #FFF;
	}

	.c-tips {
		background: #17DA70;
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		margin-right: 12rpx;
	}

	.color_9B9B9B {
		color: #9B9B9B;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}

	.color_4088FF {
		color: #9F57F7;
		font-weight: bold;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}

	.c-line {
		width: 2rpx;
		height: 30rpx;
		border-radius: 100rpx;
		background: #50CBFA;
		margin: 0 50rpx;
		border-radius: 10rpx;
	}

	.c-top2 {
		padding: 30rpx 0;
		// border-bottom: 1px solid rgb(18, 18, 18);;
	}

	.clear-del {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
	}

	.img-67 {
		width: 50rpx;
		height: 50rpx;
		margin: 0 10rpx;
	}

	.sel-color {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		border: 1px dashed rgb(87, 86, 86);
		border-radius: 10rpx;
	}

	.c-agree {
		width: 310rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		color: #FFF;
		font-size: 32rpx;
		padding: 20rpx 0;
	}

	.c-close {
		width: 310rpx;
		text-align: center;
		border: 1px solid rgb(203, 202, 202);
		border-radius: 10rpx;
		background: rgb(255, 255, 255);
		font-size: 32rpx;
		color: #929292;
		padding: 20rpx 0;
	}

	.img-65 {
		width: 42rpx;
		height: 42rpx;
		margin-right: 6rpx;
	}

	.c-but {
		width: 710rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		color: #FFFFFF;
		font-size: 32rpx;
		padding: 26rpx 0;
		margin-bottom: 20rpx;
	}

	.img-58 {
		width: 28rpx;
		height: 28rpx;
		margin-left: 6rpx;
	}

	.img-63 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
	}

	.c-input {
		width: 670rpx;
		// background-color: #434343;
		background-color: #2A2A2A;
		border-radius: 10rpx;
		padding: 20rpx;
		// margin: 20rpx 0;
		margin-bottom: 20rpx;
		color: #FFF;
	}

	.placeholder {
		color: #979797;
	}

	.c-frame {
		width: 710rpx;
		margin: 0 20rpx;
		border-radius: 10rpx;
		// background: linear-gradient(180.00deg, rgb(36, 45, 51), rgb(0, 0, 0) 100%);
		background-color: #111317;
		// padding: 20rpx;
	}

	.upload-i {
		width: 296rpx;
		text-align: center;
		border-radius: 100rpx;
		background: #fff;
		padding: 20rpx 0;
		color: #333333;
	}

	.upload-v {
		width: 296rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(102, 214, 253) 0.763%, rgb(70, 75, 253) 100%);
		padding: 20rpx 0;
		color: #FFFFFF;
		font-size: 32rpx;
	}

	.c-top {
		width: 600rpx;
		border-radius: 100rpx;
		background-color: #FFFFFF;
		padding: 2rpx;
		margin: 0 74rpx 40rpx;
	}

	page {
		border-top: none;
		background-color: #111317;
	}
</style>