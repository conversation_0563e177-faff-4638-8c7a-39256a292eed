<template>
	<view>

		<block v-if="step == 1">
			
			<view class="font-bott-color">任务名称<span class="red-dot">*</span></view>
			<input type="text" class="input-name" v-model="obj.name" placeholder="请输入任务名称"
				placeholder-class="color_8b8b8b" />

		
			<view class="font-bott-color">选择素材<span class="red-dot">*</span></view>
			<view class="display-a input-name" style="width: 710rpx;" @click="selVideo()">
				<input type="text" style="width: 640rpx;" v-model="obj.projectName" disabled
					placeholder="请选择素材文件夹" placeholder-class="color_8b8b8b" />
				<image class="matrix-20" :src="imgUrl+'21.png'"></image>
			</view>
			
			<block v-if="obj.projectName">
				<view class="font-bott-color">视频数量<span class="red-dot">*</span></view>
				<view class="input-name" style="width: 710rpx;">{{obj.numberCount?obj.numberCount:0}}</view>
			</block>
			
			<view class="font-bott-color">选择标题<span class="red-dot">*</span></view>
			<view class="display-a input-name" style="width: 710rpx;" @click="getTitle(2)">
				<input type="text" style="width: 640rpx;" v-model="obj.seoName" disabled placeholder="请选择标题"
					placeholder-class="color_8b8b8b" />
				<image class="matrix-20" :src="imgUrl+'21.png'"></image>
			</view>

			<view v-if="type == 1 || type == 2" class="color_FFFFFF">
				<view class="font-bott-color">挂载类型</view>
				<block v-if="type == 1">
					<view class="display-a margin-bottom_30rpx">
						<image class="matrix-13" @click="selMountType(1)"
							:src="obj.linkType == 1 ? imgUrl+'384.png' : imgUrl+'383.png'">
						</image>
						<view style="margin-right: 28rpx;" @click="selMountType(1)">商家POI</view>
						<!-- <image class="matrix-13" @click="selMountType(2)"
							:src="obj.linkType == 2 ? imgUrl+'384.png' : imgUrl+'383.png'">
						</image>
						<view style="margin-right: 28rpx;" @click="selMountType(2)">小程序</view> -->
						
						<image class="matrix-13" @click="selMountType(3)"
							:src="obj.linkType == 3 ? imgUrl+'384.png' : imgUrl+'383.png'">
						</image>
						<view @click="selMountType(3)">不挂载</view>
					</view>

					<block v-if="obj.linkType == 1">
						<view class="margin-bottom_30rpx">
							<view class="font-bott-color">选择城市<span class="red-dot">*</span></view>
							<view class="display-a input-name" style="width: 710rpx;" @click="handleRegion()">
								<input type="text" v-model="cityName" style="width: 640rpx;" disabled
									placeholder="请先选择城市" placeholder-class="color_8b8b8b" />
								<image class="matrix-20" :src="imgUrl+'21.png'"></image>
							</view>
						</view>
						<view class="margin-bottom_30rpx">
							<view class="font-bott-color">商家简称<span class="red-dot">*</span></view>
							<view class="display-a input-name" style="width: 710rpx;padding: 0 0 0 20rpx;">
								<input type="text" style="width: 590rpx;" v-model="keyword" placeholder="请输入商家简称搜索POI地址"
									placeholder-class="color_8b8b8b" />
								<image class="img-385" :src="imgUrl+'385.png'" @click="getPoiList()"></image>
							</view>
						</view>
						<view class="font-bott-color">POI地址<span class="red-dot">*</span></view>
						<picker mode="selector" @change="bindPickerChange3" :range="poiList" :range-key="'poi_name'">
							<view class="display-a input-name" style="width: 710rpx;">
								<input type="text" style="width: 640rpx;" v-model="obj.poiName" disabled
									placeholder="请选择POI地址" placeholder-class="color_8b8b8b" />
								<image class="matrix-20" :src="imgUrl+'21.png'"></image>
							</view>
						</picker>
					</block>

					<block v-if="obj.linkType == 2">
						<view class="margin-bottom_30rpx">
							<view class="font-bott-color">D音小程序APPID<span class="red-dot">*</span></view>
							<input type="text" class="input-name" v-model="obj.anchorId" placeholder="D音小程序APPID"
								placeholder-class="color_8b8b8b" />
						</view>
						<view class="margin-bottom_30rpx">
							<view class="font-bott-color">D音小程序名称<span class="red-dot">*</span></view>
							<input type="text" class="input-name" v-model="obj.anchorTitle" placeholder="D音小程序名称"
								placeholder-class="color_8b8b8b" />
						</view>
						<view class="font-bott-color">D音小程序URL<span class="red-dot">*</span></view>
						<input type="text" class="input-name" v-model="obj.anchorUrl" placeholder="D音小程序URL"
							placeholder-class="color_8b8b8b" />
					</block>

					
				</block>
				<block v-if="type == 2">
					<view class="display-a margin-bottom_30rpx">
						<image class="matrix-13" @click="selMountType2(2)"
							:src="obj.mountType == 2 ? imgUrl+'384.png' : imgUrl+'383.png'">
						</image>
						<view style="margin-right: 28rpx;" @click="selMountType2(2)">小程序</view>
						<image class="matrix-13" @click="selMountType2(3)"
							:src="obj.mountType == 3 ? imgUrl+'384.png' : imgUrl+'383.png'">
						</image>
						<view @click="selMountType2(3)">不挂载</view>
					</view>
					<block v-if="obj.mountType == 2">
						<view class="margin-bottom_30rpx">
							<view class="font-bott-color">K手小程序APPID<span class="red-dot">*</span></view>
							<input type="text" class="input-name" v-model="obj.anchorId" placeholder="K手小程序APPID"
								placeholder-class="color_8b8b8b" />
						</view>
						<view class="margin-bottom_30rpx">
							<view class="font-bott-color">K手小程序名称<span class="red-dot">*</span></view>
							<input type="text" class="input-name" v-model="obj.anchorTitle" placeholder="K手小程序名称"
								placeholder-class="color_8b8b8b" />
						</view>
						<view class="margin-bottom_30rpx">
							<view class="font-bott-color">K手小程序URL<span class="red-dot">*</span></view>
							<input type="text" class="input-name" v-model="obj.anchorUrl" placeholder="K手小程序URL"
								placeholder-class="color_8b8b8b" />
						</view>
						<view class="font-bott-color">挂载小黄车<span class="red-dot">*</span></view>
						<input type="text" class="input-name" v-model="obj.littleYellowCarId"
							placeholder="请输入挂载小黄车的商品ID" placeholder-class="color_8b8b8b" />
						<view class="color_1E6CEB margin-top_10rpx font-size_24rpx" @click="getA()">如何获取K手小黄车的商品ID?
						</view>
					</block>
				</block>
			</view>
			
			<view class="color_FFFFFF" v-if="type == 4">
				
				<view class="font-bott-color">选择文案<span class="red-dot">*</span></view>
				<view class="display-a input-name" style="width: 710rpx;" @click="getTitle(1)">
					<!-- <textarea style="width: 640rpx;" class="margin-bottom_40rpx" v-model="obj.copywiringName" placeholder="请选择文案" placeholder-class="color_8b8b8b"></textarea> -->
					<input type="text" style="width: 640rpx;" v-model="obj.copywiringName" disabled
						placeholder="请选择文案" placeholder-class="color_8b8b8b" />
					<image class="matrix-20" :src="imgUrl+'21.png'"></image>
				</view>
				
				<view class="font-bott-color">商品ID</view>
				<input type="text" class="input-name" v-model="obj.goods_id" placeholder="请输入商品ID"
					placeholder-class="color_8b8b8b" />
				
				<!-- <view class="margin-bottom_30rpx">
					<view class="font-bott-color">话题简称</view>
					<view class="display-a input-name" style="width: 710rpx;padding: 0 0 0 20rpx;">
						<input type="text" style="width: 590rpx;" v-model="obj.topic_name" placeholder="请输入话题简称后点击搜索"
							placeholder-class="color_8b8b8b" />
						<image class="img-385" :src="imgUrl+'385.png'" @click="getTopicList()"></image>
					</view>
				</view>
				<view class="font-bott-color">选择话题<span class="red-dot">*</span></view>
				<picker mode="selector" @change="bindPickerChange5" :range="topicList" :range-key="'poi_name'">
					<view class="display-a input-name" style="width: 710rpx;">
						<input type="text" style="width: 640rpx;" v-model="obj.poiName" disabled
							placeholder="选择话题" placeholder-class="color_8b8b8b" />
						<image class="matrix-20" :src="imgUrl+'21.png'"></image>
					</view>
				</picker> -->
				
			</view>
			
			<view style="height: 160rpx;"></view>
			<view class="next" @click="getStep(2)">下一步</view>
			
		</block>

		<block v-if="step == 2">
			<view class="font-bott-color">发布时间<span class="red-dot">*</span></view>
			<view class="display-a margin-bottom_30rpx color_FFFFFF">
				<image class="matrix-13" @click="selPublishTimeType(1)"
					:src="obj.publishTimeType == 1 ? imgUrl+'384.png' : imgUrl+'383.png'">
				</image>
				<view style="margin-right: 120rpx;" @click="selPublishTimeType(1)">随机</view>
				<image class="matrix-13" @click="selPublishTimeType(2)"
					:src="obj.publishTimeType == 2 ? imgUrl+'384.png' : imgUrl+'383.png'">
				</image>
				<view @click="selPublishTimeType(2)">指定</view>
			</view>

			<block v-if="obj.publishTimeType == 1">
				<view class="margin-bottom_30rpx">
					<view class="font-bott-color">开始发布日期<span class="red-dot">*</span></view>
					<picker mode="date" fields="day" :value="obj.startDate" :start="startDate" :end="endDate"
						@change="bindDateChange">
						<view class="display-a input-name" style="width: 710rpx;">
							<input type="text" v-model="obj.startDate" style="width: 680rpx;" disabled
								placeholder="请选择开始发布日期" placeholder-class="color_8b8b8b" />
							<image class="matrix-20" :src="imgUrl+'21.png'"></image>
						</view>
					</picker>
				</view>
				<view class="margin-bottom_30rpx">
					<view class="font-bott-color">每天发布数量<span
							class="color_999999 font-size_24rpx margin-left_10rpx">(最大值35)</span><span
							class="red-dot">*</span></view>
					<input type="number" maxlength="2" class="input-name" v-model="obj.dayCount"
						placeholder="请输入每天发布数量(最大值35)" placeholder-class="color_8b8b8b" />
				</view>
				<view class="margin-bottom_30rpx">
					<view class="font-bott-color">发布天数<span class="red-dot">*</span></view>
					<input type="number" class="input-name" v-model="obj.publishDays" placeholder="请输入发布天数"
						placeholder-class="color_8b8b8b" />
				</view>
			</block>
			<view class="margin-bottom_30rpx">
				<view class="font-bott-color">账户分组<span class="red-dot">*</span></view>
				<picker mode="selector" @change="bindPickerChange4" :range="accountGroupList"
					:range-key="'name'">
					<view class="display-a input-name" style="width: 710rpx;">
						<input type="text" style="width: 680rpx;" v-model="obj.accountGroupName" disabled
							placeholder="请选择账户分组" placeholder-class="color_8b8b8b" />
						<image class="matrix-20" :src="imgUrl+'21.png'"></image>
					</view>
				</picker>
			</view>
			<view class="display-a">
				<view class="font-size_32rpx color_FFFFFF">选择账户<span class="red-dot">*</span></view>
				<view class="sel-account" @click="getSelAccount()">请选择账户</view>
			</view>

			<view class="acc-group" v-if="groupArr.length > 0">
				<view class="display-fw-js">
					<block v-for="(item,index) in groupArr" :key="index">
						<view class="frame-data">
							<view class="display">
								<image class="img-head" :src="item.avatar"></image>
								<image class="matrix-23" @click="delGroup(index)" :src="imgUrl+'60.png'">
								</image>
							</view>
							<view class="frame-name font-overflow">{{item.account_name}}</view>
							<view class="display-a-jc" @click="getBatch(index)" v-if="obj.publishTimeType == 2">
								<image class="matrix-22" :src="imgUrl+'229.png'"></image>
								<view class="color_00FFCA font-size_24rpx" v-if="item.publishTime">{{item.publishTime}}
								</view>
								<view class="color_00FFCA font-size_26rpx" v-else>请指定时间</view>
							</view>
						</view>
					</block>
				</view>
			</view>

			<view style="height: 140rpx;"></view>

			<view class="bott-but display-a-js">
				<view class="bott-back" @click="getStep(1)">上一步</view>
				<view class="bott-release" @click="releaseBut()">
					完成发布
					<block v-if="type == 1">
						<span class="font-size_24rpx margin-left_10rpx">转发一个视频扣{{tallySetObj.douyin_video}}点</span>
					</block>
					<!-- <block v-if="type == 2">
						<span class="font-size_24rpx margin-left_10rpx">转发一个视频扣{{tallySetObj.kuaishou_video}}点</span>
					</block> -->
					<block v-if="type == 3">
						<span class="font-size_24rpx margin-left_10rpx">转发一个视频扣{{tallySetObj.shipinghao_video}}点</span>
					</block>
					<block v-if="type == 4">
						<span class="font-size_24rpx margin-left_10rpx">转发一个视频扣{{tallySetObj.xiaohongshu_video}}点</span>
					</block>
				</view>
			</view>

		</block>

		<sunui-popup ref="pop2">
			<template v-slot:content>
				<view class="display-a-js padding_30rpx">
					<view></view>
					<view class="color_FF0000" @click="closeBatch()">关闭</view>
				</view>
				<block v-if="groupArrIndex > -1">
					<view class="tabs-2 margin-bottom_20rpx">
						<scroll-view id="tab-bar" class="scroll-h" :scroll-x="true" :show-scrollbar="false">
							<view v-for="(item,index) in groupArr[groupArrIndex].futureDate" :key="index"
								class="uni-tab-item" @click="selTab(index)">
								<view
									:class="futureDateIndex == index ? 'uni-tab-item-title-active' : 'uni-tab-item-title'">
									{{item.name}}
								</view>
							</view>
						</scroll-view>
					</view>
					<view class="display-fw-a margin-bottom_30rpx">
						<block v-for="(items,indexs) in groupArr[groupArrIndex].futureDate[futureDateIndex].timeList"
							:key="indexs">
							<view @click="selTime(items)" :key="updateKey" class="time-label"
								:style="JSON.parse(groupArr[groupArrIndex].selFutureDate)[futureDateIndex].timeList.indexOf(items) == -1 ? '' : 'color:#0084FE;background: #E1EDFF;'">
								{{items}}
							</view>
						</block>
					</view>
					<view class="display-a">
						<view class="display-a-jc select-all" @click="getAllSel()">
							<image class="matrix-15"
								:src="allSel ? imgUrl+'384.png' : imgUrl+'383.png'"></image>
							<view class="font-size_32rpx color_000000">全选</view>
						</view>
						<view class="save-set" @click="saveSet()">保存设置</view>
					</view>
				</block>
				
			</template>
		</sunui-popup>
		
		<lb-picker ref="picker2" v-model="value2" mode="multiSelector" confirmColor="#6568F6" :list="regionList"
			:props="factoryProp" :level="2" @change="handleChange" @confirm="handleConfirm" @cancel="handleCancel">
			<template>
				<view slot="action-center">选择城市</view>
			</template>
		</lb-picker>

	</view>
</template>

<script>
	import regionList from '@/utils/region-picker.js';
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置
				
				// type: '', //1抖音H5发布 3 D音代用户发布 4快手 5.B站
				type: '', //1。D音 2。K手 3。视频号 4.小红薯 5.B站

				step: 1, //步骤

				//城市
				value2: [],
				regionList: regionList.zones_tree,
				factoryProp: {
					label: 'name',
					value: 'id'
				},

				//抖音POI
				keyword: '', //关键字
				city: '', //城市
				cityName: '',
				poiList: [], //poi
				
				topicList: [], //话题列表

				accountGroupList: [], //账户分组列表

				//指定时间数据 sta

				groupArrInit: [], //初始已选账户列表
				groupArr: [], //已选账户列表
				groupArrIndex: -1,

				scheduleTimes: [], //最终账户日期时间数据
				futureDateIndex: 0,
				
				bilTypeList: [], //B站分区列表
				dateIndex: [0, 0],	//预约时间选择

				allSel: false, //是否全选
				
				updateKey: false,

				//指定时间数据 end

				isWhether: true, //判断重复点击

				dayData: ['07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
					'12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30',
					'17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30',
					'22:00', '22:30', '23:00', '23:30', '24:00'
				],

				obj: {
					name: '', //任务名称
					projectId: '', //
					projectName: '', //素材名称
					numberCount: '', //视频数量
					
					seoId: [], //标题ID
					seoName: '', //标题名称
					copywiring: '', //文案ID
					copywiringName: '', //文案名称
					
					linkType: 3, //抖音挂载链接类型 1POI 2小程序  3不挂载
					poiId: '',
					poiName: '', //
					anchorUrl: '', //小程序地址
					anchorTitle: '', //小程序标题
					anchorId: '', //小程序ID
					
					goods_id: '', //商品ID  小红书
					topic_name: '', //话题简称
					topic_list: '',
					
					publishTimeType: 1, //发布时间类型 1随机 2指定时间
					startDate: null, //开始时间	最早第二天开始
					dayCount: '', //日发送量		最多35条
					publishDays: '', //发送天数  最多30天
					accountGroup: '', //发布账号分类ID
					accountGroupName: '', //发布账号分类名称
					mountType: 3, //2小程序 3不挂载
					littleYellowCarId: '', //快手小黄车ID
				},

			}
		},

		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},

		onLoad(options) {
			if (options.type) {
				this.type = options.type;
				if (this.type == 1) {
					this.$sun.title("D音发布");
				}
				if (this.type == 2) {
					this.$sun.title("K手发布");
				}
				if (this.type == 3) {
					this.$sun.title("视频号发布");
				}
				if (this.type == 4) {
					this.$sun.title("小红薯发布");
				}
				if (this.type == 5) {
					this.$sun.title("B站发布");
				}
				
				this.getAccountGroupList();
			}
		},

		onShow() {

		},

		methods: {
			
			//选中的标题/文案  type 1 文案 2 标题
			otherVideoTitleFun(obj,type) {
				if (type == 2) {
					this.obj.seoId = obj; //标题ID
					// this.obj.seoName = obj.answer; //标题名称
					this.obj.seoName = '已选'+this.obj.seoId.length+'条'; //标题名称
				}
				if (type == 1) {
					this.obj.copywiring = obj.id; //文案ID
					this.obj.copywiringName = obj.name; //文案名称
				}
			},
			
			//选择标题
			getTitle(type) {
				uni.navigateTo({
					url: '/pages/matrix/videoTitle?type='+type+'&tabId='+this.type
				})
			},
			
			//选中的视频素材
			otherVideoFun(obj) {
				this.obj.projectId = obj.id; //
				this.obj.projectName = obj.title; //素材名称
				this.obj.numberCount = obj.video_count; //视频数量
			},
			
			//选择视频素材
			selVideo() {
				uni.navigateTo({
					url: '/pages/matrix/videoSource?projectId='+this.obj.projectId+'&type='+this.type
				})
			},
			
			//预约时间
			setTime() {
				let range = [
					[],
					[]
				];
				let timeList = this.bilTypeList;
				timeList.forEach(el => {
					range[0].push({
						name: el.name
					});
				});
				timeList[this.dateIndex[0]].children.forEach(el => {
					console.log('el', el);
					range[1].push({
						name: el.name,
						id: el.id
					});
				});
				// 更新数据
				this.dateList = range;
				this.$forceUpdate();
			},
			
			
			//删除选中账户
			delGroup(index) {
				uni.showModal({
					title: '提示',
					content: '确认删除该账户?',
					success: async (res) => {
						if (res.confirm) {
							this.groupArr.splice(index, 1);
							this.groupArrInit.splice(index, 1);
							if (this.obj.publishTimeType == 2) {
								this.saveSet();
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			
			//保存账户指定发布时间设置
			saveSet() {

				this.scheduleTimes = [];
				let dateTimeList = [];
				for (let i = 0; i < this.groupArr.length; i++) {
					let arrObj = JSON.parse(this.groupArr[i].selFutureDate);
					for (let k = 0; k < arrObj.length; k++) {
						for (let h = 0; h < arrObj[k].timeList.length; h++) {
							let timeName = arrObj[k].name;
							let dateTimeObj = {};
							
							dateTimeObj = {
								dyAccountId: this.groupArr[i].id,
								// dyPublishId: '',
								productPolderId: this.obj.projectId,
								publishTime: timeName + ' ' + arrObj[k].timeList[h] + ':00',
							}
							
							// if (this.type == 4) {
							// 	dateTimeObj = {
							// 		releaseTime: arrObj[k].timeList[h].replace(":", "-"),
							// 		releaseDate: timeName.substring(5, 10),
							// 		publishTime: timeName + ' ' + arrObj[k].timeList[h],
							// 	}
							// }
							// if (this.type == 5) {
							// 	dateTimeObj = {
							// 		blblAccountId: this.groupArr[i].id,
							// 		blblPublishId: '',
							// 		projectId: this.obj.projectId,
							// 		releaseTime: arrObj[k].timeList[h].replace(":", "-"),
							// 		releaseDate: timeName.substring(5, 10),
							// 		publishTime: timeName + ' ' + arrObj[k].timeList[h],
							// 	}
							// }
							const keys = Object.keys(this.groupArrInit[i]);
							for (let key of keys) {
								dateTimeObj[key] = this.groupArrInit[i][key];
							}
							dateTimeList.push(dateTimeObj);
						}
					}
				}

				for (let t = 0; t < dateTimeList.length; t++) {
					if (dateTimeList[t].id == this.groupArr[this.groupArrIndex].id) {
						this.groupArr[this.groupArrIndex].publishTime = dateTimeList[t].publishTime;
						break;
					} else {
						this.groupArr[this.groupArrIndex].publishTime = '';
					}
				}

				if (dateTimeList.length == 0) {
					this.groupArr[this.groupArrIndex].publishTime = '';
				}

				this.scheduleTimes = dateTimeList;

				this.futureDateIndex = 0;
				this.$refs.pop2.close();
				this.$set(this.scheduleTimes, this.scheduleTimes, dateTimeList);
				this.$set(this.groupArr[this.groupArrIndex], this.groupArr[this.groupArrIndex].publishTime, this.groupArr[
					this.groupArrIndex].publishTime);
				this.$forceUpdate();
			},

			//全选
			getAllSel() {
				this.allSel = !this.allSel;
				this.groupArr[this.groupArrIndex].selFutureDate = JSON.parse(this.groupArr[this.groupArrIndex]
					.selFutureDate);
				let getDateList = this.groupArr[this.groupArrIndex].futureDate[this.futureDateIndex].timeList;
				this.groupArr[this.groupArrIndex].selFutureDate[this.futureDateIndex].timeList = [];
				if (this.allSel) {
					for (let i = 0; i < getDateList.length; i++) {
						this.groupArr[this.groupArrIndex].selFutureDate[this.futureDateIndex].timeList.push(getDateList[
							i]);
					}
				}
				this.$set(this.groupArr[this.groupArrIndex], this.groupArr[this.groupArrIndex].selFutureDate[this
					.futureDateIndex].timeList, this.groupArr[this.groupArrIndex].selFutureDate[this
					.futureDateIndex].timeList);
				this.groupArr[this.groupArrIndex].selFutureDate = JSON.stringify(this.groupArr[this.groupArrIndex]
					.selFutureDate);
				this.$forceUpdate();
			},

			//选中时间
			selTime(time) {
				
				this.updateKey = false;
				
				this.groupArr[this.groupArrIndex].selFutureDate = JSON.parse(this.groupArr[this.groupArrIndex]
					.selFutureDate);
				
				let getDateList = this.groupArr[this.groupArrIndex].futureDate[this.futureDateIndex].timeList;
				let isBoo = this.groupArr[this.groupArrIndex].selFutureDate[this.futureDateIndex].timeList.indexOf(time);
				if (isBoo == -1) {
					this.groupArr[this.groupArrIndex].selFutureDate[this.futureDateIndex].timeList.push(time);
					if (Number(getDateList.length == this.groupArr[this.groupArrIndex].selFutureDate[this.futureDateIndex]
							.timeList.length)) {
						this.allSel = true;
					}
				} else {
					this.groupArr[this.groupArrIndex].selFutureDate[this.futureDateIndex].timeList.splice(isBoo, 1);
					this.allSel = false;
				}
				
				
				this.groupArr[this.groupArrIndex].selFutureDate = JSON.stringify(this.groupArr[this.groupArrIndex].selFutureDate);
				this.updateKey = true;
				
			},

			//日期导航
			selTab(index) {
				this.futureDateIndex = index;
				let getTimeList = JSON.parse(this.groupArr[this.groupArrIndex].selFutureDate)[this.futureDateIndex]
					.timeList;
				let getDateList = this.groupArr[this.groupArrIndex].futureDate[this.futureDateIndex].timeList;
				if (getTimeList.length == getDateList.length) {
					this.allSel = true;
				} else {
					this.allSel = false;
				}
				this.$forceUpdate();
			},

			// '随机'任务账户发布时间
			randomInfo() {
				
				// 清空总数据，重新计算
				this.scheduleTimes = [];
				// 获取后N天日期 ‘发布天数’计算后年月日 dateList:年月日数组
				let randomDateList = this.getLastOrNextFewDateBy(this.obj.startDate, this.obj.publishDays)
				
				// 集合 年月日 + 时分秒
				const timeList = []

				/*修改111 start*/
				// 测试olds_原始数组
				for (let i = 0; i < this.groupArr.length; i++) { // 修改1
					randomDateList.forEach(item => {
						// randomNum: 根据'每天发布数量' ，获取随机的时间time 例如：12:00:00
						let randomList = this.randomNum(this.dayData, this.obj.dayCount)
						randomList.forEach(child => {
							timeList.push(item + ' ' + child + ':00') // 增加秒
						})
					})
				}

				// new_目标数组
				const timeList2 = []
				for (let i = 0; i < this.groupArr.length; i++) { // 修改1
					let account = this.groupArr[i]
					randomDateList.forEach((item, j) => {
						// randomNum: 根据'每天发布数量' ，获取随机的时间time 例如：12:00:00
						let randomList = this.randomNum(this.dayData, this.obj.dayCount)
						// 发布时间
						randomList.forEach(child => {
							// if (this.type == 1 || this.type == 3) {
								account.productPolderId = this.obj.projectId; //选择文件夹id
								account.dyAccountId = account.id;
								// account.dyPublishId = '';
								account.publishTime = '';
							// }
							// if (this.type == 4) {
							// 	account.releaseTime = '';
							// 	account.releaseDate = '';
							// 	account.publishTime = '';
							// }
							
							timeList2.push(account) // 增加秒
						})
					})
				}

				const originalArray = JSON.parse(JSON.stringify(timeList2));
				// 目标数组
				const targetArray = timeList
				// 使用 map() 方法将原始数组的字段赋值给目标数组
				targetArray.map((item, j) => {
					
					// if (this.type == 1 || this.type == 3) {
						originalArray[j].publishTime = (item);
					// }
					// if (this.type == 4) {
					// 	originalArray[j].publishTime = item.substring(0, 16);
					// 	originalArray[j].releaseDate = item.substring(5, 10);
					// 	originalArray[j].releaseTime = item.substring(11, 16).replace(":", "-");
					// }
				});
				
				console.log("随机时间===>",originalArray);
				
				this.scheduleTimes = originalArray;
				return true;
			},

			//获取后七天的日期
			getDayDate() {
				let futureList = [];
				let selFutureList = [];
				let getFutureDate = this.timeForMat(7);
				for (let i = 0; i < getFutureDate.length; i++) {
					let dataObj = {
						name: getFutureDate[i],
						timeList: this.dayData,
					}
					let dataObj2 = {
						name: getFutureDate[i],
						timeList: [],
					}
					futureList.push(dataObj);
					selFutureList.push(dataObj2);
				}
				let futureObj = {
					futureList: futureList,
					selFutureList: selFutureList,
				}
				return futureObj;
			},

			//指定时间
			getBatch(index) {
				this.groupArrIndex = index;
				this.$refs.pop2.show({
					style: 'background-color:#fff;width:750rpx;height:auto;border-radius: 20px 20px 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},
			closeBatch() {
				this.futureDateIndex = 0;
				this.$refs.pop2.close();
			},

			otherFun(groupArr) {
				let getFutureObj = this.getDayDate();
				this.groupArr = JSON.parse(groupArr); //已选账户列表
				this.groupArrInit = JSON.parse(groupArr); //初始已选账户列表
				//指定发布时间
				if (this.obj.publishTimeType == 2) {
					for (let i = 0; i < this.groupArr.length; i++) {
						this.groupArr[i].futureDate = getFutureObj.futureList;
						if (this.groupArr[i].selFutureDate) {
					
						} else {
							//JSON.stringify()防止数组统一改变
							this.groupArr[i].selFutureDate = JSON.stringify(getFutureObj.selFutureList);
						}
					}
				}
				//随机发布时间
				// if (this.obj.publishTimeType == 1) {
				// 	// this.randomInfo();
				// }
				
			},

			//选择账户
			getSelAccount() {

				if (this.obj.publishTimeType == 1) {
					if (!this.obj.startDate) {
						this.$sun.toast("请选择开始发布日期", 'none');
						return;
					}
					if (!this.obj.dayCount) {
						this.$sun.toast("请输入每天发布数量", 'none');
						return;
					} else {
						if (Number(this.obj.dayCount) > 35) {
							this.$sun.toast("每天发布数量最多35次", 'none');
							return;
						}
					}
					if (!this.obj.publishDays) {
						this.$sun.toast("请输入发布天数", 'none');
						return;
					}
				}

				if (!this.obj.accountGroup) {
					this.$sun.toast("请选择账户分组", 'none');
					return;
				}

				let searchObj = {
					selLabel: this.type,
					groupId: this.obj.accountGroup,
					groupName: this.obj.accountGroupName,
					groupArr: this.groupArr,
				}

				uni.navigateTo({
					url: '/pages/matrix/selectAccount?obj=' + JSON.stringify(searchObj)
				})


			},

			getA() {
				window.location.href =
					'https://open.kwaixiaodian.com/docs/api?categoryId=44&apiName=open.item.list.get&version=1';
			},

			//数组去重
			removeDuplicate(arr) {
				let arr2 = arr;
				let len = arr2.length
				for (let i = 0; i < len; i++) {
					for (let j = i + 1; j < len; j++) {
						if (arr2[i].id == arr2[j].id) {
							arr2.splice(j, 1)
							len-- // 减少循环次数提高性能
							j-- // 保证j的值自加后不变
						}
					}
				}
				return arr2
			},

			//发布任务
			releaseBut() {
				
				if (!this.obj.name) {
					this.$sun.toast("请输入任务名称", 'none');
					return;
				}
				if (!this.obj.projectId) {
					this.$sun.toast("请选择素材", 'none');
					return;
				}
				if (!this.obj.numberCount) {
					this.$sun.toast("选择的素材没有视频，请重新选择素材", 'none');
					return;
				}
				
				
				if (this.type == 1) {
					if (this.obj.seoId.length == 0) {
						this.$sun.toast("请选择标题", 'none');
						return;
					}
					if (this.obj.linkType == 1) {
						if (!this.city) {
							this.$sun.toast("请选择城市", 'none');
							return;
						}
						if (!this.keyword) {
							this.$sun.toast("请输入商家简称", 'none');
							return;
						}
						if (!this.obj.poiId) {
							this.$sun.toast("请选择POI地址", 'none');
							return;
						}
					}
					if (this.obj.linkType == 2) {
						if (!this.obj.anchorId) {
							this.$sun.toast("请输入D音小程序APPID", 'none');
							return;
						}
						if (!this.obj.anchorTitle) {
							this.$sun.toast("请输入D音小程序名称", 'none');
							return;
						}
						if (!this.obj.anchorUrl) {
							this.$sun.toast("请输入D音小程序URL", 'none');
							return;
						}
					}
					
				}
				
				if (this.type == 2) {
					if (this.obj.seoId.length == 0) {
						this.$sun.toast("请选择标题", 'none');
						return;
					}
					if (this.obj.mountType == 2) {
						if (!this.obj.anchorId) {
							this.$sun.toast("请输入K手小程序APPID", 'none');
							return;
						}
						if (!this.obj.anchorTitle) {
							this.$sun.toast("请输入K手小程序名称", 'none');
							return;
						}
						if (!this.obj.anchorUrl) {
							this.$sun.toast("请输入K手小程序URL", 'none');
							return;
						}
						if (!this.obj.littleYellowCarId) {
							this.$sun.toast("请输入K手小黄车商品ID", 'none');
							return;
						}
					}
				}
				
				if (this.type == 4) {
					if (this.obj.seoId.length == 0) {
						this.$sun.toast("请选择标题", 'none');
						return;
					}
					if (!this.obj.copywiring) {
						this.$sun.toast("请选择文案", 'none');
						return;
					}
				}
				
				if (this.obj.publishTimeType == 1) {
					if (!this.obj.startDate) {
						this.$sun.toast("请选择开始发布日期", 'none');
						return;
					}
					if (!this.obj.dayCount) {
						this.$sun.toast("请输入每天发布数量", 'none');
						return;
					} else {
						if (Number(this.obj.dayCount) > 35) {
							this.$sun.toast("每天发布数量最多35次", 'none');
							return;
						}
					}
					if (!this.obj.publishDays) {
						this.$sun.toast("请输入发布天数", 'none');
						return;
					}
				}

				if (!this.obj.accountGroup) {
					this.$sun.toast("请选择账户分组", 'none');
					return;
				}

				if (this.groupArrInit.length == 0) {
					this.$sun.toast("请选择发布账户", 'none');
					return;
				}
				
				let falg = false;
				
				if (this.obj.publishTimeType == 1) {
					falg = this.randomInfo();
				}else {
					falg = true;
				}
				
				if (falg) {
					
					if (this.scheduleTimes.length == 0) {
						this.$sun.toast("请先选择发布时间", 'none');
						return;
					}
					let arrTList = JSON.stringify(this.scheduleTimes);
					let arrTList2 = JSON.parse(arrTList);
					let arrTList3 = this.removeDuplicate(arrTList2);
					if (arrTList3.length != this.groupArrInit.length) {
						this.$sun.toast("请设置每个账户的发布时间", 'none');
						return;
					}
					if (this.scheduleTimes.length > Number(this.obj.numberCount)) {
						this.$sun.toast("超过最大视频发布数,请重新设置发布时间", 'none');
						return;
					}
					
					
					if (!this.isWhether) {
						return;
					}
					this.isWhether = false;
					
					this.releaseDy();
					
				}
			},
			
			//发布任务
			async releaseDy() {
				
				let getData = null;
				
				if (this.type == 1 || this.type == 2) {
					getData = {
						uid: uni.getStorageSync("uid"),
						name: this.obj.name, //任务名称
						mount_type: this.obj.linkType, //抖音挂载链接类型 1POI 2小程序 3不挂载
						poi: this.obj.poiId,
						poi_address: this.obj.poiName, //
						release_type: this.obj.publishTimeType, //发布时间类型 1随机 2指定时间
						clip_id: this.obj.projectId, //文件ID
						title_id: this.obj.seoId.toString(), //标题ID
						set_time: this.scheduleTimes, //指定发布时间
						merchant_product_id: this.obj.littleYellowCarId, //快手小黄车ID
						startDate: this.obj.startDate, //开始时间	最早第二天开始
						release_everyday_count: this.obj.dayCount, //日发送量		最多35条
						release_day_count: this.obj.publishDays, //发送天数  最多30天
						type: this.type, //1.抖音h5 2.抖音无痕(cookid) 3.抖音代员工 4.快手
					}
				}
				
				if (this.type == 3) {
					getData = {
						uid: uni.getStorageSync("uid"),
						name: this.obj.name, //任务名称
						clip_id: this.obj.projectId, //文件ID
						title_id: this.obj.seoId.toString(), //标题ID
						release_type: this.obj.publishTimeType, //发布时间类型 1随机 2指定时间
						set_time: this.scheduleTimes, //指定发布时间
						merchant_product_id: this.obj.littleYellowCarId, //快手小黄车ID
						startDate: this.obj.startDate, //开始时间	最早第二天开始
						release_everyday_count: this.obj.dayCount, //日发送量		最多35条
						release_day_count: this.obj.publishDays, //发送天数  最多30天
						type: this.type, //1.抖音h5 2.抖音无痕(cookid) 3.抖音代员工 4.快手
					}
				}
				
				if (this.type == 4) {
					getData = {
						uid: uni.getStorageSync("uid"),
						name: this.obj.name, //任务名称
						clip_id: this.obj.projectId, //文件ID
						title_id: this.obj.seoId.toString(), //标题ID
						copywiring: this.obj.copywiring, //文案ID
						release_type: this.obj.publishTimeType, //发布时间类型 1随机 2指定时间
						set_time: this.scheduleTimes, //指定发布时间
						merchant_product_id: this.obj.littleYellowCarId, //快手小黄车ID
						startDate: this.obj.startDate, //开始时间	最早第二天开始
						release_everyday_count: this.obj.dayCount, //日发送量		最多35条
						release_day_count: this.obj.publishDays, //发送天数  最多30天
						type: this.type, //1.抖音h5 2.抖音无痕(cookid) 3.抖音代员工 4.快手
						goods_id: this.obj.goods_id, //商品ID  小红书
						topic_name: this.obj.topic_name, //话题简称
						topic_list: this.obj.topic_list,
					}
				}
				
				const result = await this.$http.post({
					url: this.$api.addVideoTask,
					data: getData
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.isWhether = true;
						uni.navigateBack();
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//清空指定发布时间
			clearDate(type) {
				if (type == 1) {
					this.obj.accountGroup = ''; //发布账号分类ID
					this.obj.accountGroupName = ''; //发布账号分类名称
					this.startDate = null; //开始时间	最早第二天开始
					this.dayCount = ''; //日发送量		最多35条
					this.publishDays = ''; //发送天数  最多30天
				}
				this.groupArrInit = []; //初始已选账户列表
				this.groupArr = []; //已选账户列表
				this.groupArrIndex = -1;
				this.scheduleTimes = []; //最终账户日期时间数据
				this.futureDateIndex = 0;
				this.allSel = false; //是否全选
			},

			//发布时间类型
			selPublishTimeType(type) {

				if (this.obj.accountGroup) {
					uni.showModal({
						title: '提示',
						content: '切换发布时间类型会清空已设置的账户,确认切换?',
						success: (res) => {
							if (res.confirm) {
								this.clearDate(1);
								this.obj.publishTimeType = type;
								this.$forceUpdate();
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				} else {
					this.obj.publishTimeType = type;
				}
			},

			/*  开始日期  */
			bindDateChange(e) {
				this.obj.startDate = e.target.value;
				// this.yeartime = e.target.value.split('-')[0];
				// this.monthtime = e.target.value.split('-')[1];
			},

			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate() + 1;

				if (type === 'start') {
					year = year;
				} else if (type === 'end') {
					year = year + 100;
				}
				month = month > 9 ? month : '0' + month;;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
		
			
			//账户分组列表
			async getAccountGroupList() {
				const result = await this.$http.post({
					url: this.$api.accountGroupUser,
					data: {
						uid: uni.getStorageSync("uid"),
						type: this.type
					}
				});
				if (result.errno == 0) {
					this.accountGroupList = result.data;
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},

			//账户分组列表
			bindPickerChange4(e) {
				this.obj.accountGroup = this.accountGroupList[e.detail.value].id;
				this.obj.accountGroupName = this.accountGroupList[e.detail.value].name;
				this.clearDate(2);
			},
			
			//选择话题
			bindPickerChange5(e) {
				this.obj.poiId = this.poiList[e.detail.value].poi_id; //商家POI Id
				this.obj.poiName = this.poiList[e.detail.value].poi_name; //POI地址名称
			},
			
			//选择POI地址
			bindPickerChange3(e) {
				this.obj.poiId = this.poiList[e.detail.value].poi_id; //商家POI Id
				this.obj.poiName = this.poiList[e.detail.value].poi_name; //POI地址名称
			},

			//选择城市
			handleRegion() {
				this.$refs['picker2'].show();
			},

			//选择城市
			handleChange(e) {
				console.log(e);
			},
			handleConfirm(e) {
				let index = e.index;
				let firstIndex = index[0];
				let secondIndex = index[1];
				// let thridIndex = index[2];

				// let firstStatus = this.regionList[firstIndex];
				let secondStatus = this.regionList[firstIndex].children[secondIndex];
				// let thridStatus = this.regionList[firstIndex].children[secondIndex].children[thridIndex];
				this.cityName = secondStatus.name;
				this.city = secondStatus.code;

			},
			handleCancel(e) {
				console.log('cancel::', e);
			},
			
			//话题列表
			async getTopicList() {
				if (!this.obj.topic_name) {
					this.$sun.toast("请输入话题简称", 'none');
					return;
				}
				const result = await this.$http.get({
					url: this.$api.xiaohongshuTopic,
					data: {
						keyword: this.obj.topic_name,
					}
				});
				if (result.errno == 0) {
					this.topicList = result.data;
				} else {
					this.$sun.toast(result.msg, 'none');
				}
			},
			
			//poi列表
			async getPoiList() {

				if (!this.city) {
					this.$sun.toast("请选择城市", 'none');
					return;
				}

				if (!this.keyword) {
					this.$sun.toast("请输入商家简称", 'none');
					return;
				}

				const result = await this.$http.get({
					url: this.$api.poiList,
					data: {
						keyword: this.keyword,
						city: this.city
					}
				});
				if (result.errno == 0) {
					this.poiList = result.data;
				} else {
					this.$sun.toast(result.msg, 'none');
				}
			},

			//抖音挂载类型
			selMountType(type) {
				this.obj.linkType = type;
			},
			//快手挂载类型
			selMountType2(type) {
				this.obj.mountType = type;
			},


			/*   获取随机发布时间 sta  */

			// 获取后N天日期
			getLastOrNextFewDateBy(now, publishDays) {
				// now：随机开始时间; publishDays：发布天数
				
				now = new Date(now);
				
				var year = now.getFullYear(); //得到年份

				var month = now.getMonth() + 1 >= 10 ? now.getMonth() + 1 : '0' + (now.getMonth() + 1); //得到月份
				var day = now.getDate() < 10 ? '0' + (now.getDate()) : now.getDate(); //得到日期

				var date = now.getDate(); //得到日期
				date = year + "年" + month + "月" + date + "日";
				// let dayNum= Number(publishDays)+1   // 在发布天数+1
				let dayNum = Number(publishDays)
				let resultList = []
				let sameDay = year + "-" + month + "-" + day;
				resultList.push(sameDay)
				for (let i = 1; i < dayNum; i++) {
					//24 * 3600 * 1000 就是计算一天的时间
					var date = new Date(now.getTime() + i * 24 * 3600 * 1000);
					var year = date.getFullYear();
					var month = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1);
					var day = date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate();
					//把七天的时间和星期添加到数组中
					let result = year + "-" + month + "-" + day;
					resultList.push(result);
				}
				return resultList
			},

			// 从一个给定的数组arr中，随机返回num个不重复项
			randomNum(arr, num) {
				// 新建一个数组，将传入的数组复制，用于运算，而不要直接操作传入的数据
				var temp = new Array();
				for (var i in arr) {
					temp.push(arr[i])
				}
				// 取出的数据项，保存在此数组
				var newArray = new Array();
				for (var j = 0; j < num; j++) {
					// 判断如果数组还有可以取出的元素，以防下标越界
					if (temp.length > 0) {
						// 在数组中产生一个随机索引
						var arrIndex = Math.floor(Math.random() * temp.length);
						// 将此随机索引的对应的数组元素值push进数组
						newArray.push(temp[arrIndex]);
						//然后删掉次索引的数组元素，这时候temp变为新的数组
						temp.splice(arrIndex, 1)
					} else {
						// 数组中数据项取完后，退出循环，比如数组本来只有10项，单要求去除20项
						break
					}
				}
				return newArray
			},

			/*   获取随机发布时间 end  */

			//获取最近七天日期
			timeForMat(count) {
				// 拼接时间
				const time1 = new Date()

				if (count === 1) {
					time1.setTime(time1.getTime() - (24 * 60 * 60 * 1000))
				} else {
					if (count >= 0) {
						time1.setTime(time1.getTime())
					} else {
						if (count === -2) {
							time1.setTime(time1.getTime() + (24 * 60 * 60 * 1000) * 2)
						} else {
							time1.setTime(time1.getTime() + (24 * 60 * 60 * 1000))
						}
					}
				}

				const Y1 = time1.getFullYear()
				const M1 = ((time1.getMonth() + 1) > 9 ? (time1.getMonth() + 1) : '0' + (time1.getMonth() + 1))
				const D1 = (time1.getDate() > 9 ? time1.getDate() : '0' + time1.getDate())
				// const timer1 =M1 + '-' + D1 // 当前时间
				var date = [];
				// date.push(timer1);
				for (var i = 1; i <= count; i++) {
					const time2 = new Date()
					time2.setTime(time2.getTime() + (24 * 60 * 60 * 1000 * i))
					const Y2 = time2.getFullYear()
					const M2 = ((time2.getMonth() + 1) > 9 ? (time2.getMonth() + 1) : '0' + (time2.getMonth() + 1))
					const D2 = (time2.getDate() > 9 ? time2.getDate() : '0' + time2.getDate())
					const yesterday = Y2 + '-' + M2 + '-' + D2 // 之后的7天的日期
					date.push(yesterday);
				}
				return date;
			},

			//步骤
			getStep(type) {
				if (type == 2) {
					
					if (!this.obj.name) {
						this.$sun.toast("请输入任务名称", 'none');
						return;
					}
					if (!this.obj.projectId) {
						this.$sun.toast("请选择素材", 'none');
						return;
					}
					if (!this.obj.numberCount) {
						this.$sun.toast("选择的素材没有视频，请重新选择素材", 'none');
						return;
					}
					
					
					if (this.type == 1) {
						if (this.obj.seoId.length == 0) {
							this.$sun.toast("请选择标题", 'none');
							return;
						}
						if (this.obj.linkType == 1) {
							if (!this.city) {
								this.$sun.toast("请选择城市", 'none');
								return;
							}
							if (!this.keyword) {
								this.$sun.toast("请输入商家简称", 'none');
								return;
							}
							if (!this.obj.poiId) {
								this.$sun.toast("请选择POI地址", 'none');
								return;
							}
						}
						if (this.obj.linkType == 2) {
							if (!this.obj.anchorId) {
								this.$sun.toast("请输入D音小程序APPID", 'none');
								return;
							}
							if (!this.obj.anchorTitle) {
								this.$sun.toast("请输入D音小程序名称", 'none');
								return;
							}
							if (!this.obj.anchorUrl) {
								this.$sun.toast("请输入D音小程序URL", 'none');
								return;
							}
						}
						
					}
					
					if (this.type == 2) {
						if (this.obj.seoId.length == 0) {
							this.$sun.toast("请选择标题", 'none');
							return;
						}
						if (this.obj.mountType == 2) {
							if (!this.obj.anchorId) {
								this.$sun.toast("请输入K手小程序APPID", 'none');
								return;
							}
							if (!this.obj.anchorTitle) {
								this.$sun.toast("请输入K手小程序名称", 'none');
								return;
							}
							if (!this.obj.anchorUrl) {
								this.$sun.toast("请输入K手小程序URL", 'none');
								return;
							}
							if (!this.obj.littleYellowCarId) {
								this.$sun.toast("请输入K手小黄车商品ID", 'none');
								return;
							}
						}
					}
					
					if (this.type == 4) {
						if (this.obj.seoId.length == 0) {
							this.$sun.toast("请选择标题", 'none');
							return;
						}
						if (!this.obj.copywiring) {
							this.$sun.toast("请选择文案", 'none');
							return;
						}
					}
					
				}
				this.step = type;
			},

		}
	}
</script>

<style lang="scss">
	
	textarea {
		width: 670rpx;
		height: 200rpx;
		padding: 20rpx;
		background-color: #242424;
		border-radius: 10rpx;
		color: #FFF;
	}
	
	.acc-group {
		padding: 40rpx 10rpx;
	}
	
	.font-bott-color {
		font-size: 30rpx;
		margin-bottom: 30rpx;
		color: #FFF;
	}
	
	.img-23 {
		position: absolute;
		width: 40rpx;
		height: 40rpx;
		top: -14rpx;
		right: -14rpx
	}
	
	.label-add {
		padding: 10rpx 20rpx;
		display: inline-block;
		border: 1px solid #D9D9D9;
		border-radius: 10rpx;
		font-size: 26rpx;
		color: #A1A1A1;
		margin-left: 30rpx;
		margin-bottom: 24rpx;
	}
	
	textarea {
		width: 610rpx;
		padding: 20rpx;
		border: 1px solid #D9D9D9;
		border-radius: 10rpx;
		height: 100rpx;
	}
	
	
	.save-set {
		width: 430rpx;
		color: #000;
		text-align: center;
		background: linear-gradient(90.00deg, rgb(79, 255, 118),rgb(0, 236, 255) 97.869%);
		// background-color: #1890FF;
		font-size: 32rpx;
		padding: 40rpx 0;
	}

	.matrix-15 {
		width: 36rpx;
		height: 36rpx;
		margin-right: 20rpx;
	}

	.select-all {
		width: 320rpx;
		background-color: #F7F7F7;
		padding: 40rpx 0;
	}

	.time-label {
		width: 160rpx;
		text-align: center;
		border-radius: 10rpx;
		background-color: #F5F5F5;
		padding: 12rpx 0;
		margin-bottom: 24rpx;
		margin-left: 22rpx;
		font-size: 32rpx;
	}

	.matrix-22 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
	}

	.frame-name {
		text-align: center;
		color: #fff;
		width: 286rpx;
		padding: 14rpx 12rpx;
	}

	.matrix-23 {
		width: 38rpx;
		height: 38rpx;
		margin-left: auto;
	}

	.img-head {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		margin-left: 112rpx;
		margin-top: 18rpx;
	}

	.frame-data {
		width: 310rpx;
		background-color: #1B1C1F;
		padding: 10rpx 10rpx 20rpx;
		// border: 1px solid #D9D9D9;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}

	.color_1E6CEB {
		color: #1E6CEB;
	}

	.bott-release {
		width: 460rpx;
		// background: linear-gradient(180deg, #0E8FF3 0%, #1D6FEC 100%);
		background: linear-gradient(90.00deg, rgb(79, 255, 118),rgb(0, 236, 255) 97.869%);
		text-align: center;
		color: #000;
		font-size: 32rpx;
		padding: 30rpx 0;
		font-weight: bold;
		border-radius: 100rpx;
	}

	.bott-back {
		width: 220rpx;
		text-align: center;
		background: linear-gradient(90.00deg, rgb(79, 255, 118),rgb(0, 236, 255) 97.869%);
		color: #000;
		font-size: 32rpx;
		padding: 30rpx 0;
		border-radius: 100rpx;
	}

	.bott-but {
		position: fixed;
		bottom: 50rpx;
		// background-color: #FFF;
		// padding: 24rpx 30rpx;
		z-index: 9;
		width: 710rpx;
	}

	.sel-account {
		width: 190rpx;
		text-align: center;
		background-color: #69F8AA;
		color: #000;
		padding: 20rpx 0;
		font-size: 26rpx;
		margin-left: 40rpx;
		border-radius: 10rpx;
	}

	.next {
		position: fixed;
		bottom: 50rpx;
		width: 710rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118),rgb(0, 236, 255) 97.869%);
		color: #000;
		font-size: 30rpx;
		padding: 30rpx 0;
		z-index: 9;
	}
	
	.img-385 {
		width: 84rpx;
		height: 84rpx;
		margin-left: auto;
		border-radius: 10rpx;
	}
	
	.matrix-13 {
		width: 36rpx;
		height: 36rpx;
		margin-right: 8rpx;
	}

	.matrix-20 {
		width: 24rpx;
		height: 24rpx;
		margin-left: auto;
	}

	.input-name {
		width: 670rpx;
		padding: 20rpx;
		// border: 1px solid #D9D9D9;
		background-color: #242424;
		border-radius: 10rpx;
		margin-bottom: 30rpx;
		color: #FFF;
	}

	.red-dot {
		color: #ff0000;
		margin-left: 10rpx;
	}

	.tabs-2 {
		flex: 1;
		flex-direction: column;
		overflow: hidden;
		/* #ifdef MP-ALIPAY || MP-BAIDU */
		height: 100vh;
		/* #endif */
	}

	.scroll-h {
		width: 750rpx;
		// padding: 20rpx 0;
		background-color: #FFFFFF;
		margin-bottom: 20rpx;
		flex-direction: row;
		/* #ifndef APP-PLUS */
		white-space: nowrap;
		/* #endif */
	}

	.uni-tab-item {
		/* #ifndef APP-PLUS */
		display: inline-block;
		/* #endif */
		flex-wrap: nowrap;
	}

	.uni-tab-item-title {
		margin: 0 30rpx;
		padding: 30rpx 0 20rpx;
		flex-wrap: nowrap;
		/* #ifndef APP-PLUS */
		white-space: nowrap;
		/* #endif */
	}

	.uni-tab-item-title-active {
		margin: 0 30rpx;
		padding: 30rpx 0 20rpx;
		color: #1C6EFF;
		border-bottom: 2px solid #1C6EFF;
		font-weight: bold;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
		padding: 26rpx 20rpx;
	}
	
</style>
