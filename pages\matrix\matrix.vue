<template>
	<view>
		<view class="h_20rpx"></view>
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'373.png'+')'}">
			<view style="padding: 16rpx 30rpx;">
				<view class="margin-bottom_4rpx">累计发布账户</view>
				<view class="font-size_32rpx">{{countObj.account_count}}</view>
			</view>
			<view class="display-fw-a">
				<view class="width_176rpx-center margin-bottom_20rpx">
					<view class="margin-bottom_4rpx">D音账户</view>
					<view class="font-size_32rpx">{{countObj.douyin_account_count}}</view>
				</view>
				<view class="width_176rpx-center margin-bottom_20rpx">
					<view class="margin-bottom_4rpx">K手账户</view>
					<view class="font-size_32rpx">{{countObj.kuaishou_account_count}}</view>
				</view>
				<view class="width_176rpx-center margin-bottom_20rpx">
					<view class="margin-bottom_4rpx">视频号账户</view>
					<view class="font-size_32rpx">{{countObj.shipinghao_account_count}}</view>
				</view>
				<view class="width_176rpx-center margin-bottom_20rpx">
					<view class="margin-bottom_4rpx">小红薯账户</view>
					<view class="font-size_32rpx">{{countObj.xiaohong_account_count}}</view>
				</view>
				<!-- <view class="width_176rpx-center margin-bottom_20rpx">
					<view class="margin-bottom_4rpx">B站账户</view>
					<view class="font-size_32rpx">200</view>
				</view> -->
				<view class="width_176rpx-center margin-bottom_20rpx">
					<view class="margin-bottom_4rpx">累计曝光</view>
					<view class="font-size_32rpx">{{countObj.exposure_count_total}}</view>
				</view>
				<view class="width_176rpx-center margin-bottom_20rpx">
					<view class="margin-bottom_4rpx">累计点赞</view>
					<view class="font-size_32rpx">{{countObj.like_count_total}}</view>
				</view>
				<view class="width_176rpx-center margin-bottom_20rpx">
					<view class="margin-bottom_4rpx">累计评价</view>
					<view class="font-size_32rpx">{{countObj.comment_total}}</view>
				</view>
			</view>
		</view>
		
		<view class="display-a padding-right_20rpx margin-bottom_20rpx">
			<block v-for="(item,index) in tabs" :key="index">
				<view @click="getTabs(item.id)" class="tabs-list" :class="tabId == item.id ? 'tabs-list-2' : 'tabs-list-1'">{{item.name}}</view>
			</block>
		</view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="list-name">{{item.name}}</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="list-type list-type-1" v-if="item.channle_type == 1">D音</view>
						<view class="list-type list-type-2" v-if="item.channle_type == 2">K手</view>
						<view class="list-type list-type-3" v-if="item.channle_type == 3">视频号</view>
						<view class="list-type list-type-4" v-if="item.channle_type == 4">小红薯</view>
						<view class="font-size_26rpx color_999999">{{item.create_time}}</view>
					</view>
					<view class="display-a">
						<view style="width: 580rpx;">
							<progress :percent="item.percentage" border-radius="10" stroke-width="10"
								activeColor="#166DFD" backgroundColor="#FFF" />
						</view>
						<view class="margin-left-auto text-align_center">
							<view class="font-size_30rpx">{{item.percentage+'%'}}</view>
						</view>
					</view>
					<view class="display-a list-data">
						<view class="width_230rpx-center">
							<view>{{item.published_task}}</view>
							<view>发布成功</view>
						</view>
						<view class="width_230rpx-center">
							<view>{{item.published_wait_task}}</view>
							<view>待发布</view>
						</view>
						<view class="width_230rpx-center">
							<view>{{item.published_fail_task}}</view>
							<view>发布失败</view>
						</view>
					</view>
					<view class="display-a padding_30rpx_0">
						
						<block v-if="item.channle_type == 1 || item.channle_type == 2 || item.channle_type == 4">
							<view class="width_230rpx-center" @click="getAllTask(item.id,item.channle_type)">
								<view class="color_00FFCA">查看任务</view>
							</view>
							<view class="width_230rpx-center">
								<view class="color_FF0000" @click="termination(item.id)" v-if="item.status == 1">终止任务</view>
								<view class="color_00FFCA" v-if="item.status == 3">已完成</view>
								<view class="color_00FFCA" v-if="item.status == 2">已终止</view>
							</view>
							<view class="width_230rpx-center">
								<view class="color_FF0000" @click="del(item.id)" v-if="item.status == 2 || item.status == 3">删除任务</view>
								<view class="color_FF0_rgb" v-else>删除任务</view>
							</view>
						</block>
						
						<block v-if="item.channle_type == 3">
							<view class="width_230rpx-center">
								
							</view>
							<view class="width_230rpx-center">
								
							</view>
							<view class="width_230rpx-center">
								<view class="color_FF0000" @click="del(item.id)" v-if="item.status == 3">删除任务</view>
								<view class="color_FF0_rgb" v-else>删除任务</view>
							</view>
						</block>
						
					</view>
				</view>
			</block>
		</mescroll-body>

		<image class="matrix-11" @click="getNav()" :src="imgUrl+'377.png'"></image>

		<sunui-tabbar2 v-if="type !== 1" :fixed="true" :current="tabIndex" tintColor="#00FFCA"
			backgroundColor="#1B1B1B"></sunui-tabbar2>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: null,
				tabIndex: 0,

				imgUrl: this.$imgUrl,

				tabs: [{
						id: '',
						name: '全部'
					},
					{
						id: '1',
						name: 'D音'
					},
					{
						id: '2',
						name: 'K手'
					},
					{
						id: '3',
						name: '视频号'
					},
					{
						id: '4',
						name: '小红薯'
					},
					// {id:'5',name:'B站'}
				],
				tabId: '',

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],

				windowHeight: '',

				isWhether: true, //判断重复点击

				countObj: {},

			}
		},

		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)
			}
			//获取系统信息
			uni.getSystemInfo({
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 600;
				}
			})
		},
		
		onShow() {
			if (uni.getStorageSync('uid')) {
				this.getCount();
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},
		
		methods: {
			
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表
			
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			
			//统计数据
			async getCount() {
				const result = await this.$http.post({
					url: this.$api.indexStatistics,
					data: {
						uid: uni.getStorageSync("uid"),
					}
				});
				if (result.errno == 0) {
					this.countObj = result.data;
				}
			},
			
			//查看任务
			getAllTask(id,tabId) {
				uni.navigateTo({
					url: '/pages/matrix/allTask?taskId='+id+'&type=2'+'&selLabel='+tabId
				})
			},
			
			//删除任务
			del(id) {
				uni.showModal({
					title: '提示',
					content: '是否确认删除该任务?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.delTask,
								data: {
									uid: uni.getStorageSync("uid"),
									id: id,
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			//终止任务
			termination(id) {
				uni.showModal({
					title: '提示',
					content: '任务一旦终止后不可启用,是否确认?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.endTask,
								data: {
									uid: uni.getStorageSync("uid"),
									id: id,
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.VideoTaskList,
					data: {
						type: this.tabId,
						uid: uni.getStorageSync("uid"),
						name: this.name,
						page: scroll.num,
						psize: 12
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			getNav() {
				uni.redirectTo({
					url: '/pages/index/index'
				})
			},
			
			getTabs(id) {
				this.tabId = id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
		}
	}
</script>

<style lang="scss">
	
	.color_FF0_rgb {
		color: rgba(255, 0, 0, 0.34);
	}
	
	.matrix-11 {
		width: 96rpx;
		height: 96rpx;
		position: fixed;
		bottom: 170rpx;
		right: 20rpx;
		z-index: 9;
	}
	
	.list-type-4 {
		background-color: #FF0000;
	}
	
	.list-type-3 {
		background-color: #FFDC00;
	}
	
	.list-type-2 {
		background-color: #FF7B00;
	}
	
	.list-type-1 {
		background-color: #166DFD;
	}
	
	.list-type {
		display: inline-block;
		padding: 4rpx 12rpx;
		border-radius: 10rpx;
		margin-right: 18rpx;
		font-size: 24rpx;
	}
	
	.list-name {
		font-size: 32rpx;
		padding: 26rpx 0 20rpx;
	}
	
	.list-data {
		padding: 20rpx 0;
		border-bottom: 1px solid #2C2C2C;
	}
	
	.list-public {
		background-color: #0F0F0F;
		padding: 0 20rpx;
		color: #FFF;
	}
	
	.tabs-list-2 {
		background: linear-gradient(90.00deg, rgb(105, 248, 170),rgb(22, 189, 253) 100%);
		color: #000;
	}
	
	.tabs-list-1 {
		background: rgb(68, 68, 68);
		color: #E8E8E8;
	}
	
	.tabs-list {
		width: 126rpx;
		text-align: center;
		border-radius: 10rpx;
		padding: 8rpx 0;
		margin-left: 20rpx;
	}
	
	.bg {
		width: 710rpx;
		height: 324rpx;
		// height: 265rpx;
		border-radius: 10rpx;
		margin: 0 20rpx 30rpx;
		box-shadow: 0px 4px 4px 0px rgba(18, 84, 254, 0.47);
		background-repeat: no-repeat;
		background-size: contain;
		color: #FFF;
		// padding: 36rpx 0;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}

</style>
