<template>
	<view>
		
		<view v-if="shopObj.id">
			
			<image class="img-pic" :src="shopObj.pic_url"></image>
			
			<view class="d-top">
				<view class="d-money"><span class="font-size_26rpx">￥</span>{{Number(shopObj.money)}}</view>
				<view class="font-size_32rpx margin-bottom_30rpx">{{shopObj.name}}</view>
				<view class="display-fw-a" v-if="shopObj.type == 1">
					<view class="d-count">获得: {{shopObj.type == 1 ? shopObj.day+'(天)' : shopObj.point+'(点)'}}</view>
					<view class="d-count">视频合成: {{shopObj.second_infinite == 1 ? '无限' : shopObj.second?(shopObj.second/60).toFixed(0):0}}(分)</view>
					<view class="d-count">AI文案: {{shopObj.ai_copywriting_times}}(次)</view>
					<view v-if="cloneSet.voice_high_open == 1" class="d-count">高保真声音: {{shopObj.voice_twin_count}}(次)</view>
					<view v-if="cloneSet.voice_high_open == 1" class="d-count">高保真合成: {{shopObj.high_fidelity_words_number}}(字)</view>
					<view v-if="cloneSet.xunfei_sound_clone_swich == 1" class="d-count">专业版声音: {{shopObj.xunfei_sound_generate_words_number}}(次)</view>
					<view v-if="cloneSet.xunfei_sound_clone_swich == 1" class="d-count">专业版合成: {{shopObj.xunfei_sound_clone_words_number}}(字)</view>
				</view>
				<view class="d-count" v-else>
					获得: {{shopObj.type == 1 ? shopObj.day+'(天)' : shopObj.point+'(点)'}}
				</view>
				<view class="display-a-js">
					<view class="font-size_24rpx color_999999">已售: {{shopObj.sold}}</view>
					<view class="display-a">
						<image class="img-53" @click="reduceNum()" :src="imgUrl + '53.png'"></image>
						<input type="number" disabled class="input-num" v-model="cardNum" @input="inputChange" />
						<image class="img-53" @click="addNum()" :src="imgUrl + '54.png'"></image>
					</view>
				</view>
			</view>
			
			<view class="d-frame">
				<view class="d-title">-- 详情介绍 --</view>
				<view class="padding_20rpx">
					<rich-parser :html="shopObj.detail" domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load
						ref="article" selectable show-with-animation use-anchor>
						<!-- 加载中... -->
					</rich-parser>
				</view>
			</view>
			
			<view style="height: 150rpx;"></view>
			
			<block v-if="isOsName == 1">
				<view class="but" @click="but()">立即购买￥{{Number(totalPrice)}}</view>
			</block>
			<block v-if="isOsName == 2">
				<view class="but" v-if="payFig.pay_open == 1" @click="but()">立即购买￥{{Number(totalPrice)}}</view>
				<view class="but" @click="getContactUs()" v-else>请联系客服</view>
			</block>
			
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				shopId: '',
				
				shopObj: {},
				
				imgUrl: this.$imgUrl,
				
				isWhether: true, //防止重复点击
				
				cardNum: 1,
				
				totalPrice: 0,
				
				payFig: {},
				
				isOsName: '', //1安卓 2ios
				
				cloneSet: {},
				
			}
		},
		
		onLoad(options) {
			this.getCloneSet();
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					if (res.osName == 'ios' || res.osName == 'macos') {
						this.isOsName = 2;
					}else {
						this.isOsName = 1;
					}
					console.log("isOsName==>",this.isOsName);
					
				},
				fail(err) {
					// console.log(err);
				}
			})
			this.getPayconfig();
			if (options.shopId) {
				this.shopId = options.shopId;
				this.detail();
			}
		},
		
		onShow() {
			
		},
		
		methods: {
			
			//克隆设置
			async getCloneSet() {
				const result = await this.$http.post({
					url: this.$api.cloneSet
				});
				if (result.errno == 0) {
					this.cloneSet = result.data;
				}
			},
			
			getContactUs() {
				uni.navigateTo({
					url: '/pages/my/contactUs'
				})
			},
			
			//支付设置
			async getPayconfig() {
				const result = await this.$http.post({
					url: this.$api.payconfig,
				});
				if (result.errno == 0) {
					this.payFig = result.data;
				}
			},
			
			//购买会员
			async but() {
				
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				
				const result = await this.$http.post({
					url: this.$api.partnerCardBuy,
					data: {
						uid: uni.getStorageSync('uid'),
						partner_id: uni.getStorageSync('partnerId'),
						goods_id: this.shopObj.id,
						count: this.cardNum
					}
				});
				if (result.errno == 0) {
					this.wxPay(result.data)
				} else {
					this.$sun.toast(result.message,'none');
					this.isWhether = true;
				}
				
			},
			
			/*  微信支付  */
			async wxPay(log_no) {
				const result = await this.$http.post({
					url: this.$api.pay,
					data: {
						openid: uni.getStorageSync('openid'),
						price: this.totalPrice,
						log_no: log_no,
						name: this.shopObj.name
					}
				});
				if (result.errno == 0) {
					uni.requestPayment({
						provider: 'wxpay',
						timeStamp: result.data.timeStamp,
						nonceStr: result.data.nonceStr,
						package: result.data.package,
						signType: result.data.signType,
						paySign: result.data.paySign,
						success: async (res) => {
							this.$sun.toast("支付成功");
							setTimeout(() => {
								this.isWhether = true;
								uni.navigateTo({
									url: '/pages/my/partner/order'
								})
							}, 2000);
						},
						fail: (err) => {
							this.isWhether = true;
							this.$sun.toast("取消支付",'error');
						}
					});
				}else {
					this.isWhether = true;
					if (result.errno == -1){
						this.$sun.toast(result.message,'none');
						return;
					}
					if (result.return_code == 'FAIL'){
						uni.showModal({
							title: '支付配置错误',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									
								}
							}
						})
					}else {
						uni.showModal({
							title: '提示',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									
								}
							}
						})
					}
				}
			},
			
			//减少
			reduceNum() {
				if (this.cardNum <= 1) {
					this.cardNum = 1;
					this.totalPrice = Number(this.shopObj.money);
					return;
				}
				this.cardNum = Number(this.cardNum) - 1;
				this.getTotal();
			},
			
			//增加
			addNum() {
				this.cardNum = Number(this.cardNum) + 1;
				this.getTotal();
			},
			
			//计算总价
			getTotal() {
				
				let allPrice = Number(this.cardNum) * Number(this.shopObj.money);
				
				this.totalPrice = Number(allPrice).toFixed(2);
				
			},
			
			inputChange(e) {
				if (e.detail.value) {
					
				}
			},
			
			//商品详情
			async detail() {
				const result = await this.$http.post({
					url: this.$api.partnerGoodsDetail,
					data: {
						goods_id: this.shopId
					}
				});
				if (result.errno == 0) {
					this.shopObj = result.data;
					this.totalPrice = Number(this.shopObj.money);
					this.$sun.title(this.shopObj.name);
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.but {
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(0, 183, 255),rgb(0, 134, 255) 100%);
		width: 690rpx;
		text-align: center;
		color: #FFF;
		font-size: 34rpx;
		font-weight: bold;
		padding: 20rpx 0;
		position: fixed;
		bottom: 50rpx;
		left: 30rpx;
	}
	
	.d-title {
		text-align: center;
		color: #999999;
		font-size: 32rpx;
		padding-top: 24rpx;
	}
	
	.d-frame {
		width: 750rpx;
		background-color: #FFF;
	}
	
	.input-num {
		width: 60rpx;
		text-align: center;
		color: #000;
	}
	
	.img-53 {
		width: 56rpx;
		height: 56rpx;
	}
	
	.d-count {
		// width: 332rpx;
		// font-size: 26rpx;
		display: inline-block;
		padding: 10rpx;
		background-color: #F6F6F6;
		border-radius: 10rpx 14rpx;
		font-size: 24rpx;
		color: #333;
		margin-bottom: 26rpx;
		margin-right: 16rpx;
	}
	
	.d-money {
		font-weight: bold;
		font-size: 38rpx;
		color: #FF0000;
		margin-bottom: 10rpx;
	}
	
	.d-top {
		width: 750rpx;
		background-color: #FFF;
		border-radius: 20rpx 20rpx 0 0;
		margin-top: -30rpx;
		padding: 26rpx 26rpx 34rpx;
		margin-bottom: 26rpx;
	}
	
	.img-pic {
		width: 750rpx;
		height: 750rpx;
		position: relative;
		z-index: -1;
	}
	
	page {
		background-color: #F6F6F6;
		border: none;
	}
	
</style>
