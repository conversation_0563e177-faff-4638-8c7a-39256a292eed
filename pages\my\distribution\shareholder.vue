<template>
	<view>
		
		<view class="h_30rpx"></view>
		
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'328.png'+')'}" v-if="statisticsObj">
			<view class="padding-left_50rpx margin-bottom_20rpx">平台总分红</view>
			<view class="padding-left_40rpx font-size_56rpx font-weight_bold margin-bottom_30rpx">￥{{statisticsObj.platform_money ? Number(statisticsObj.platform_money).toFixed(2) : 0}}</view>
			<view class="display-a">
				<view class="width_236rpx-center">
					<view class="margin-bottom_10rpx font-size_30rpx">￥{{statisticsObj.yesterday_total ? Number(statisticsObj.yesterday_total).toFixed(2) : 0}}</view>
					<view>昨日营业额</view>
				</view>
				<view class="width_236rpx-center">
					<view class="margin-bottom_10rpx font-size_30rpx">￥{{statisticsObj.yesterday_money ? Number(statisticsObj.yesterday_money).toFixed(2) : 0}}</view>
					<view>昨日分红</view>
				</view>
				<view class="width_236rpx-center">
					<view class="margin-bottom_10rpx font-size_30rpx">￥{{statisticsObj.total_money ? Number(statisticsObj.total_money).toFixed(2) : 0}}</view>
					<view>我的累计分红</view>
				</view>
			</view>
		</view>
		
		<view class="display-a padding_0_30rpx color_FFFFFF">
			<view class="s-line"></view>
			<view class="font-size_36rpx font-weight_bold">收益列表</view>
			<view class="display-a color_FFFFFF margin-left-auto">
				<picker mode="date" fields="month" :value="birth" :start="startDate" :end="endDate"
					@change="bindDateChange">
					<input type="text" style="width: 120rpx;" disabled placeholder="请选择" v-model="birth"
						placeholder-class="font-size_28rpx" />
				</picker>
				<image class="img-99" :src="imgUrl+'267.png'"></image>
			</view>
		</view>
		
		<view class="s-top">平台本月累计分红: ￥{{month_total_money ? Number(month_total_money).toFixed(2) : 0}}，我的本月累计分红: ￥{{month_my_money ? Number(month_my_money).toFixed(2) : 0}}</view>
		
		<mescroll-body ref="mescrollRef" :optionIcon="optionIcon" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a padding_30rpx_0">
						<view class="list-date">
							<view class="font-size_30rpx font-weight_bold">{{item.month}}月</view>
							<view class="font-size_24rpx">{{item.day}}日</view>
						</view>
						<view class="margin-left-auto list-money">
							<view class="s-money color_FFFFFF">￥{{item.total ? Number(item.total).toFixed(2) : 0}}</view>
							<view class="color_757575">总分红金额</view>
						</view>
						<view class="list-line"></view>
						<view class="list-money">
							<view class="s-money color_FF0000">￥{{item.money ? Number(item.money).toFixed(2) : 0}}</view>
							<view class="color_757575">我分红金额</view>
						</view>
					</view>
					<view class="color_c7c7c7 display-a-js list-bott">
						<view class="font-size_26rpx">本期股东数量<span class="color_FFFFFF margin_0_10rpx font-weight_bold">{{item.shareholder_number}}</span>(位)</view>
						<view class="font-size_26rpx">分红到账时间: {{item.create_data}}</view>
					</view>
				</view>
			</block>
		</mescroll-body>
		
		
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				birth: '',
				yeartime: '',
				monthtime: '',
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				month_total_money: '',
				month_my_money: '',
				
				list: [],
				
				windowHeight: '',
				
				statisticsObj: '',
				
			}
		},
		
		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		
		onLoad() {
			//获取系统信息
			uni.getSystemInfo({ 
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 540;
				}
			})
		},
		
		onShow() {
			if (uni.getStorageSync('uid')) {
				var myDate = new Date();
				this.yeartime = myDate.getFullYear();
				this.monthtime = myDate.getMonth() + 1;
				if (this.monthtime.toString().length == 1) {
					this.monthtime = '0' + this.monthtime;
				}
				this.birth = this.yeartime + '-' + this.monthtime;
				setTimeout(()=> {
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
				}, 1000);
				this.getShareholderStatistics();
			}else {
				uni.showModal({
					content:"请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},
		
		methods: {
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.shareholderLog,
					data: {
						uid: uni.getStorageSync("uid"),
						year: this.yeartime,
						month: this.monthtime,
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.month_total_money = result.data.month_total_money;
					this.month_my_money = result.data.month_my_money;
					for (let i = 0 ; i < result.data.list.length ; i++) {
						if (result.data.list[i].amount_data) {
							let arr = result.data.list[i].amount_data.split("-");
							result.data.list[i].month = Number(arr[1]);
							result.data.list[i].day = Number(arr[2]);
						}
					}
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			//分红统计
			async getShareholderStatistics() {
				const result = await this.$http.post({
					url: this.$api.shareholderStatistics,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.statisticsObj = result.data;
				}
			},
			
			/*  日期选择  */
			bindDateChange(e) {
				// console.log("选择的日期", e.target.value);
				this.birth = e.target.value;
				this.yeartime = e.target.value.split('-')[0];
				this.monthtime = e.target.value.split('-')[1];
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();
			
				if (type === 'start') {
					year = year - 100;
				} else if (type === 'end') {
					year = year;
				}
				month = month > 9 ? month : '0' + month;;
				// day = day > 9 ? day : '0' + day;
				return `${year}-${month}`;
			},
			
			//返回
			navig() {
				
				let pages = getCurrentPages();  //获取所有页面栈实例列表
				
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}else {
					uni.navigateBack();
				}
				
			},
			
		}
	}
</script>

<style lang="scss">
	
	.s-money {
		font-size: 34rpx;
		font-weight: bold;
		margin-bottom: 16rpx;
	}
	
	.list-line {
		width: 2rpx;
		height: 72rpx;
		background-color: rgb(47, 47, 47);
	}
	
	.list-money {
		width: 280rpx;
		text-align: center;
	}
	
	.list-bott {
		background-color: #181616;
		border-radius: 0 0 10rpx 10rpx;
		padding: 20rpx;
	}
	
	.list-date {
		background-color: #169DFD;
		height: 88rpx;
		width: 88rpx;
		border-radius: 10rpx;
		color: #FFF;
		padding: 6rpx 14rpx;
		text-align: center;
		margin: 0 26rpx;
	}
	
	.list-public {
		background-color: #1C1C1C;
		padding: 0;
	}
	
	.img-99 {
		width: 20rpx;
		height: 20rpx;
	}
	
	.s-top {
		background-color: #171718;
		width: 710rpx;
		border-radius: 10rpx;
		padding: 20rpx;
		margin: 20rpx;
		color: #0095FF;
		font-size: 24rpx;
	}
	
	.s-line {
		width: 8rpx;
		height: 34rpx;
		background-color: #0072FF;
		border-radius: 10rpx;
		margin-right: 20rpx;
	}
	
	.padding-left_50rpx {
		padding-left: 50rpx;
	}
	
	.padding-left_40rpx {
		padding-left: 40rpx;
	}
	
	.bg {
		width: 710rpx;
		height: 314rpx;
		background-repeat: no-repeat;
		background-size: contain;
		margin: 0 20rpx 34rpx;
		padding-top: 40rpx;
		color: #FFF;
		border-radius: 20rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		background-color: #000;
		border-top: none;
	}
	
</style>
