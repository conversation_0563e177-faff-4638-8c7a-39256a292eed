<template>
	<view>

		<view class="display-a c-top margin-bottom_10rpx" v-if="voiceType == 1">
			<block v-for="(item,index) in tabs" :key="index">
				<view class="display-ac-jc width_186rpx-center" @click="getId(item.id)">
					<view :class="tabsId == item.id ? 'color_sel' : 'color_9B9B9B'">{{item.name}}</view>
					<view class="c-line" :class="tabsId == item.id ? 'c-line-1' : 'c-line-2'"></view>
				</view>
			</block>
		</view>

		<block v-if="tabsId == 1">
			<view class="c-frame">
				<view class="display-a frame-top">
					<view class="copywriting display-ac-jc" @click="openAICreation()">
						<image class="img-319" :src="imgUrl+'356.png'"></image>
						<view class="font-size_24rpx">AI文案</view>
					</view>
					<view class="copywriting display-ac-jc" @click="getAccountInfo()">
						<image class="img-319" :src="imgUrl+'357.png'"></image>
						<view class="font-size_24rpx">一键仿写</view>
					</view>
				</view>
				<textarea v-model="msgText" maxlength="1000" placeholder="我是虚拟数字人,请输入您的配音文案"
					placeholder-class="placeholder"></textarea>
				<view class="display-a padding_20rpx">
					<view class="display-a-jc a-speed" @click="openSpeed()">
						<view>语速: {{speed}}</view>
						<image class="img-242" :src="imgUrl+'242.png'"></image>
					</view>
					<view class="display-a-jc a-speed" @click="openSpeed()">
						<view>语调: {{pitch}}</view>
						<image class="img-242" :src="imgUrl+'242.png'"></image>
					</view>
					<view class="msg-tips">{{msgText.length}}/1000字</view>
				</view>
				<!-- <view class="color_FF0000 font-size_24rpx" style="padding: 0 20rpx 20rpx;">注:一个中文3个字节,一个数字或英文1个字节,一个中文标点符号3个字节,一个英文标点符号1个字节</view> -->
			</view>

			<view class="display-a padding_0_20rpx color_FFFFFF margin-bottom_20rpx">
				<view class="a-line"></view>
				<view class="font-size_32rpx font-weight_bold">选择音色</view>
				<image class="img-243" :src="imgUrl+'243.png'"></image>
				<view class="color_979797 margin-left-auto" @click="getVoice()">+ 克隆音色</view>
			</view>
			<!-- <view class="display-a margin-bottom_30rpx">
				<block v-for="(item,index) in selArr" :key="index">
					<view @click="getSelArr(item.id)" class="sel-tabs" :class="selIndex == item.id ? 'sel-tabs-1' : 'sel-tabs-2'">{{item.name}}</view>
				</block>
			</view>
			<block v-if="selIndex == 1">
				<block v-for="(item,index) in timbreList" :key="index">
					<view class="list-public display-a" @click="selVoiceId(item,1)">
						<image class="img-238" :src="imgUrl+'238.png'"></image>
						<view class="color_FFFFFF font-size_32rpx font-weight_bold">{{item.name}}</view>
						<image class="img-240 margin-left-auto" :src="voiceObj.id == item.id ? imgUrl+'241.png' : imgUrl+'240.png'"></image>
					</view>
				</block>
			</block>
			<block v-if="selIndex == 2"> -->
			<block v-if="voiceList.length > 0">
				<block v-for="(item,index) in voiceList" :key="index">
					<view class="list-public display-a" @click="selVoiceId(item,2)">
						<image class="img-238" :src="imgUrl+'238.png'"></image>
						<view class="color_FFFFFF font-size_32rpx font-weight_bold">{{item.name}}</view>
						<image class="img-240 margin-left-auto"
							:src="voiceObj.id == item.id ? imgUrl+'241.png' : imgUrl+'240.png'"></image>
					</view>
				</block>
			</block>
			<block v-else>
				<view class="display-ac-jc">
					<image class="nodata" :src="imgUrl+'418.png'"></image>
					<view class="nodata-tips">您还没有声音资产，快去复刻一个吧~</view>
					<view class="nodata-but" @click="getVoice()">立即复刻声音</view>
				</view>
			</block>
			<!-- </block> -->

		</block>
		<block v-if="tabsId == 2">
			<block v-if="soundList.length > 0">
				<block v-for="(item,index) in soundList" :key="index">
					<view class="list-public display-a">
						<block v-if="item.url">
							<view class="display-a-jc r-play" :key="updateKey"
								@click="playAudio(item.isPlay,index,item.url)">
								<image class="img-94" :key="updateKey"
									:src="item.isPlay == 1 ? imgUrl+'95.png' : imgUrl+'94.png'"></image>
							</view>
						</block>
						<block v-else>
							<view class="display-a-jc r-play" @click="getSoundRefresh(item.id)">
								<view class="circle-loading">
									<view class="dot">
										<view class="first-dot"></view>
									</view>
									<view class="dot"></view>
									<view class="dot"></view>
									<view class="dot"></view>
								</view>
							</view>
						</block>
						<view @click="getSel(item)" style="width: 460rpx;">
							<view class="color_FFFFFF font-size_32rpx font-weight_bold margin-bottom_10rpx">
								{{item.name}}
							</view>
							<view class="color_A1A1A1 font-size_26rpx">{{item.create_time}}</view>
						</view>

						<image @click="getSel(item)" v-if="item.url" class="img-240 margin-left-auto"
							:src="soundObj.id == item.id ? imgUrl+'241.png' : imgUrl+'240.png'"></image>
						<image v-else @click="getSoundRefresh(item.id)" class="img-240 margin-left-auto"
							:src="imgUrl+'245.png'"></image>
						<image @click="delAudio(item.name,item.id)" class="img-244" :src="imgUrl+'244.png'"></image>
					</view>
				</block>
			</block>
			<block v-else>
				<view class="display-ac-jc">
					<image class="nodata" :src="imgUrl+'418.png'"></image>
					<view class="nodata-tips">您还没有音频，快去生成音频吧~</view>
				</view>
			</block>
		</block>

		<view style="height: 140rpx;"></view>
		<view class="bott-pos">
			<view v-if="tabsId == 1" class="a-but" @click="getVideoSendTts()">生成音频</view>
			<view v-if="tabsId == 2" class="a-but" @click="returnNav(1)">确认选择</view>
		</view>

		<sunui-popup ref="pop4">
			<template v-slot:content>
				<view class="pop-bg">
					<image @click="closeSpeed()" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title">调整语调语速</view>
					<view style="padding: 50rpx 20rpx;">
						<view class="display-a-js">
							<view class="font-size_32rpx">语速</view>
							<view>当前语速: {{Number(speechRate)}}</view>
						</view>
						<view>
							<slider activeColor="#423AFE" block-size="18" backgroundColor="#A7A7A7" min="-500" max="500"
								:value="Number(speechRate)" @change="sliderChange" />
						</view>
					</view>
					<view style="padding: 50rpx 20rpx;">
						<view class="display-a-js">
							<view class="font-size_32rpx">语调</view>
							<view>当前语调: {{Number(pitchRate)}}</view>
						</view>
						<view>
							<slider activeColor="#423AFE" block-size="18" backgroundColor="#A7A7A7" min="-500" max="500"
								:value="Number(pitchRate)" @change="pitchRateChange" />
						</view>
					</view>
					<view class="c-but" @click="speedConfirm()">确认</view>
				</view>
			</template>

		</sunui-popup>

		<sunui-popup ref="pop9">
			<template v-slot:content>

				<view class="pop-bg" style="padding: 34rpx 20rpx;">
					<image @click="closeAICreation()" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title">AI文案类型</view>
					<view class="display-fw-js">
						<view class="a-frame display-a" @click="getAdd(18)">
							<image class="img-25" :src="imgUrl + '324.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">DeepSeek
								</view>
								<view class="color_D5D5D5 font-size_24rpx">DeepSeek文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(1)">
							<image class="img-25" :src="imgUrl + '309.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">企业宣传
								</view>
								<view class="color_D5D5D5 font-size_24rpx">企业宣传</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(2)">
							<image class="img-25" :src="imgUrl + '310.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">同城团购
								</view>
								<view class="color_D5D5D5 font-size_24rpx">团购产品文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(3)">
							<image class="img-25" :src="imgUrl + '311.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">电商带货
								</view>
								<view class="color_D5D5D5 font-size_24rpx">带货营销文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(4)">
							<image class="img-25" :src="imgUrl + '312.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">知识科普
								</view>
								<view class="color_D5D5D5 font-size_24rpx">知识获取很方便</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(5)">
							<image class="img-25" :src="imgUrl + '313.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">情感专家
								</view>
								<view class="color_D5D5D5 font-size_24rpx">情感问题一点通</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(6)">
							<image class="img-25" :src="imgUrl + '314.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">口播文案
								</view>
								<view class="color_D5D5D5 font-size_24rpx">播音必备神器</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(7)">
							<image class="img-25" :src="imgUrl + '315.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">朋友圈营销
								</view>
								<view class="color_D5D5D5 font-size_24rpx">轻松搞定营销文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(8)">
							<image class="img-25" :src="imgUrl + '316.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">小红书笔记
								</view>
								<view class="color_D5D5D5 font-size_24rpx">轻松生成笔记</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(9)">
							<image class="img-25" :src="imgUrl + '318.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">智能代写
								</view>
								<view class="color_D5D5D5 font-size_24rpx">一键智能代写</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(10)">
							<image class="img-25" :src="imgUrl + '317.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">文案仿写
								</view>
								<view class="color_D5D5D5 font-size_24rpx">模仿生成营销文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(16)">
							<image class="img-25" :src="imgUrl + '325.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">AI提取文案
								</view>
								<view class="color_D5D5D5 font-size_24rpx">一键提取文案</view>
							</view>
						</view>
					</view>
				</view>

			</template>
		</sunui-popup>

	</view>
</template>

<script>
	export default {
		data() {
			return {

				voiceAudioContext: null,

				imgUrl: this.$imgUrl,

				tabs: [{
						id: 1,
						name: '合成音频'
					},
					{
						id: 2,
						name: '历史音频'
					}
				],

				tabsId: 1, //1合成 2历史 

				voiceType: '1', //1入门 3专业 2高保真

				selArr: [{
						id: '1',
						name: '共享'
					},
					{
						id: '2',
						name: '我的'
					},
				],
				selIndex: '1',

				speechRate: 0, //语速值 -500~500
				pitchRate: 0, //语调值 -500~500
				speed: 0,
				pitch: 0,

				msgText: '', //数字人文本

				timbreList: [], //默认音色

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				voiceList: [], //音色
				voiceObj: {
					name: '',
					// media_url: '',//
					id: '', //
					voice_type: '', // 1-默认音色；2-自定义音色
				},

				topIndex: '', //记录上次播放下标

				soundList: [], //历史音频
				soundObj: {
					name: '',
					url: '', //
					id: '', //
				},

				updateKey: false,

				isWhether: true, //判断重复点击

			}
		},

		onLoad(options) {
			if (options.id) {
				this.soundObj.id = options.id;
			}
			if (options.tabsId) {
				this.tabsId = options.tabsId;
			}
			if (options.voiceType) {
				this.voiceType = options.voiceType;
			}
			if (options.answerType == 1) {
				this.msgText = uni.getStorageSync("answer");
			}
			if (options.content) {
				this.msgText = options.content
			}
			this.getVoiceTrainList();
			this.getVideoSoundList();
			// this.getTimbreList();
		},

		onShow() {

		},

		onUnload() {

			if (this.voiceAudioContext && (!this.voiceAudioContext.paused)) {
				this.voiceAudioContext.stop();
			}

		},

		computed: {
			getByteLength: function() {
				return function(str) {
					const buffer = Buffer.from(str, 'utf8');
					return buffer.length;
				};
			},

		},

		methods: {

			// getByteLength(str) {
			// 	const encoder = new TextEncoder();
			// 	const uint8array = encoder.encode(str);
			// 	return uint8array.length;
			// },

			getAdd(type) {
				if (type == 16) {
					uni.navigateTo({
						url: '/pages/index/AICreation/extract?typeIndex=2'
					})
				} else {
					uni.navigateTo({
						url: '/pages/index/AICreation/release?type=' + type + '&typeIndex=2'
					})
				}
				this.closeAICreation();
			},

			//查询点数是否足够
			async getAccountInfo() {

				if (!this.msgText) {
					this.$sun.toast("请先输入文案", 'error');
					return;
				}

				// uni.showModal({
				// 	content: this.tallySetObj.ai_create_deduct + '点/1次,一键仿写会覆盖当前文案,是否确认?',
				// 	cancelText: "取消",
				// 	confirmText: "确认",
				// 	success: async (res) => {
				// 		if (res.confirm) {
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.accountInfo,
					data: {
						type: 1,
						uid: uni.getStorageSync('uid'),
					}
				});
				if (result.errno == 0) {
					this.getCopyImitation();
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
				// } else if (res.cancel) {

				// 		}
				// 	}
				// })

			},

			//一键仿写
			async getCopyImitation() {
				const result = await this.$http.post({
					url: 'https://vapi.weijuyunke.com/api/doBao/copyImitation',
					data: {
						content: this.msgText,
						countType: 2,
						ipStatus: 2,
						domainName: 'vapi.weijuyunke.com',
						requestIp: '**************'
					},
					mask: true,
					title: '请求中...'
				});
				if (result.code == 200) {
					// this.$sun.toast("操作成功");
					// setTimeout(() => {
					// 	this.msgText = result.data;
					// 	this.isWhether = true;
					// }, 1000);
					this.getAICreation(result.data);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//生成AI文案
			async getAICreation(answer) {

				const result = await this.$http.post({
					url: this.$api.aiCreateAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: "一键仿写",
						type: 17,
						question: this.msgText, //拼接的文本
						answer: answer, // 接口返回的文本
						words: answer.length
					}
				});
				if (result.errno == 0) {
					this.$sun.toast("操作成功");
					setTimeout(() => {
						this.msgText = answer;
						this.isWhether = true;
					}, 1000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//AI文案
			openAICreation() {
				this.$refs.pop9.show({
					style: 'background-color:#222127;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeAICreation() {
				this.$refs.pop9.close();
			},

			getSelArr(type) {
				this.selIndex = type;
				this.voiceObj.id = '';
				this.voiceObj.name = '';
				this.voiceObj.voice_type = '';
			},

			//选择音色
			selVoiceId(obj, type) {
				this.voiceObj.id = obj.id;
				this.voiceObj.name = obj.name;
				this.voiceObj.voice_type = type;
			},

			//上一页文案仿写的
			otherFun2(text) {
				this.msgText = text;
			},

			//返回上一页
			returnNav(type) {
				let navObj = {
					type: type,
					id: this.soundObj.id,
					name: this.soundObj.name,
					url: this.soundObj.url,
				};
				var pages = getCurrentPages();
				var prevPage = pages[pages.length - 2]; //上一个页面
				prevPage.$vm.otherFun(navObj); //重点$vm
				uni.navigateBack();
			},

			//选中音频
			getSel(obj) {
				this.soundObj = {
					name: obj.name,
					url: obj.url, //
					id: obj.id, //
				}
			},

			//确认语速语调
			speedConfirm() {
				this.speed = this.speechRate;
				this.pitch = this.pitchRate;
				this.closeSpeed();
			},

			//语调语速
			openSpeed() {
				this.$refs.pop4.show({
					style: 'background-color:#222127;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeSpeed() {
				this.$refs.pop4.close();
			},

			//语调设置
			pitchRateChange(e) {
				this.pitchRate = e.detail.value;
			},

			//语速设置
			sliderChange(e) {
				// console.log('value 发生变化：' + e.detail.value)
				// this.speechRate = Number(e.detail.value / 10).toFixed(1);
				this.speechRate = e.detail.value;
			},

			/*  删除音频  */
			delAudio(name, id) {

				uni.showModal({
					title: '提示',
					content: '确认删除 ' + name + ' 吗?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.soundDel,
								data: {
									uid: uni.getStorageSync('uid'),
									sound_id: id
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.getVideoSoundList();
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				});
			},

			//播放音频
			async playAudio(isPlay, index, url) {

				this.updateKey = false;

				// if (!this.isWhether) {
				// 	return;
				// }
				// this.isWhether = false;


				if (url) {

					// console.log("----->进入了");

					// uni.showLoading({
					// 	title: '正在试听...',
					// 	// mask: true
					// })

					if (this.voiceAudioContext && (!this.voiceAudioContext.paused)) {
						this.voiceAudioContext.stop();
						this.voiceAudioContext.destroy();
					}


					this.voiceAudioContext = null;

					this.voiceAudioContext = uni.createInnerAudioContext();

					this.voiceAudioContext.src = url;
					setTimeout(() => {
						if (isPlay == 2) {
							for (let i = 0; i < this.soundList.length; i++) {
								if (this.soundList[i].isPlay == 1) {
									this.soundList[i].isPlay = 2;
									this.updateKey = true;
								}
							}
							this.soundList[index].isPlay = 1;
							this.updateKey = true;
							this.voiceAudioContext.play();
							this.voiceAudioContext.onPlay(() => {
								// this.isWhether = true;
								// this.$forceUpdate();
								console.log('开始播放');
							});
							this.voiceAudioContext.onEnded(() => {
								this.soundList[index].isPlay = 2;
								this.updateKey = true;
								// uni.hideLoading();
								this.voiceAudioContext.destroy();
								this.voiceAudioContext = null;
								this.$sun.toast("试听完成");
								// this.isWhether = true;

							});
							this.voiceAudioContext.onError((err) => {
								this.soundList[index].isPlay = 2;
								this.updateKey = true;
								this.$sun.toast("音频播放出错:" + err, 'none');
								// uni.hideLoading();
								this.voiceAudioContext.destroy();
								this.voiceAudioContext = null;
								// this.isWhether = true;
							});
						} else {
							this.soundList[index].isPlay = 2;
							this.updateKey = true;
							// uni.hideLoading();
							this.voiceAudioContext.pause();
							this.voiceAudioContext.onPause(() => {
								console.log('暂停播放');
							});
						}
					}, 500);
				}

			},

			//音频刷新
			async getSoundRefresh(id) {

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.soundRefresh,
					data: {
						uid: uni.getStorageSync('uid'),
						sound_id: id,
					}
				});
				if (result.errno == 0) {
					if (result.data) {
						this.$sun.toast("音频合成成功");
					} else {
						this.$sun.toast("正在合成中，请稍后重试", 'none');
					}
					setTimeout(() => {
						if (result.data) {
							this.getVideoSoundList();
						}
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//生成音频
			async getVideoSendTts() {

				if (!this.msgText) {
					this.$sun.toast("请输入音频文案", 'none');
					return;
				}

				if (this.msgText.length > 1000) {
					this.$sun.toast("请把文案限制在1000字以内", 'none');
					return;
				}

				if (!this.voiceObj.id) {
					this.$sun.toast("请选择一个音色", 'none');
					return;
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.videoSendTts,
					data: {
						uid: uni.getStorageSync('uid'),
						msg: this.msgText,
						speechRate: this.speed,
						pitchRate: this.pitch,
						name: this.voiceObj.name,
						voice_type: this.voiceObj.voice_type,
						voice_id: this.voiceObj.id
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.returnNav(2);
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//去克隆声音
			getVoice() {
				uni.navigateTo({
					url: '/pages/index/voice/voice'
				})
			},

			getId(id) {
				this.tabsId = id;
				if (id == 1) {
					this.soundObj = {
						name: '',
						url: '', //
						id: '', //
					}
				}
				if (id == 2) {
					this.getVideoSoundList();
				}
			},

			//默认音色
			async getTimbreList() {
				const result = await this.$http.post({
					url: this.$api.timbreList
				});
				if (result.errno == 0) {
					this.timbreList = result.data;
					// if (this.timbreList.length > 0) {
					// 	this.voiceObj = {
					// 		name: this.timbreList[0].name,
					// 		id: this.timbreList[0].id,
					// 		voice_type: 1
					// 		// media_url: this.voiceList[0].media_url
					// 	};
					// }
				}
			},

			//历史音频
			async getVideoSoundList() {
				const result = await this.$http.post({
					url: this.$api.videoSoundList,
					data: {
						uid: uni.getStorageSync('uid'),
						voice_channel: this.voiceType,
						page: 1,
						psize: 1000
					}
				});
				if (result.errno == 0) {

					this.soundList = result.data.list;

					for (let i = 0; i < this.soundList.length; i++) {
						this.soundList[i].isPlay = 2;
					}

				}
			},

			//声音克隆记录
			async getVoiceTrainList() {
				const result = await this.$http.post({
					url: this.$api.voiceTrainList,
					data: {
						uid: uni.getStorageSync('uid'),
						current_status: 'completed',
						train_mode: 1, //1-标准模式 2-高保真
						page: 1,
						psize: 200
					}
				});
				if (result.errno == 0) {

					this.voiceList = result.data.list;

				}
			},

		}
	}
</script>

<style lang="scss">
	.img-25 {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}

	.a-frame {
		width: 344rpx;
		padding: 30rpx;
		background-color: #323232;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}

	.frame-top {
		margin: 0 20rpx 20rpx;
		padding: 30rpx 0;
		border-bottom: 1px solid rgb(51, 51, 51);
	}

	.img-319 {
		width: 50rpx;
		height: 50rpx;
	}

	.copywriting {
		background-color: #32353A;
		border-radius: 10rpx;
		width: 110rpx;
		height: 110rpx;
		color: #FFF;
		margin-right: 30rpx;
	}

	.sel-tabs-2 {
		color: #999999;
		background-color: #2A2A2A;
	}

	.sel-tabs-1 {
		color: #FFF;
		background-color: #166DFD;
	}

	.sel-tabs {
		width: 104rpx;
		text-align: center;
		border-radius: 10rpx;
		margin-left: 36rpx;
		padding: 6rpx 0;
	}

	.c-but {
		width: 690rpx;
		padding: 22rpx 0;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118), rgb(0, 236, 255) 97.869%);
		margin: 0 0 20rpx;
		font-size: 32rpx;
		color: #FFF;
		text-align: center;
	}

	.p-title {
		font-weight: 600;
		letter-spacing: 4%;
		font-size: 40rpx;
		text-align: center;
		color: #FFF;
		margin-bottom: 30rpx;
	}

	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 42rpx;
		right: 30rpx;
	}

	.pop-bg {
		position: relative;
		width: 750rpx;
		background-repeat: no-repeat;
		background-size: contain;
		padding: 34rpx 30rpx;
		color: #FFF;
	}

	/*圆形加载*/
	.circle-loading {
		width: 44rpx;
		height: 44rpx;
		position: relative;
		margin: auto;

		.dot {
			position: absolute;
			top: 0;
			left: 0;
			width: 44rpx;
			height: 44rpx;
			animation: 1.5s loadrotate cubic-bezier(0.800, 0.005, 0.500, 1.000) infinite;

			&:after,
			.first-dot {
				content: '';
				position: absolute;
				width: 12rpx;
				height: 12rpx;
				background: #FFF;
				border-radius: 50%;
				left: 50%;
			}

			.first-dot {
				background: #fff;
				animation: 1.5s dotscale cubic-bezier(0.800, 0.005, 0.500, 1.000) infinite;

			}
		}
	}

	@for $i from 1 through 4 {
		.circle-loading {
			&>.dot:nth-child(#{$i}) {
				animation-delay: 0.15s*$i;
			}
		}
	}

	@keyframes loadrotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	@keyframes dotscale {

		0%,
		10% {
			width: 28rpx;
			height: 28rpx;
			margin-left: -2rpx;
			margin-top: -5rpx;
		}

		50% {
			width: 16rpx;
			height: 16rpx;
			margin-left: 0rpx;
			margin-top: 0rpx;
		}

		90%,
		100% {
			width: 28rpx;
			height: 28rpx;
			margin-left: -2rpx;
			margin-top: -5rpx;
		}
	}

	.img-94 {
		width: 30rpx;
		height: 30rpx;
	}

	.r-play {
		width: 70rpx;
		height: 70rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(76, 254, 124), rgb(2, 236, 252) 100%);
		margin-right: 20rpx;
	}

	.img-244 {
		width: 34rpx;
		height: 34rpx;
		margin-left: 30rpx;
	}

	.a-but {
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118), rgb(0, 236, 255) 97.869%);
		width: 710rpx;
		margin-left: 20rpx;
		padding: 26rpx 0;
		text-align: center;
		color: #FFF;
		font-weight: 600;
		font-size: 32rpx;
	}

	.bott-pos {
		position: fixed;
		bottom: 40rpx;
		width: 750rpx;
		z-index: 9;
	}

	.img-240 {
		width: 34rpx;
		height: 34rpx;
	}

	.img-238 {
		width: 90rpx;
		height: 90rpx;
		margin-right: 20rpx;
	}

	.list-public {
		background-color: #1C2022;
		padding: 30rpx 20rpx;
	}

	.nodata {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 10rpx;
	}

	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		// background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		border: 1px solid rgb(151, 249, 135);
		color: #97F987;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}

	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}

	.a-line {
		width: 6rpx;
		height: 22rpx;
		background-color: #FFF;
		border-radius: 4rpx;
		margin-right: 10rpx;
	}

	.img-243 {
		width: 82rpx;
		height: 48rpx;
	}

	.a-speed {
		width: 170rpx;
		padding: 10rpx 0;
		background-color: #32353A;
		border-radius: 70rpx;
		margin-right: 30rpx;
		color: #FFF;
	}

	.img-242 {
		width: 22rpx;
		height: 22rpx;
		margin-left: 10rpx;
	}

	textarea {
		width: 670rpx;
		padding: 20rpx;
		color: #FFF;
		height: 200rpx;
	}

	.msg-tips {
		font-size: 26rpx;
		color: #C9CACA;
		margin-left: auto;
	}

	.c-frame {
		width: 710rpx;
		background-color: #212429;
		border-radius: 10rpx;
		margin: 0 20rpx 30rpx;
	}

	.color_9B9B9B {
		color: #999898;
		font-size: 34rpx;
		margin-bottom: 10rpx;
	}

	.color_sel {
		color: #FFF;
		font-size: 34rpx;
		font-weight: 600;
		letter-spacing: 0%;
		margin-bottom: 10rpx;
	}

	.c-line-2 {
		background: #000;
	}

	.c-line-1 {
		background: #FFF;
	}

	.c-line {
		width: 66rpx;
		height: 8rpx;
		border-radius: 100rpx;
	}

	.c-top {
		padding: 30rpx 0;
	}

	page {
		background-color: #000;
		border: none;
	}
</style>