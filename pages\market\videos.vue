<template>
	<view>
		
		<view class="h_20rpx"></view>
		
		<!-- <video class="video-url" :src="base_video" :autoplay="true"></video> -->
		<image class="video-url" :src="base_video+'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"></image>
		
		<view class="v-but" @click="getOpen()">购买共享形象</view>
		
		<view class="h_20rpx"></view>
		
		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="text-align_center margin-bottom_60rpx">
					<view class="font-size_36rpx margin-bottom_20rpx">{{name}}</view>
					<view class="font-size_30rpx">￥<span class="font-size_40rpx margin-left_10rpx">{{price[priceIndex].price}}</span></view>
				</view>
				<view style="height: 60rpx;"></view>
				<view class="margin-bottom_20rpx">选择购买时长</view>
				<scroll-view :scroll-x="true" class="margin-bottom_60rpx" style="width: 700rpx; white-space: nowrap;" :scroll-with-animation="true">
					<view class="display-a">
						<block v-for="(item,index) in price" :key="index">
							<view @click="getTabs(item,index)" class="tabs-a" :class="priceIndex == index ? 'tabs-a-2' : 'tabs-a-1'">{{item.name}}</view>
						</block>
					</view>
				</scroll-view>
				<view style="height: 60rpx;"></view>
				<view class="display-a-js">
					<view class="pay-v" @click="save()">支付</view>
					<view class="cancel" @click="getClose()">取消</view>
				</view>
				
			</template>
		</sunui-popup>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				isWhether: true, //判断重复点击
				
				price: [], //价格
				priceIndex: 0,
				id: '', //资产ID
				base_video: '', //视频链接
				name: '',
				
			}
		},
		
		onLoad(options) {
			
			console.log("---->",options.price);
			
			if (options.price) {
				this.price = JSON.parse(options.price);
				console.log("---->",this.price);
			}
			if (options.id) {
				this.id = options.id;
			}
			if (options.base_video) {
				this.base_video = options.base_video;
			}
			if (options.name) {
				this.name = options.name;
			}
		},
		
		onShow() {
			
		},
		
		methods: {
			
			getTabs(obj,index) {
				this.priceIndex = index;
			},
			
			getOpen() {
				this.$refs.pop3.show({
					style: 'background-color:#fff;width:750rpx;border-radius: 10rpx 10rpx 0 0;padding: 50rpx 24rpx',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			
			getClose() {
				this.$refs.pop3.close();
			},
			
			async save() {
				
				if (uni.getStorageSync('uid')) {
					if (!this.isWhether) {
						return;
					}
					this.isWhether = false;
					
					const result = await this.$http.post({
						url: this.$api.createOrder,
						data: {
							uid: uni.getStorageSync('uid'),
							id: this.id,
							price: this.price[this.priceIndex].price,
							day_index: this.price[this.priceIndex].index
						}
					});
					if (result.errno == 0) {
						this.getClose();
						this.wxPay(result.data)
					} else {
						this.$sun.toast(result.message, 'none');
						this.isWhether = true;
					}
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "返回",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								
							}
						}
					})
				}
				
			},
			
			/*  微信支付  */
			async wxPay(log_no) {
				const result = await this.$http.post({
					url: this.$api.pay,
					data: {
						openid: uni.getStorageSync('openid'),
						price: this.price[this.priceIndex].price,
						log_no: log_no,
						name: ''
					}
				});
				if (result.errno == 0) {
					uni.requestPayment({
						provider: 'wxpay',
						timeStamp: result.data.timeStamp,
						nonceStr: result.data.nonceStr,
						package: result.data.package,
						signType: result.data.signType,
						paySign: result.data.paySign,
						success: async (res) => {
							this.$sun.toast("支付成功");
							setTimeout(() => {
								this.isWhether = true;
							}, 2000);
						},
						fail: (err) => {
							this.isWhether = true;
							this.$sun.toast("取消支付", 'error');
						}
					});
				} else {
					this.isWhether = true;
					if (result.errno == -1) {
						this.$sun.toast(result.message, 'none');
						return;
					}
					if (result.return_code == 'FAIL') {
						uni.showModal({
							title: '支付配置错误',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
			
								}
							}
						})
					} else {
						uni.showModal({
							title: '提示',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
			
								}
							}
						})
					}
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.cancel {
		background-color: #FA5551;
		width: 320rpx;
		text-align: center;
		padding: 26rpx 0;
		border-radius: 10rpx;
		color: #FFF;
		font-size: 30rpx;
	}
	
	.pay-v {
		background-color: #00CE55;
		width: 320rpx;
		text-align: center;
		padding: 26rpx 0;
		border-radius: 10rpx;
		color: #FFF;
		font-size: 30rpx;
	}
	
	.tabs-a-2 {
		background-color: #FA5551;
		color: #FFF;
	}
	.tabs-a-1{
		background-color: #EDEDED;
		color: #000;
	}
	
	.tabs-a {
		width: 190rpx;
		text-align: center;
		border-radius: 10rpx;
		padding: 20rpx 0;
		margin-right: 20rpx;
	}
	
	.v-but {
		width: 710rpx;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(0, 158, 255),rgb(135, 80, 242) 100%);
		padding: 24rpx 0;
		margin: 40rpx 20rpx 20rpx;
		color: #FFF;
		font-size: 32rpx;
		text-align: center;
	}
	
	.video-url {
		width: 648rpx;
		height: 1152rpx;
		margin-left: 51rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
	
</style>
