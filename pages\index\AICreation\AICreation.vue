<template>
	<view>
		
		<view style="height: 20rpx;"></view>
		
		<swiper :indicator-dots="indicatorDots" indicator-active-color="#FFFFFF" :autoplay="autoplay" :circular="true"
			:interval="interval" :duration="duration">
			<swiper-item v-for="(item,index) in banner" :key="index">
				<image class="pic-img" :src="item.pic_url" @click="changeUrl(index,item)"></image>
			</swiper-item>
		</swiper>
		
		<view class="a-top">
			<view class="display-a margin-bottom_30rpx">
				<view class="a-line"></view>
				<view class="font-size_32rpx color_FFFFFF">文案类型</view>
			</view>
			<view class="display-fw-js">
				<view class="a-frame display-a" @click="getAdd(18)">
					<image class="img-25" :src="imgUrl + '324.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">DeepSeek</view>
						<view class="color_D5D5D5 font-size_24rpx">DeepSeek文案</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAiTitle()">
					<image class="img-25" :src="imgUrl + '391.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">AI标题</view>
						<view class="color_D5D5D5 font-size_24rpx">批量生成标题</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(1)">
					<image class="img-25" :src="imgUrl + '309.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">企业宣传</view>
						<view class="color_D5D5D5 font-size_24rpx">企业宣传</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(19)">
					<image class="img-25" :src="imgUrl + '181.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">账号装修</view>
						<view class="color_D5D5D5 font-size_24rpx">一键起号</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(2)">
					<image class="img-25" :src="imgUrl + '310.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">同城团购</view>
						<view class="color_D5D5D5 font-size_24rpx">团购产品文案</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(3)">
					<image class="img-25" :src="imgUrl + '311.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">电商带货</view>
						<view class="color_D5D5D5 font-size_24rpx">带货营销文案</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(4)">
					<image class="img-25" :src="imgUrl + '312.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">知识科普</view>
						<view class="color_D5D5D5 font-size_24rpx">知识获取很方便</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(5)">
					<image class="img-25" :src="imgUrl + '313.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">情感专家</view>
						<view class="color_D5D5D5 font-size_24rpx">情感问题一点通</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(6)">
					<image class="img-25" :src="imgUrl + '314.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">口播文案</view>
						<view class="color_D5D5D5 font-size_24rpx">播音必备神器</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(7)">
					<image class="img-25" :src="imgUrl + '315.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">朋友圈营销</view>
						<view class="color_D5D5D5 font-size_24rpx">轻松搞定营销文案</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(8)">
					<image class="img-25" :src="imgUrl + '316.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">小红书笔记</view>
						<view class="color_D5D5D5 font-size_24rpx">轻松生成笔记</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(9)">
					<image class="img-25" :src="imgUrl + '318.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">智能代写</view>
						<view class="color_D5D5D5 font-size_24rpx">一键智能代写</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(10)">
					<image class="img-25" :src="imgUrl + '317.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">文案仿写</view>
						<view class="color_D5D5D5 font-size_24rpx">模仿生成营销文案</view>
					</view>
				</view>
				<view class="a-frame display-a" @click="getAdd(16)">
					<image class="img-25" :src="imgUrl + '325.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">AI提取文案</view>
						<view class="color_D5D5D5 font-size_24rpx">一键提取文案</view>
					</view>
				</view>
			</view>
		</view>
		<block v-if="system.banner_id">
			<view style="width: 710rpx;height: 240rpx;margin-left: 20rpx;border-radius: 10rpx;">
				<ad v-if="system.banner_id" :unit-id="system.banner_id"></ad>
			</view>
			<view class="h_20rpx"></view>
		</block>
		<sunui-tabbar :fixed="true" :current="tabIndex" :types="3" tintColor="#A3C3FF"
			backgroundColor="#1B1B1B"></sunui-tabbar>

	</view>
</template>

<script>
	export default {
		data() {
			return {

				tabIndex: 1,
				// 轮播图
				indicatorDots: true,
				autoplay: true,
				interval: 4000,
				duration: 500,

				banner: [],

				imgUrl: this.$imgUrl,

				system: uni.getStorageSync('system'),

			}
		},

		onLoad() {
			this.getBanner();
		},

		onShow() {

		},

		methods: {
			
			getAiTitle() {
				uni.navigateTo({
					url: '/pages/index/AICreation/aiTitle'
				})
			},

			//获取用户数据
			async getInfo(type) {
				const result = await this.$http.get({
					url: this.$api.getUserInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					if (!result.data) {
						uni.navigateTo({
							url: '/pages/my/userInfo?type=' + type
						})
					} else if (type === 19) {
						uni.navigateTo({
							url: '/subPackages/subPackageA/stream?type=' + type
						})
					} else {
						uni.navigateTo({
							url: '/pages/index/AICreation/release?type=' + type + '&typeIndex=1'
						})
					}
				} else {
					this.$sun.toast(result.message, 'none');
				}

			},


			getAdd(type) {

				if (uni.getStorageSync('uid')) {
					if (type == 16) {
						uni.navigateTo({
							url: '/pages/index/AICreation/extract?typeIndex=1'
						})
					} else if (type === 19) {
						uni.navigateTo({
							url: '/subPackages/subPackageA/stream?type=' + type
						})
					} else {
						uni.navigateTo({
							url: '/pages/index/AICreation/release?type=' + type + '&typeIndex=1'
						})
					}

					// {
					// 	this.getInfo(type)
					// }

				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								// this.navig();
							}
						}
					})
				}
			},
			
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表
			
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			
			//自定义跳转
			changeUrl(index, values) {
				if (values.type == 1) {
					uni.navigateTo({
						url: values.url
					});
				}
				if (values.type == 2) {
			
					if (values.appid) {
						wx.navigateToMiniProgram({
							appId: values.appid,
							success: (res) => {
								// 打开成功
								// console.log('成功', res);
							},
							fail: (err) => {
								// console.log('失败', err);
							}
						})
					} else {
						this.$sun.toast("请检查跳转外部小程序的APPID是否正确", 'none');
					}
				}
			},

			//轮播图
			async getBanner() {
				const result = await this.$http.post({
					url: this.$api.banner,
					data: {
						b_type: 3
					}
				});
				if (result.errno == 0) {
					this.banner = result.data;
				}
			},

		}
	}
</script>

<style lang="scss">
	
	.color_B1FEFF {
		color: #B1FEFF;
		font-size: 24rpx;
		margin-left: auto;
	}
	
	.img-62 {
		width: 30rpx;
		height: 30rpx;
	}
	
	.img-25 {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}
	
	.a-frame {
		width: 344rpx;
		padding: 30rpx;
		background-color: #323232;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}
	
	.a-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245),rgb(66, 72, 244) 100%);
		margin-right: 14rpx;
	}
	
	.a-top {
		width: 710rpx;
		margin: 0 20rpx;
	}
	
	.pic-img {
		width: 710rpx;
		height: 280rpx;
		// margin: 0 20rpx 24rpx;
		border-radius: 10rpx;
	}

	swiper {
		width: 710rpx;
		height: 280rpx;
		margin: 0 20rpx 30rpx;
	}
	
	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
	
</style>