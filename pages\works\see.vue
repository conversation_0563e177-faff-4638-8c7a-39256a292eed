<template>
	<view>
		<view class="h_20rpx"></view>
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'186.png'+')'}">
			<view class="display-fw-a">
				<view class="width_176rpx-center">
					<view class="font-weight_bold font-size_30rpx">{{obj.dy_like}}</view>
					<view>D音点赞</view>
				</view>
				<view class="width_176rpx-center">
					<view class="font-weight_bold font-size_30rpx">{{obj.dy_comment}}</view>
					<view>D音评论</view>
				</view>
				<view class="width_176rpx-center">
					<view class="font-weight_bold font-size_30rpx">{{obj.dy_play}}</view>
					<view>D音播放</view>
				</view>
				<view class="width_176rpx-center">
					<view class="font-weight_bold font-size_30rpx">{{obj.dy_share}}</view>
					<view>D音分享</view>
				</view>
				<view class="width_176rpx-center">
					<view class="font-weight_bold font-size_30rpx">{{obj.ks_like}}</view>
					<view>K手点赞</view>
				</view>
				<view class="width_176rpx-center">
					<view class="font-weight_bold font-size_30rpx">{{obj.ks_comment}}</view>
					<view>K手评论</view>
				</view>
				<view class="width_176rpx-center">
					<view class="font-weight_bold font-size_30rpx">{{obj.ks_play}}</view>
					<view>K手播放</view>
				</view>
				<!-- <view class="width_176rpx-center">
					<view class="font-weight_bold font-size_30rpx">{{obj.ks_share}}</view>
					<view>K手分享</view>
				</view> -->
			</view>
		</view>
		<view class="display-a padding_20rpx">
			<block v-for="(item,index) in arr" :Key="index">
				<view @click="getTabs(item.id)" class="tabs" :class="arrId == item.id ? 'tabs-2' : 'tabs-1'">{{item.name}}</view>
			</block>
			<view class="display-a margin-left-auto">
				<picker mode="date" fields="month" :value="birth" :start="startDate" :end="endDate"
					@change="bindDateChange">
					<input type="text" style="width: 120rpx;color: #FFF;" disabled placeholder="日期筛选" v-model="birth"
						placeholder-class="font-size_28rpx" />
				</picker>
				<image class="img-99" :src="imgUrl+'187.png'"></image>
			</view>
		</view>
		
		<mescroll-body ref="mescrollRef" :optionIcon="optionIcon" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a padding-bottom_20rpx p-bo">
						<image class="avatar" :src="item.avatar?item.avatar:imgUrl+'30.png'"></image>
						<view>
							<view class="color_FFFFFF font-size_32rpx margin-bottom_10rpx">{{item.nickname?item.nickname:'默认昵称'}}</view>
							<view class="display-a">
								<view class="list-way" :class="item.way == 1 ? 'way-1' : 'way-2'">{{item.way == 1 ? 'D音' : 'K手'}}</view>
								<view class="font-size_24rpx color_999999">{{item.create_time}}</view>
							</view>
						</view>
					</view>
					<view class="display-a padding-top_20rpx">
						<view class="width_172rpx-center">
							<view class="font-weight_bold font-size_30rpx">{{item.total_like}}</view>
							<view class="font-size_26rpx">点赞数</view>
						</view>
						<view class="width_172rpx-center">
							<view class="font-weight_bold font-size_30rpx">{{item.total_comment}}</view>
							<view class="font-size_26rpx">评论数</view>
						</view>
						<view class="width_172rpx-center">
							<view class="font-weight_bold font-size_30rpx">{{item.total_play}}</view>
							<view class="font-size_26rpx">播放数</view>
						</view>
						<view class="width_172rpx-center" v-if="item.way == 1">
							<view class="font-weight_bold font-size_30rpx">{{item.total_share}}</view>
							<view class="font-size_26rpx">分享数</view>
						</view>
					</view>
				</view>
			</block>
		</mescroll-body>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				voideId: '',
				
				type: '', //1视频合成 2换脸
				
				obj: {},
				
				arr: [
					{id: '', name: '全部'},
					{id: '1', name: 'D音'},
					{id: '2', name: 'K手'},
				],
				arrId: '',
				
				birth: '',
				yeartime: '',
				monthtime: '',
				
			}
		},
		
		onLoad(options) {
			//获取系统信息
			uni.getSystemInfo({ 
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 384;
				}
			})
			var myDate = new Date();
			this.yeartime = myDate.getFullYear();
			this.monthtime = myDate.getMonth() + 1;
			if (this.monthtime.toString().length == 1) {
				this.monthtime = '0' + this.monthtime;
			}
			this.birth = this.yeartime + '-' + this.monthtime;
			if (options.id) {
				this.voideId = options.id;
				// this.type = options.type;
				this.getDataStatis();
				setTimeout(()=> {
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
				}, 1000);
			}
		},
		
		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		
		onShow() {
			
		},
		
		methods: {
			
			/*  日期选择  */
			bindDateChange(e) {
				// console.log("选择的日期", e.target.value);
				this.birth = e.target.value;
				this.yeartime = e.target.value.split('-')[0];
				this.monthtime = e.target.value.split('-')[1];
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();
			
				if (type === 'start') {
					year = year - 100;
				} else if (type === 'end') {
					year = year;
				}
				month = month > 9 ? month : '0' + month;;
				// day = day > 9 ? day : '0' + day;
				return `${year}-${month}`;
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.repostClipList,
					data: {
						uid: uni.getStorageSync("uid"),
						video_id: this.voideId,
						way: this.arrId,
						type: this.type,
						year: this.yeartime,
						month: this.monthtime,
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			getTabs(id) {
				this.arrId = id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			
			//统计
			async getDataStatis() {
				const result = await this.$http.post({
					url: this.$api.dataClipStatis,
					data: {
						uid: uni.getStorageSync('uid'),
						video_id: this.voideId,
						type: this.type
					}
				});
				if (result.errno == 0) {
					this.obj = result.data;
				}else {
					this.$sun.toast(result.message,'none');
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.width_172rpx-center {
		width: 172rpx;
		text-align: center;
		color: #FFF;
	}
	
	.p-bo {
		border-bottom: 1px solid rgb(58, 58, 58);
	}
	
	.way-2 {
		border: 1px solid rgb(249, 121, 37);
		color: #F97925;
	}
	
	.way-1 {
		border: 1px solid rgb(22, 109, 253);
		color: #166DFD;
	}
	
	.list-way {
		width: 66rpx;
		text-align: center;
		font-size: 24rpx;
		border-radius: 10rpx;
		padding: 2rpx 0;
		margin-right: 20rpx;
	}
	
	.avatar {
		width: 100rpx;
		height: 100rpx;
		margin-right: 16rpx;
		border-radius: 50%;
	}
	
	.list-public {
		background-color: #1F1F1F;
		padding: 30rpx 20rpx;
	}
	
	.img-99 {
		width: 20rpx;
		height: 20rpx;
		margin-top: 2rpx;
	}
	
	.tabs-2 {
		background-color: #166DFD;
		color: #FFF;
	}
	
	.tabs-1 {
		background-color: #2A2A2A;
		color: #999999;
	}
	
	.tabs {
		width: 106rpx;
		text-align: center;
		border-radius: 10rpx;
		padding: 8rpx 0;
		font-size: 26rpx;
		margin-right: 20rpx;
	}
	
	.width_176rpx-center {
		width: 178rpx;
		margin-bottom: 40rpx;
		color: #FFF;
	}
	
	.bg {
		width: 718rpx;
		height: 268rpx;
		background-repeat: no-repeat;
		background-size: contain;
		margin: 0 16rpx;
		padding-top: 26rpx;
	}
	
	page {
		background-color: #000;
		border: none;
	}
	
</style>
