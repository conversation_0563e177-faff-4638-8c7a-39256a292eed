<template>
	<view>

		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption" :optionIcon="optionIcon" @emptyclick="emptyClick">

			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a-js margin-bottom_30rpx">
						<view class="r-name" v-if="type == 1">{{item.name}}</view>
						<view class="r-name" v-if="type == 2">
							<block v-if="tabId == 1">
								{{item.answer+'#'+item.challenges}}
							</block>
							<block v-else>
								{{item.answer}}
							</block>
						</view>
						<image v-if="type == 2" @click="selGetObj(item,2)" class="img-346"
							:src="selId.indexOf(item.id) != -1 ? imgUrl+'384.png' : imgUrl+'345.png'"></image>
						<image v-if="type == 1" @click="selGetObj(item,1)" class="img-346"
							:src="selObj.id == item.id ? imgUrl+'384.png' : imgUrl+'345.png'"></image>
					</view>
					<view class="display-a">
						<block v-if="type == 1">
							<view class="color_CBCACA margin-right_16rpx">字数: {{item.words}}字</view>
						</block>
						<view class="color_CBCACA">{{item.create_time}}</view>
						<view class="display-a margin-left-auto" @click="detail(item.name,item.id)" v-if="type == 1">
							<view class="color_CBCACA">查看详情</view>
							<image class="img-58" :src="imgUrl + '58.png'"></image>
						</view>
						<view class="display-a margin-left-auto" @click="edit(item)" v-if="type == 2">
							<view class="color_CBCACA">修改标题</view>
							<image class="img-58" :src="imgUrl + '58.png'"></image>
						</view>
					</view>
				</view>
			</block>

		</mescroll-body>

		<view style="height: 160rpx;"></view>
		<view class="next" @click="returnNav()">确认选择</view>

		<!-- 修改标题弹窗 -->
		<sunui-popup ref="editPopup" :style="{'background-color': '#1B1C1F'}">
			<template v-slot:content>
				<view class="edit-popup-content">
					<view class="edit-popup-title">修改标题</view>
					<view class="edit-popup-form">
						<view class="edit-popup-label">标题内容</view>
						<textarea v-model="tempEditData.answer" class="edit-popup-textarea" placeholder="请输入标题内容"
							placeholder-class="placeholder"></textarea>

						<view class="edit-popup-label" v-if="tabId == 1">话题</view>
						<textarea v-if="tabId == 1" v-model="tempEditData.challenges" class="edit-popup-textarea"
							placeholder="请输入话题" placeholder-class="placeholder"></textarea>
					</view>
					<view class="edit-popup-btn" @click="confirmEdit">确认修改</view>
				</view>
			</template>
		</sunui-popup>

	</view>
</template>

<script>
	export default {
		data() {
			return {

				imgUrl: this.$imgUrl,

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],

				selId: [], //选中的标题
				selObj: {}, //选中的文案

				windowHeight: '',

				type: '', //2 标题 1 文案

				tabId: '', //1。D音 2。K手 3。视频号 4.小红薯 5.B站
				optionIcon: {},
				// 修改标题数据
				editData: {},
				// 临时存储修改的数据
				tempEditData: {
					answer: '',
					challenges: ''
				}
			}
		},

		onLoad(options) {
			if (options.tabId) {
				this.tabId = options.tabId;
			}
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 190;
				},
			})
		},

		onShow() {

			let pagearr = getCurrentPages(); //获取应用页面栈
			let currentPage = pagearr[pagearr.length - 1]; //获取当前页面信息
			if (currentPage.options.type) {
				this.type = currentPage.options.type;
				this.$sun.title(this.type == 1 ? '选择文案' : '选择标题');
				if (this.type == 1) {
					this.optionIcon = {
						btnText: '点击生成文案'
					}
				} else {
					this.optionIcon = {
						btnText: '点击生成标题'
					}
				}
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			} else {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}

		},

		methods: {
			edit(item) {
				this.editData = item;
				// 复制数据到临时变量，避免直接修改原始数据
				this.tempEditData.answer = item.answer || '';
				this.tempEditData.challenges = item.challenges || '';
				// 打开弹窗
				this.$refs.editPopup.show({
					style: 'width: 90%; background-color: #1B1C1F; border-radius: 20rpx;',
					anim: 'scaleIn',
					shadeClose: true
				});
			},

			// 确认修改标题
			async confirmEdit() {
				// 更新原始数据
				this.editData.answer = this.tempEditData.answer;
				this.editData.challenges = this.tempEditData.challenges;

				// 关闭弹窗
				this.$refs.editPopup.close();

				// 调用服务器接口更新标题
				try {
					const result = await this.$http.post({
						url: this.$api.updateTitle,
						data: {
							uid: uni.getStorageSync('uid'),
							id: this.editData.id,
							answer: this.editData.answer,
							challenges: this.editData.challenges
						}
					});

					if (result.errno == 0) {
						uni.showToast({
							title: '修改成功',
							icon: 'none'
						});

						// 修改成功后重新加载列表数据
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
					} else {
						uni.showToast({
							title: result.message || '修改失败',
							icon: 'none'
						});
					}
				} catch (e) {
					uni.showToast({
						title: '修改失败，请重试',
						icon: 'none'
					});
				}
			},

			emptyClick() {
				if (this.type == 1) {
					// 生成文案
					uni.navigateTo({
						url: '/pages/index/AICreation/release?type=8&typeIndex=1&back=1'
					})
				} else {
					// 生成标题
					uni.navigateTo({
						url: '/pages/index/AICreation/aiTitle?back=1'
					})
				}

			},

			//返回上一页
			returnNav() {

				let navObj = null;
				if (this.type == 2) {
					navObj = this.selId;
				}
				if (this.type == 1) {
					navObj = this.selObj;
				}
				var pages = getCurrentPages();
				var prevPage = pages[pages.length - 2]; //上一个页面
				prevPage.$vm.otherVideoTitleFun(navObj, this.type); //重点$vm
				uni.navigateBack();
			},

			//选中
			selGetObj(obj, type) {

				if (type == 2) {
					let getIndex = this.selId.indexOf(obj.id);
					if (getIndex == -1) {
						this.selId.push(obj.id);
					} else {
						this.selId.splice(getIndex, 1);
					}
				}

				if (type == 1) {
					this.selObj = obj;
				}


			},

			//查看详情
			detail(name, id) {
				uni.navigateTo({
					url: '/pages/index/AICreation/detail?name=' + name + '&id=' + id
				})
			},

			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.type == 1 ? this.$api.aiCreateLog : this.$api.titleList,
					data: {
						uid: uni.getStorageSync('uid'),
						name: '',
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},

		},

	}
</script>

<style lang="scss">
	.sunui-pop-main {
		width: 90%;
	}

	.sunui-pop-child {
		width: 100% !important;
	}

	.next {
		position: fixed;
		bottom: 50rpx;
		width: 710rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118), rgb(0, 236, 255) 97.869%);
		color: #000;
		font-size: 30rpx;
		padding: 30rpx 0;
		margin: 0 20rpx;
	}

	.img-346 {
		width: 40rpx;
		height: 40rpx;
	}

	.color_CBCACA {
		color: #CBCACA;
		font-size: 26rpx;
	}

	.r-name {
		font-size: 32rpx;
		width: 600rpx;
	}

	.img-58 {
		width: 28rpx;
		height: 28rpx;
		margin-left: 8rpx;
	}

	.list-public {
		background-color: #1B1C1F;
		padding: 24rpx 26rpx;
		color: #FFF;
	}

	.edit-popup-content {
		padding: 30rpx;
		color: #FFFFFF;
	}

	.edit-popup-title {
		font-size: 36rpx;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.edit-popup-form {
		margin-bottom: 30rpx;
	}

	.edit-popup-label {
		font-size: 28rpx;
		margin-bottom: 15rpx;
		color: #CBCACA;
	}

	.edit-popup-textarea {
		width: 100%;
		height: 160rpx;
		background-color: #2C2C2E;
		border-radius: 10rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
		color: #FFFFFF;
		box-sizing: border-box;
	}

	.edit-popup-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: linear-gradient(90.00deg, rgb(79, 255, 118), rgb(0, 236, 255) 97.869%);
		border-radius: 100rpx;
		color: #000;
		font-size: 30rpx;
	}

	.placeholder {
		color: rgba(255, 255, 255, 0.5);
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
		padding-top: 20rpx;
	}
</style>