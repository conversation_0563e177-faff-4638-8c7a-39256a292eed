<template>
	<view v-if="uid">
		<view class="h_20rpx"></view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="font-size_32rpx margin-bottom_20rpx">{{item.name}}</view>
					<view class="display-a-js list-date">
						<view class="font-size_26rpx">采集数量: ( {{item.gather_count}} ) 条</view>
						<view class="font-size_26rpx">{{item.create_time}}</view>
					</view>
					<view class="display-a">
						<view class="display-a-jc list-look" @click="getLook(item.id)">
							<view class="font-size_26rpx">查看</view>
							<image class="img-421" :src="imgUrl+'421.png'"></image>
						</view>
						<view class="display-a-jc list-refresh" @click="getRefresh(item.id)">
							<view class="font-size_26rpx">刷新</view>
							<image class="img-421 margin-left_10rpx" :src="imgUrl+'422.png'"></image>
						</view>
					</view>
				</view>
			</block>
		</mescroll-body>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				uid: '',
				
				imgUrl: this.$imgUrl,
				
				windowHeight: '',
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				isWhether: true, //判断重复点击
				
				
			}
		},
		
		onLoad() {
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					
					this.windowHeight = res.windowHeight * 2 - 24;
				},
				fail(err) {
					// console.log(err);
				}
			})
		},
		
		onShow() {
			if (uni.getStorageSync('uid')) {
				this.uid = uni.getStorageSync('uid');
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},
		
		methods: {
			
			//查看
			getLook(id) {
				uni.navigateTo({
					url: '/pages/index/ipGather/look?id='+id
				})
			},
			
			//刷新
			async getRefresh(id) {
				
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				
				const result = await this.$http.post({
					url: this.$api.homepageVideoRefresh,
					data: {
						id: id
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message,'none');
					setTimeout(() => {
						this.isWhether = true;
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
				
			},
			
			async upCallback(scroll) {
				
				const result = await this.$http.post({
					url: this.$api.homepageList,
					data: {
						uid: uni.getStorageSync('uid'),
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.list-refresh {
		width: 120rpx;
		background-color: #1EFF8B;
		color: #000;
		padding: 14rpx 0;
		border-radius: 10rpx;
		margin-left: 40rpx;
	}
	
	.list-look {
		width: 120rpx;
		border-radius: 10rpx;
		border: 1px solid #1EFF8B;
		color: #1EFF8B;
		padding: 14rpx 0;
		margin-left: auto;
	}
	
	.list-date {
		color: #a8a8a8;
		padding-bottom: 20rpx;
		margin-bottom: 20rpx;
		border-bottom: 1px solid #393939;
	}
	
	.img-421 {
		width: 32rpx;
		height: 32rpx;
	}
	
	.list-public {
		background-color: #262626;
		padding: 30rpx 20rpx;
		color: #FFF;
	}
	
	page {
		border: none;
		background: #000;
		width: 100%;
		overflow-x: hidden !important;
	}
	
</style>
