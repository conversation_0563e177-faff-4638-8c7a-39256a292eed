<template>
	<view>
		
		<view class="tab">
			<block v-for="(item,index) in arr" :key="index">
				<view @click="sel(item.id)" :class="selIndex == item.id?'sel':''">{{item.name}}</view>
			</block>
		</view>
		
		<view class="display-a search-x">
			<icon type="search" size="16" style="margin-left: 240rpx;margin-right: 20rpx;" ></icon>
			<input type="text" placeholder="请输入成员昵称" v-model="nickname" placeholder-class="color_999999" @input="inputChange" @confirm="confirmChange" />
		</view>
		
		<view class="display-a-js padding_0_30rpx margin-bottom_30rpx">
			<view class="font-size_26rpx color_757575">{{selIndex == 1 ? '直推团队' : '间推团队'}}共计 <span class="color_1C6EFF margin_0_10rpx font-weight_bold">{{total}}</span> 人</view>
			<view class="display-a margin-left-auto">
				<picker mode="date" fields="month" :value="birth" :start="startDate" :end="endDate"
					@change="bindDateChange">
					<input type="text" style="width: 120rpx;text-align: center;" disabled placeholder="日期筛选" v-model="birth"
						placeholder-class="font-size_28rpx" />
				</picker>
				<image class="img-99" :src="imgUrl+'99.png'"></image>
			</view>
		</view>
		
		<!-- 列表 -->
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a padding-bottom_30rpx p-bo">
						<!-- <image class="avatar" :src="item.avatar" mode=""></image> -->
						<!-- <image class="avatar" :src="item.avatar"></image> -->
						<image class="avatar" :src="item.is_member == 1 ? imgUrl + '160.png' : imgUrl + '159.png'"></image>
						<view style="width: 380rpx;">
							<view class="display-a margin-bottom_20rpx">
								<view class="font-size_36rpx color_FFFFFF" style="max-width: 200rpx;">{{item.nickname}}</view>
								<!-- <view class="display-a vip" v-if="item.is_member == 1">
									<view class="img-30">
										<image :src="imgUrl+'30.png'"></image>
									</view>
									<view class="font-size_24rpx margin-left_10rpx color_9E6205">VIP用户</view>
								</view> -->
							</view>
							<view class="level-name" v-if="item.level_name">{{item.level_name}}</view>
							<!-- <view class="color_999999 font-size_24rpx">{{item.create_time}}</view> -->
						</view>
						<view class="margin-left-auto text-align_center" style="max-width: 140rpx;">
							<view class="margin-bottom_20rpx color_FF0000 font-size_32rpx font-weight_bold">{{item.money}}</view>
							<view class="color_999999 font-size_24rpx">共获得佣金</view>
						</view>
					</view>
					<view class="display-a" style="padding: 30rpx 0 0;">
						<view class="color_999999 font-size_26rpx">{{item.create_time}}</view>
						<image @click="callPhone(item.telphone)" class="img-360" :src="imgUrl+'360.png'"></image>
						<view @click="callPhone(item.telphone)" class="font-size_26rpx color_1C6EFF">联系TA</view>
					</view>
				</view>
				
			</block>
		</mescroll-body>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				// optionIcon: {
				// 	icon: this.$imgUrl+'tabbar/nodata2.png'
				// },
				
				imgUrl: this.$imgUrl,
				
				selIndex: 1,
				nickname: '', //搜索名称
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				birth: '',
				yeartime: '',
				monthtime: '',
				
				arr: [],
				
				total: '',//总条数
				
			}
		},
		
		onLoad(options) {
			//获取系统信息
			uni.getSystemInfo({ 
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 292;
				}
			})
			if (options.is_level == 1){
				this.arr = [{id:1,name:'直推团队'}];
			}
			if (options.is_level == 2){
				this.arr = [{id:1,name:'直推团队'},{id:2,name:'间推团队'}];
			}
		},
		
		onShow() {
			// var myDate = new Date();
			// this.yeartime = myDate.getFullYear();
			// this.monthtime = myDate.getMonth() + 1;
			// if (this.monthtime.toString().length == 1) {
			// 	this.monthtime = '0' + this.monthtime;
			// }
			// this.birth = this.yeartime + '-' + this.monthtime;
			setTimeout(()=> {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}, 1000);
		},
		
		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		
		methods: {
			
			//拨打电话
			callPhone(phone) {
				if (phone) {
					this.$sun.phone(phone);
				} else {
					this.$sun.toast("暂无联系方式", 'error');
				}
			},
			
			/*  日期选择  */
			bindDateChange(e) {
				// console.log("选择的日期", e.target.value);
				this.birth = e.target.value;
				this.yeartime = e.target.value.split('-')[0];
				this.monthtime = e.target.value.split('-')[1];
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();
			
				if (type === 'start') {
					year = year - 100;
				} else if (type === 'end') {
					year = year;
				}
				month = month > 9 ? month : '0' + month;;
				// day = day > 9 ? day : '0' + day;
				return `${year}-${month}`;
			},
			
			// 输入事件
			inputChange(e) {
				this.nickname = e.detail.value;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
				
			},
			confirmChange(e) {
				this.nickname = e.detail.value;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.brokerageTeamList,
					data: {
						uid: uni.getStorageSync("uid"),
						nickname: this.nickname,
						type: this.selIndex,
						year: this.yeartime,
						month: this.monthtime,
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.total = result.data.total;
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			sel(id) {
				this.selIndex = id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
		}
	}
</script>

<style lang="scss">
	
	.img-360 {
		width: 44rpx;
		height: 44rpx;
		margin-right: 10rpx;
		margin-left: auto;
	}
	
	.p-bo {
		border-bottom: 1px solid rgb(43, 43, 43);
	}
	
	.level-name {
		background-color: #FFDC00F5;
		padding: 6rpx 10rpx;
		border-radius: 10rpx;
		font-size: 24rpx;
		margin-left: 16rpx;
	}
	
	.list-public {
		background-color: #1C1C1C;
		padding: 30rpx;
	}
	
	.img-30 {
		
		width: 52rpx;
		background-color: #E6B966;
		border-radius: 10rpx 0 0 10rpx;
		padding-top: 8rpx;
		
		image {
			width: 38rpx;
			height: 38rpx;
			margin: 0 8rpx;
		}
		
	}
	
	.vip {
		width: 160rpx;
		background-color: #ECCE90;
		border-radius: 10rpx;
		margin-left: 20rpx;
	}
	
	.avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	.img-99 {
		width: 17rpx;
		height: 13rpx;
	}
	
	.search-x {
		width: 710rpx;
		margin: 20rpx 20rpx 30rpx;
		background-color: #1C1C1C;
		border-radius: 100rpx;
		padding: 20rpx;
	}
	
	.tab {
		display: flex;
		align-items: center;
		width: 750rpx;
		height: 80rpx;
		color: #897F7F;
		view {
			padding: 18rpx 0;
			margin: 0 131rpx;
			text-align: center;
		}
	}
	
	.sel {
		color: #1C6EFF;
		font-weight: bold;
		border-bottom: 2px solid #1C6EFF;
	}
	
	page {
		border: none;
		background-color: #000;
		width: 100%;
		overflow-x: hidden;
	}

</style>
