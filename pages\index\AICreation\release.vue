<template>
	<view>
		<view class="r-top" v-if="type">
			<block v-if="type == 6 || type > 7">
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">文案标题</view>
					<view class="red-dot">*</view>
				</view>
				<input type="text" v-model="name" class="r-input margin-bottom_40rpx" placeholder="请输入文案标题"
					placeholder-class="placeholder" />
			</block>
			<block v-if="type == 1 || type == 2 || type == 3">
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">{{type == 1 ? '公司名称' : type == 2 ? '店铺名称' : '商品名称'}}
					</view>
					<view class="red-dot">*</view>
				</view>
				<input type="text" v-model="name" class="r-input margin-bottom_40rpx"
					:placeholder="type == 1 ? '请输入公司名称' : type == 2 ? '请输入店铺名称' : '请输入商品名称'"
					placeholder-class="placeholder" />
				<block v-if="type == 1">
					<view class="display-a margin-bottom_20rpx">
						<view class="r-line"></view>
						<view class="color_FFFFFF font-size_30rpx">公司简称</view>
						<view class="red-dot">*</view>
					</view>
					<input type="text" v-model="store_introduction" class="r-input margin-bottom_40rpx"
						placeholder="请输入公司简称" placeholder-class="placeholder" />
				</block>
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">
						<block v-if="type == 1">公司特色</block>
						<block v-if="type == 2">团购套餐</block>
						<block v-if="type == 3">商品特色</block>
					</view>
					<view class="red-dot">*</view>
				</view>
				<input type="text" v-model="product" class="r-input margin-bottom_40rpx" v-if="type == 1"
					placeholder="请输入公司特色，优势" placeholder-class="placeholder" />
				<input type="text" v-model="product" class="r-input margin-bottom_40rpx" v-if="type == 2"
					placeholder="请输入团购套餐名称和特色" placeholder-class="placeholder" />
				<input type="text" v-model="product" class="r-input margin-bottom_40rpx" v-if="type == 3"
					placeholder="请输入商品的一些特色和优势" placeholder-class="placeholder" />
				<view class="display-a margin-bottom_40rpx">
					<view v-if="type == 1 || type == 3">
						<view class="display-a margin-bottom_20rpx">
							<view class="r-line"></view>
							<view class="color_FFFFFF font-size_30rpx">
								<block v-if="type == 1">主营业务</block>
								<block v-if="type == 3">优惠价格</block>
							</view>
							<view class="red-dot">*</view>
						</view>
						<block v-if="type == 1">
							<textarea v-model="desc" placeholder="请描述企业简介" placeholder-class="placeholder"></textarea>
						</block>
						<input type="digit" v-model="price" class="r-input" v-if="type == 3" placeholder="请输入商品的活动价格"
							placeholder-class="placeholder" />
					</view>
					<block v-if="type == 2">
						<view>
							<view class="display-a margin-bottom_20rpx">
								<view class="r-line"></view>
								<view class="color_FFFFFF font-size_30rpx">套餐原价</view>
								<view class="red-dot">*</view>
							</view>
							<input type="digit" v-model="originalPrice" class="r-input" style="width: 300rpx;"
								placeholder="请输入原价" placeholder-class="placeholder" />
						</view>
						<view class="margin-left-auto">
							<view class="display-a margin-bottom_20rpx">
								<view class="r-line"></view>
								<view class="color_FFFFFF font-size_30rpx">团购价格</view>
								<view class="red-dot">*</view>
							</view>
							<input type="digit" v-model="price" class="r-input" style="width: 300rpx;"
								placeholder="请输入团购价格" placeholder-class="placeholder" />
						</view>
					</block>
				</view>
			</block>

			<block v-if="type == 2">
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">商业定位信息</view>
				</view>
				<textarea class="margin-bottom_40rpx" maxlength="300" v-model="desc"
					placeholder="商家特色(如:10年老店、网红打卡点).独特优势(如:独家配方、明星产品).客户评价数据(好评率、回购率)"
					placeholder-class="placeholder"></textarea>
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">发布渠道</view>
					<view class="red-dot">*</view>
				</view>
				<view class="display-a margin-bottom_40rpx">
					<block v-for="(item,index) in channelList" :key="index">
						<view @click="getChannel(item)" class="font-size-1"
							:class="item.name == channel ? 'font-size-3' : 'font-size-2'">{{item.name}}</view>
					</block>
				</view>
				<block>
					<view class="display-a margin-bottom_20rpx">
						<view class="r-line"></view>
						<view class="color_FFFFFF font-size_30rpx">目标年龄阶段</view>
						<view class="red-dot">*</view>
					</view>
					<input type="text" v-model="ages" class="r-input margin-bottom_40rpx" placeholder="例如：25-35岁年轻白领"
						placeholder-class="placeholder" />
				</block>
			</block>

			<block v-if="(type > 3 && type < 10) || type == 18">
				<block v-if="type == 4 || type == 5 || type == 7">
					<view class="display-a margin-bottom_20rpx">
						<view class="r-line"></view>
						<view class="color_FFFFFF font-size_30rpx">
							<block v-if="type == 4">科普主题</block>
							<block v-if="type == 5">情感主题</block>
							<block v-if="type == 7">产品名称</block>
						</view>
						<view class="red-dot">*</view>
					</view>
					<input type="text" v-model="name" class="r-input margin-bottom_40rpx" placeholder="请输入"
						placeholder-class="placeholder" />
				</block>
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">
						<block v-if="type == 4">内容补充</block>
						<block v-if="type == 5">情感话题</block>
						<block v-if="type == 6 || type == 8">分享主题</block>
						<block v-if="type == 7">其它说明</block>
						<block v-if="type == 9">代写主题</block>
						<block v-if="type == 18">主题内容</block>
					</view>
					<view class="red-dot">*</view>
				</view>
				<textarea class="margin-bottom_40rpx" v-model="desc" placeholder="请输入"
					placeholder-class="placeholder"></textarea>
			</block>

			<block v-if="type == 8">
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">风格</view>
					<view class="red-dot">*</view>
				</view>
				<view class="display-a margin-bottom_40rpx">
					<block v-for="(item,index) in styleArr" :key="index">
						<view @click="getStyle(item)" class="font-size-1"
							:class="item.name == styles ? 'font-size-3' : 'font-size-2'">{{item.name}}</view>
					</block>
				</view>
			</block>

			<block v-if="type == 10">
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">改写内容</view>
					<view class="red-dot">*</view>
				</view>
				<textarea v-if="typeIndex == 2" class="margin-bottom_40rpx" maxlength="250" v-model="desc"
					placeholder="请输入一段自定义得需求文字AI智能帮你生成 " placeholder-class="placeholder"></textarea>
				<textarea v-else class="margin-bottom_40rpx" maxlength="300" v-model="desc"
					placeholder="请输入一段自定义得需求文字AI智能帮你生成 " placeholder-class="placeholder"></textarea>
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">改写要求</view>
					<view class="red-dot">*</view>
				</view>
				<view class="display-a margin-bottom_40rpx">
					<block v-for="(item,index) in requireArr" :key="index">
						<view @click="getRequire(item)" class="font-size-1"
							:class="item.name == require ? 'font-size-3' : 'font-size-2'">{{item.name}}</view>
					</block>
				</view>
			</block>

			<view v-if="type < 10 || type == 18">
				<view class="display-a margin-bottom_20rpx">
					<view class="r-line"></view>
					<view class="color_FFFFFF font-size_30rpx">文案字数</view>
					<view class="red-dot">*</view>
				</view>
				<view class="display-a">
					<view @click="getWords(1)" class="font-size-1" :class="words == 1 ? 'font-size-3' : 'font-size-2'">
						短(150字)</view>
					<view @click="getWords(2)" class="font-size-1" :class="words == 2 ? 'font-size-3' : 'font-size-2'">
						中(250字)</view>
					<view v-if="typeIndex  != 2" @click="getWords(3)" class="font-size-1"
						:class="words == 3 ? 'font-size-3' : 'font-size-2'">长(500字)</view>
				</view>
			</view>

			<view style="height: 160rpx;"></view>

			<view class="r-but" @click="getAccountInfo()">
				点击立即生成
				<!-- <span class="margin-left_16rpx">
					{{tallySetObj.ai_create_deduct}}点/1次
				</span> -->
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 年龄阶段
				ages: '',
				isWhether: true, //判断重复点击

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				typeIndex: '', //1导航进入 2标准文本 3高保真
				type: '', //1-生产工厂 2-团购商家 3-零售商家 4-智能代写 5-文案仿写  
				types: '', //

				name: '', //名称
				store_introduction: '', //商家简介
				product: '', //特色
				price: '', //价格
				originalPrice: '', //原价
				desc: '', //介绍
				words: 1, //文案字数 1 150 2 250 3 500
				styles: "幽默", //风格
				styleArr: [{
						id: 1,
						name: '幽默'
					},
					{
						id: 2,
						name: '热情'
					},
					{
						id: 3,
						name: '温柔'
					},
					{
						id: 4,
						name: '理性'
					}
				],

				require: '润色', //改写要求
				requireArr: [{
						id: 1,
						name: '润色'
					},
					{
						id: 2,
						name: '扩写'
					},
					{
						id: 3,
						name: '缩写'
					},
					{
						id: 4,
						name: '仿写'
					}
				],
				channel: '抖音',
				channelList: [{
						id: 1,
						name: '抖音'
					},
					{
						id: 2,
						name: '小红书'
					},
					{
						id: 3,
						name: '视频号'
					},
					{
						id: 4,
						name: '快手'
					}
				],

				back: false, // 是否返回
			}
		},

		onLoad(options) {
			if (options.back) {
				this.back = true
			}
			if (options.typeIndex) {
				this.typeIndex = options.typeIndex;
			}
			if (options.type) {
				this.type = options.type;
				if (this.type == 1) {
					this.$sun.title("企业宣传");
					this.types = 6;
				}
				if (this.type == 2) {
					this.$sun.title("同城团购");
					this.types = 7;
				}
				if (this.type == 3) {
					this.$sun.title("电商带货");
					this.types = 8;
				}
				if (this.type == 4) {
					this.$sun.title("知识科普");
					this.types = 9;
				}
				if (this.type == 5) {
					this.$sun.title("情感专家");
					this.types = 10;
				}
				if (this.type == 6) {
					this.$sun.title("口播文案");
					this.types = 11;
				}
				if (this.type == 7) {
					this.$sun.title("朋友圈营销");
					this.types = 12;
				}
				if (this.type == 8) {
					this.$sun.title("小红书笔记");
					this.types = 13;
				}
				if (this.type == 9) {
					this.$sun.title("智能代写");
					this.types = 15;
				}
				if (this.type == 10) {
					this.$sun.title("文案仿写");
					this.types = 14;
				}
				if (this.type == 18) {
					this.$sun.title("DeepSeek文案");
					this.types = 18;
				}
			}

		},

		onShow() {

		},

		methods: {


			//改写要求
			getRequire(obj) {
				this.require = obj.name;
			},

			//渠道
			getChannel(obj) {
				this.channel = obj.name;
			},

			//风格
			getStyle(obj) {
				this.styles = obj.name;
			},
			//字数
			getWords(num) {
				this.words = num;
			},

			//查询点数是否足够
			async getAccountInfo() {

				let content = '';

				if (this.type == 1) {
					if (!this.name) {
						this.$sun.toast("请输入公司名称", 'none');
						return;
					}
					if (!this.store_introduction) {
						this.$sun.toast("请输入公司简介", 'none');
						return;
					}
					if (!this.product) {
						this.$sun.toast("请输入公司特色，优势", 'none');
						return;
					}
					if (!this.desc) {
						this.$sun.toast("请输入公司主营业务", 'none');
						return;
					}

					content = '公司名称:' + this.name + ',' + '公司简介:' + this.store_introduction + ',' + '公司特色:' + this
						.product + ',' + '主营业务:' + this.desc

				}

				if (this.type == 2) {
					if (!this.name) {
						this.$sun.toast("请输入店铺名称", 'none');
						return;
					}
					if (!this.product) {
						this.$sun.toast("请输入团购套餐", 'none');
						return;
					}
					if (!this.originalPrice) {
						this.$sun.toast("请输入套餐原价", 'none');
						return;
					}
					if (!this.price) {
						this.$sun.toast("请输入团购价格", 'none');
						return;
					}
					if (!this.ages) {
						this.$sun.toast("请输入目标年龄阶段", 'none');
						return;
					}
					content = '店铺名称:' + this.name + ',' + '团购套餐:' + this.product + ',' + '套餐原价:' + this.originalPrice +
						'元' + ',' + '团购价格:' + this.price + '元' + ',' + '商业定位信息:' + this.desc + ',' + '发布渠道:' + this
						.channel + ',' + '目标年龄阶段:' + this.ages
				}

				if (this.type == 3) {
					if (!this.name) {
						this.$sun.toast("请输入商品名称", 'none');
						return;
					}
					if (!this.product) {
						this.$sun.toast("请输入商品特色", 'none');
						return;
					}
					if (!this.price) {
						this.$sun.toast("请输入优惠价格", 'none');
						return;
					}
					content = '商品名称:' + this.name + ',' + '商品特色:' + this.product + ',' + '优惠价格:' + this.price + '元'
				}


				if (this.type == 4) {
					if (!this.name) {
						this.$sun.toast("请输入科普主题", 'none');
						return;
					}
					if (!this.desc) {
						this.$sun.toast("请输入内容补充", 'none');
						return;
					}
					content = '科普主题:' + this.name + ',' + '内容补充:' + this.desc
				}

				if (this.type == 5) {
					if (!this.name) {
						this.$sun.toast("请输入情感主题", 'none');
						return;
					}
					if (!this.desc) {
						this.$sun.toast("请输入情感话题", 'none');
						return;
					}
					content = '情感主题:' + this.name + ',' + '情感话题:' + this.desc
				}

				if (this.type == 6) {
					if (!this.name) {
						this.$sun.toast("请输入文案标题", 'none');
						return;
					}
					if (!this.desc) {
						this.$sun.toast("请输入分享主题", 'none');
						return;
					}
					content = '分享主题:' + this.desc
				}

				if (this.type == 7) {
					if (!this.name) {
						this.$sun.toast("请输入产品名称", 'none');
						return;
					}
					if (!this.desc) {
						this.$sun.toast("请输入其他说明", 'none');
						return;
					}
					content = '产品名称:' + this.name + ',' + '其他说明:' + this.desc
				}

				if (this.type == 8) {
					if (!this.name) {
						this.$sun.toast("请输入文案标题", 'none');
						return;
					}
					if (!this.desc) {
						this.$sun.toast("请输入分享主题", 'none');
						return;
					}
					content = '分享主题:' + this.desc + ',' + '风格:' + this.styles
				}

				if (this.type == 9) {
					if (!this.name) {
						this.$sun.toast("请输入产品名称", 'none');
						return;
					}
					if (!this.desc) {
						this.$sun.toast("请输入代写主题", 'none');
						return;
					}
					content = '代写主题:' + this.desc
				}

				if (this.type == 10) {
					if (!this.name) {
						this.$sun.toast("请输入文案标题", 'none');
						return;
					}
					if (!this.desc) {
						this.$sun.toast("请输入改写内容", 'none');
						return;
					}
					content = '改写内容:' + this.desc + ',' + '改写要求:' + this.require
				}

				if (this.type == 18) {

					if (!this.desc) {
						this.$sun.toast("请输入主题内容", 'none');
						return;
					}
					content = '主题内容:' + this.desc
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				const result = await this.$http.post({
					url: this.$api.accountInfo,
					data: {
						type: 1,
						uid: uni.getStorageSync('uid'),
					}
				});
				if (result.errno == 0) {
					// if ([2, 6, 7, 8].includes(Number(this.type))) {
					// 	// 1 150 2 250 3 500
					// 	let num = 150
					// 	if (this.words === 1) {
					// 		num = 150
					// 	} else if (this.words === 2) {
					// 		num = 250
					// 	} else if (this.words === 3) {
					// 		num = 500
					// 	}
					// 	content += `,字数:${num}`
					// 	uni.setStorageSync('stream-content', content)
					// 	uni.navigateTo({
					// 		url: `/pages/index/AICreation/stream?type=${this.type}&typeIndex=${this.typeIndex}`
					// 	})
					// } else {
					this.getCopyImitation(content);
					// }
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//一键仿写
			async getCopyImitation(content) {
				const result = await this.$http.post({
					url: 'https://vapi.weijuyunke.com/api/doBao/copyImitation',
					data: {
						content: content,
						countType: this.type == 10 ? 2 : this.words,
						ipStatus: 2,
						domainName: 'vapi.weijuyunke.com',
						requestIp: '**************'
					},
					mask: true,
					title: '请求中...'
				});
				if (result.code == 200) {
					// this.$sun.toast("操作成功");
					// setTimeout(() => {
					// 	this.msgText = result.data;
					// 	this.isWhether = true;
					// }, 1000);
					this.getAICreation(content, result.data);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//生成AI文案
			async getAICreation(content, answer) {

				const result = await this.$http.post({
					url: this.$api.aiCreateAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: this.name,
						type: this.types,
						question: content, //拼接的文本
						answer: answer, // 接口返回的文本
						words: this.words
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						if (this.typeIndex == 1) {
							if (this.back) {
								uni.navigateBack()
							} else {
								uni.redirectTo({
									url: '/pages/index/AICreation/record?type=1'
								})
							}

						} else {
							this.returnNav(answer);
						}

						// uni.navigateTo({
						// 	url: '/pages/index/changeFace/record'
						// })
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//返回上一页
			returnNav(text) {
				var pages = getCurrentPages();
				var prevPage = pages[pages.length - 2]; //上一个页面
				prevPage.$vm.otherFun2(text); //重点$vm
				uni.navigateBack();
			},

		}
	}
</script>

<style lang="scss">
	.font-size-3 {
		background-color: #0084FF;
	}

	.font-size-2 {
		background-color: #343434;
	}

	.font-size-1 {
		width: 184rpx;
		text-align: center;
		border-radius: 10rpx;
		margin-right: 30rpx;
		color: #FFF;
		padding: 20rpx 0;
	}

	.placeholder {
		color: #999999;
	}

	.r-but {
		position: fixed;
		bottom: 50rpx;
		z-index: 9;
		font-size: 32rpx;
		color: #FFF;
		padding: 30rpx 0;
		width: 710rpx;
		text-align: center;
		border-radius: 10rpx;
		box-shadow: 4rpx 6rpx 28rpx 0 rgba(30, 156, 214, 0.81), inset 0 0 22rpx 0 rgba(204, 235, 255, 0.3);
		background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
	}

	textarea {
		width: 670rpx;
		height: 300rpx;
		padding: 20rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.r-input {
		width: 670rpx;
		padding: 20rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.r-top {
		padding: 30rpx 20rpx;
	}

	.red-dot {
		color: #FF0000;
		margin-left: 8rpx;
	}

	.r-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245), rgb(66, 72, 244) 100%);
		margin-right: 14rpx;
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
</style>