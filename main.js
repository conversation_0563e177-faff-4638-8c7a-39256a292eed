import App from './App'

import sunui from './utils/sun.js';
import sunUiPopup from './components/sunui-popup/sunui-popup.vue';
import MescrollEmpty from './components/mescroll-uni/components/mescroll-empty.vue';
import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue';
import MescrollUni from '@/components/mescroll-uni/mescroll-uni.vue';
import mixins from '@/components/mescroll-uni/mescroll-mixins.js';
import Api from './api.js';
import Http from './utils/http.js';
import date from './utils/date.js';
import zywPosterSmall from '@/components/zywPosterSmall/zywPosterSmall.vue';
import sunuiUpimg1 from '@/components/sunui-upimg1/sunui-upimg1.vue';
import sunuiUpimg2 from '@/components/sunui-upimg1/sunui-upimg2.vue';
import sunuiUpimg3 from '@/components/sunui-upimg1/sunui-upimg3.vue';
import sunuiUpvideo1 from '@/components/sunui-upvideo1/sunui-upvideo1.vue';
import sunuiTabbar2 from '@/components/sunui-tabbar/sunui-tabbar2.vue';
import privacy from '@/components/xh-privacy/xh-privacy.vue';
import pickerColor from '@/components/helang-pickerColor/helang-pickerColor.vue';
import soundRecording from '@/components/sound-recording/sound-recording.vue';
import soundRecording2 from '@/components/sound-recording/sound-recording2.vue';
import colorPicker from '@/components/t-color-picker/t-color-picker.vue';
import xuanLoading from '@/components/xuan-loading/xuan-loading.vue';
import termPicker from '@/components/term-picker/term-picker.vue';
import lbPicker from '@/components/lb-picker/index.vue';

// import helangCardSwiper from '@/components/helang-cardSwiper/helang-cardSwiper.vue';

Vue.prototype.$http = Http;
Vue.prototype.$api = Api;
Vue.prototype.$sun = sunui;
Vue.prototype.$date = date;

Vue.mixin(mixins);
Vue.component('poster', zywPosterSmall);
Vue.component('sunui-upimg1', sunuiUpimg1);
Vue.component('sunui-upimg2', sunuiUpimg2);
Vue.component('sunui-upimg3', sunuiUpimg3);
Vue.component('sunui-upvideo1', sunuiUpvideo1);
Vue.component('sunui-tabbar2', sunuiTabbar2);
Vue.component('mescroll-empty', MescrollEmpty);
Vue.component('mescroll-body', MescrollBody);
Vue.component('mescroll-uni', MescrollUni);
Vue.component('sunui-popup', sunUiPopup);
Vue.component('xh-privacy', privacy);
Vue.component('pick-color',pickerColor);
Vue.component('sound-recording',soundRecording);
Vue.component('sound-recording2',soundRecording2);
Vue.component('color-picker',colorPicker);
Vue.component('xuan-loading',xuanLoading);
Vue.component('term-picker',termPicker);
Vue.component('lb-picker',lbPicker);

// Vue.component('helang-cardSwiper',helangCardSwiper);



let domain = require('siteinfo.js').siteroot.replace(/^https?:\/\/(.*?)(:\d+)?\/.*$/, '$1');
Vue.prototype.$imgUrl = `https://${domain}/index/`;

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif