<template>
	<view>
		
		<block v-if="user.id">
			<view class="bg" :style="{'background-image': 'url('+imgUrl+'330.png'+')'}">
				<view :style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
					class="iconDizhssi">
					<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
					<view class="font-size_32rpx" @click="navig()">
						分销中心
					</view>
				</view>
				
				<view style="height: 180rpx;"></view>
				
				<view class="display-a padding_0_30rpx">
					<image class="avatar" :src="user.is_member == 1 ? imgUrl + '160.png' : imgUrl + '159.png'"></image>
					<view style="width: 450rpx;">
						<view class="display-a margin-bottom_20rpx">
							<view style="max-width: 370rpx;" class="color_FFFFFF font-size_36rpx">{{user.nickname}}</view>
							<view class="level-name" v-if="user.level_name">{{user.level_name}}</view>
						</view>
						<view class="color_FFFFFF">上级: {{user.p_nickname}}</view>
					</view>
				</view>
				
			</view>
			
			<view class="display-a bg-top">
				<view class="width_236rpx-center">
					<view class="margin-bottom_10rpx">我的团队(人)</view>
					<view class="team-num">{{user.teamCount}}</view>
				</view>
				<view class="width_236rpx-center"> 
					<view class="margin-bottom_10rpx">本月新增(人)</view>
					<view class="team-num">{{user.monthCount}}</view>
				</view>
				<view class="width_236rpx-center">
					<view class="margin-bottom_10rpx">今日新增(人)</view>
					<view class="team-num">{{user.todayCount}}</view>
				</view>
			</view>
			
			<view class="d-data">
				<view class="display-a" style="padding: 10rpx 40rpx 30rpx;">
					<view>
						<view class="color_FFFFFF font-size_32rpx margin-bottom_20rpx">我的收益</view>
						<view class="color_FF0000 font-size_44rpx font-weight_bold" >{{user.brokerage}}</view>
					</view>
					<view v-if="brokerageSet.cash_open == 1" @click="getWithdrawal()" class="withdrawal">提现</view>
				</view>
				<view class="display-a color_FFFFFF">
					<view class="record">
						<view class="color_999999 margin-bottom_10rpx">共计获得</view>
						<view class="font-weight_bold font-size_36rpx">{{Number(user.allGet).toFixed(2)}}</view>
					</view>
					<view class="record">
						<view class="color_999999 margin-bottom_10rpx">本月获得</view>
						<view class="font-weight_bold font-size_36rpx">{{Number(user.monthGet).toFixed(2)}}</view>
					</view>
					<view class="record">
						<view class="color_999999 margin-bottom_10rpx">今日获得</view>
						<view class="font-weight_bold font-size_36rpx">{{Number(user.todayGet).toFixed(2)}}</view>
					</view>
				</view>
			</view>
			
			<view v-if="brokerageSet.self_purchase_swich == 1" class="member-bg" :style="{'background-image': 'url('+imgUrl+'332.png'+')'}">
				<view class="display-a" style="padding: 76rpx 20rpx 0;">
					<view class="member-tips">开通赚钱,快来开通吧!</view>
					<view class="member-money">￥{{brokerageSet.member_money}}</view>
					<view class="member-but" @click="getMember()">立即开通</view>
				</view>
			</view>
			
			<view class="list-public">
				<view class="display-a margin-bottom_40rpx">
					<view class="dis-line"></view>
					<view class="font-size_32rpx font-weight_bold">常用功能</view>
				</view>
				<view class="display-fw-a">
					<view class="width_176rpx-center margin-bottom_30rpx" @click="team()">
						<image class="img-44" :src="imgUrl+'44.png'"></image>
						<view>我的团队</view>
					</view>
					<view class="width_176rpx-center margin-bottom_30rpx" v-if="brokerageSet.cash_open == 1" @click="getWithdrawalRecord(1)">
						<image class="img-44" :src="imgUrl+'49.png'"></image>
						<view>提现记录</view>
					</view>
					<view class="width_176rpx-center margin-bottom_30rpx" @click="getWithdrawalRecord(2)">
						<image class="img-44" :src="imgUrl+'45.png'"></image>
						<view>收益列表</view>
					</view>
					<view class="width_176rpx-center margin-bottom_30rpx" @click="qrCode()">
						<image class="img-44" :src="imgUrl+'47.png'"></image>
						<view>推广海报</view>
					</view>
					<view class="width_176rpx-center margin-bottom_30rpx">
						<button type="button" open-type="share">
							<image class="img-44" :src="imgUrl+'48.png'"></image>
							<view style="height: 38rpx;line-height: 38rpx;">转发给好友</view>
						</button>
					</view>
				</view>
			</view>
			
			<block v-if="brokerageSet.explain">
				<view class="h_10rpx"></view>
				<view class="display-a-jc margin-bottom_20rpx">
					<image class="img-331" mode="widthFix" :src="imgUrl+'331.png'"></image>
				</view>
				<view class="padding_0_20rpx color_FFFFFF">
					<rich-parser :html="brokerageSet.explain" domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load
						ref="article" selectable show-with-animation use-anchor>
						<!-- 加载中... -->
					</rich-parser>
				</view>
				<view class="h_30rpx"></view>
			</block>
			<!-- <view class="list-public">
				<view class="display-a padding_30rpx_0 p-bo" @click="team()">
					<image class="img-44" :src="imgUrl+'44.png'"></image>
					<view>我的团队</view>
					<image class="img-21" :src="imgUrl+'21.png'"></image>
				</view>
				<view class="display-a padding_30rpx_0 p-bo" v-if="brokerageSet.cash_open == 1" @click="getWithdrawalRecord(1)">
					<image class="img-44" :src="imgUrl+'49.png'"></image>
					<view>提现记录</view>
					<image class="img-21" :src="imgUrl+'21.png'"></image>
				</view>
				<view class="display-a padding_30rpx_0 p-bo" @click="getWithdrawalRecord(2)">
					<image class="img-44" :src="imgUrl+'45.png'"></image>
					<view>收益列表</view>
					<image class="img-21" :src="imgUrl+'21.png'"></image>
				</view>
				<view class="display-a padding_30rpx_0 p-bo" @click="that()">
					<image class="img-44" :src="imgUrl+'46.png'"></image>
					<view>分销说明</view>
					<image class="img-21" :src="imgUrl+'21.png'"></image>
				</view>
				<view class="display-a padding_30rpx_0 p-bo" @click="qrCode()">
					<image class="img-44" :src="imgUrl+'47.png'"></image>
					<view>推广海报</view>
					<image class="img-21" :src="imgUrl+'21.png'"></image>
				</view>
				<view class="padding_30rpx_0">
					<button type="button" class="display-a" open-type="share">
						<image class="img-44" :src="imgUrl+'48.png'"></image>
						<view>转发给好友</view>
						<image class="img-21" :src="imgUrl+'21.png'"></image>
					</button>
				</view>
			</view> -->
			
			<view class="h_20rpx"></view>
			
		</block>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				brokerageSet: {},
				
				user: {},
				
				heightSystemss: '',
				statusBarHeightss: '',
				
				imgUrl: this.$imgUrl,
				
				templateObj: {},
			}
		},
		
		onLoad() {
			this.getSystemInfo();
			this.getTemplate();
		},
		
		onShow() {
			
			this.getBrokerageSet();
			
		},
		
		onShareAppMessage() {
			return {
				title: uni.getStorageSync('system').share_title,
				path: '/pages/index/index?scene='+uni.getStorageSync('uid'),
				imageUrl: uni.getStorageSync('system').share_pic,
				desc: uni.getStorageSync('system').share_desc,
			}
		},
		// 分享朋友圈(微信小程序)
		onShareTimeline() {
			return {
				title: uni.getStorageSync('system').share_title,
				query: 'scene='+uni.getStorageSync('uid'),
				imageUrl: uni.getStorageSync('system').share_pic,
			}
		},
		
		methods: {
			
			//
			getMember() {
				uni.navigateTo({
					url: '/pages/my/member?mId='+this.brokerageSet.self_purchase_member_id+'&mName='+this.brokerageSet.self_purchase_member_name+'&mMoney='+this.brokerageSet.member_money
				})
			},
			
			//模板设置
			async getTemplate() {
				const result = await this.$http.post({
					url: this.$api.template,
				});
				if (result.errno == 0) {
					this.templateObj = result.data;
				}
			},
			
			//提现记录
			getWithdrawalRecord(type) {
				uni.navigateTo({
					url: '/pages/my/distribution/withdrawalRecord?index='+type
				})
			},
			
			//提现
			getWithdrawal() {
				
				if (Number(this.user.brokerage) <= 0) {
					this.$sun.toast("暂无提现佣金",'none');
					return;
				}
				
				uni.navigateTo({
					url: '/pages/my/distribution/withdrawal'
				})
			},
			
			//我的团队
			team() {
				uni.getSetting({
					withSubscriptions: true,
					success: (res) => {
						console.log(res.subscriptionsSetting);
						if (res.subscriptionsSetting.mainSwitch == false) {
							uni.navigateTo({
								url: '/pages/my/distribution/myTeam?is_level='+this.brokerageSet.is_level
							})
						} else {
							// 获取下发权限
							uni.requestSubscribeMessage({
								tmplIds: [this.templateObj.brokerage_arrival_template], //此处写在后台获取的模板ID，可以写多个模板ID，看自己的需求
								success: (data) => {
									if (data[this.templateObj.brokerage_arrival_template] == 'accept') { //accept--用户同意 reject--用户拒绝 ban--微信后台封禁,可不管
										uni.navigateTo({
											url: '/pages/my/distribution/myTeam?is_level='+this.brokerageSet.is_level
										})
									} else {
										uni.showModal({
											title: '温馨提示',
											content: '您已拒绝授权，将无法在微信中收到通知！',
											showCancel: false,
											success: res => {
												if (res.confirm) {
													// 这里可以写自己的逻辑
													uni.navigateTo({
														url: '/pages/my/distribution/myTeam?is_level='+this.brokerageSet.is_level
													})
													console.log('拒绝授权', data);
												}
											}
										})
									}
								},
								fail: (err) => {
									uni.navigateTo({
										url: '/pages/my/distribution/myTeam?is_level='+this.brokerageSet.is_level
									})
									console.log('失败', err);
								},
								complete: (result) => {
				
									console.log('完成', result);
								}
							});
						}
					}
				});
			},
			
			//分销海报
			qrCode() {
				uni.navigateTo({
					url: '/pages/my/distribution/qrCodeDistribution?poster='+this.brokerageSet.poster + '&qrCode='+this.user.qrcode
				})
			},
			
			//分销说明
			that() {
				uni.navigateTo({
					url: '/pages/my/distribution/moneyThat'
				})
			},
			
			//分销设置
			async getBrokerageSet() {
				const result = await this.$http.post({
					url: this.$api.brokerageSet,
				});
				if (result.errno == 0) {
					this.brokerageSet = result.data;
					if (this.brokerageSet.is_open == 1) {
						if (uni.getStorageSync('uid')) {
							this.getUser();
						}else {
							uni.showModal({
								content:"请先登录",
								cancelText: "返回",
								confirmText: "去登录",
								success: (res) => {
									if (res.confirm) {
										uni.redirectTo({
											url: '/pages/auth/auth?type=1'
										})
										// uni.navigateTo({
										// 	url: '/pages/auth/auth?type=1'
										// })
									} else if (res.cancel) {
										this.navig();
									}
								}
							})
						}
					}else {
						uni.showModal({
							content:"暂未开启分销",
							confirmText: "确认",
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									uni.redirectTo({
										url: '/pages/index/index'
									})
								} else if (res.cancel) {
									
								}
							}
						})
					}
				}
			},
			
			navig() {
				let pages = getCurrentPages();  //获取所有页面栈实例列表
				
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}else {
					uni.navigateBack();
				}
			},
			
			//分销中心
			async getUser() {
				const result = await this.$http.post({
					url: this.$api.brokerageIndex,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.user = result.data;
				}
			},
			
			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},
			
		}
	}
</script>

<style lang="scss">
	
	.member-but {
		width: 160rpx;
		text-align: center;
		padding: 20rpx 0;
		color: #FFF;
		border-radius: 100rpx;
		margin-left: 20rpx;
		background-color: #FF0000;
	}
	
	.member-money {
		font-size: 34rpx;
		font-weight: 600;
		color: #FF0000;
		margin-left: auto;
	}
	
	.member-tips {
		font-size: 32rpx;
		font-weight: 600;
		color: #000;
	}
	
	.member-bg {
		width: 710rpx;
		height: 176rpx;
		background-repeat: no-repeat;
		background-size: contain;
		margin: 0 20rpx 26rpx;
	}
	
	.img-331 {
		width: 240rpx;
	}
	
	.dis-line {
		width: 8rpx;
		height: 22rpx;
		border-radius: 10rpx;
		background: linear-gradient(180.00deg, rgb(240, 70, 112),rgb(240, 64, 58) 100%);
		margin: 0 10rpx 0 20rpx;
	}
	
	.d-data {
		width: 710rpx;
		border-radius: 20rpx;
		background: rgb(28, 26, 26);
		padding: 34rpx 0 28rpx;
		margin: -30rpx 20rpx 26rpx;
	}
	
	.level-name {
		background-color: #FFDC00F5;
		padding: 6rpx 10rpx;
		border-radius: 10rpx;
		font-size: 24rpx;
		margin-left: 16rpx;
	}
	
	.banner {
		width: 710rpx;
		margin: 0 20rpx 20rpx;
		border-radius: 10rpx;
	}
	
	.p-bo {
		border-bottom: 1px solid rgb(48, 48, 48);
	}
	
	.list-public {
		background-color: #1C1A1A;
		color: #FFF;
		padding: 30rpx 0 0;
	}
	
	.withdrawal {
		width: 160rpx;
		text-align: center;
		background: linear-gradient(101.31deg, rgb(240, 70, 116) 1.709%,rgb(240, 63, 53) 98.967%);
		border-radius: 100rpx;
		padding: 20rpx 0;
		color: #FFFFFF;
		font-size: 32rpx;
		margin-left: auto;
	}
	
	.record {
		width: 230rpx;
		text-align: center;
	}
	
	button {
		padding-left: 0;
		padding-right: 0;
		background-color: #1C1A1A;
		line-height: 1;
		font-size: 28rpx;
		font-weight: 500;
		color: #fff;
	}
	
	.img-21 {
		width: 28rpx;
		height: 28rpx;
		margin-left: auto;
	}
	
	.img-44 {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 4rpx;
	}
	
	.team-num {
		color: #753519;
		font-size: 32rpx;
		font-weight: 600;
	}
	
	.bg-top {
		width: 710rpx;
		border-radius: 10rpx;
		background: linear-gradient(180.00deg, rgb(255, 237, 210),rgb(245, 215, 171) 96.183%,rgba(28, 26, 26, 0) 100%);
		margin: -220rpx 20rpx 0;
		color: #B36D44;
		padding: 22rpx 0 44rpx;
	}
	
	.vip {
		width: 160rpx;
		background-color: #ECCE90;
		border-radius: 10rpx;
		margin-left: 20rpx;
	}
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}
	
	.bg {
		width: 750rpx;
		height:556rpx;
		background-repeat: no-repeat;
		background-size: cover;
	}
	
	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		// font-size: 30rpx;
		// font-weight: bold;
		display: flex;
		align-items: center;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
	
</style>

