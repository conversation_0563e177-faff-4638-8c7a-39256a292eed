<template>
	<view>
		
		<view style="height: 24rpx;"></view>
		
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'39.png'+')'}">
			<view class="margin-bottom_10rpx">当前点数</view>
			<view class="font-weight_bold font-size_50rpx">{{user.balance}}</view>
		</view>
		
		<view class="frame" style="padding: 50rpx 0 22rpx 30rpx;">
			<view class="font-size_32rpx margin-bottom_30rpx">充值金额</view>
			<view class="display-fw-a margin-bottom_30rpx">
				<block v-for="(item,index) in pointList" :key="index">
					<view class="r-list" :class="pointObj.id == item.id ? 'r-list-2' : 'r-list-1'" @click="selList(item)">
						<view class="font-weight_bold margin-bottom_10rpx"><span class="font-size_36rpx">{{item.point}}</span>点</view>
						<view>售价￥{{item.money}}</view>
					</view>
				</block>
			</view>
			<view class="but" @click="but()">立即充值</view>
		</view>
		
		<view class="frame" style="padding: 26rpx 20rpx;">
			<view class="font-size_32rpx margin-bottom_20rpx">充值须知</view>
			<rich-parser :html="pointSet.explain" domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load
				ref="article" selectable show-with-animation use-anchor>
				<!-- 加载中... -->
			</rich-parser>
		</view>
		
		<view style="height: 20rpx;"></view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				user: {},
				
				pointList: [],
				pointObj: {},
				
				pointSet: {},
				
				isWhether: true, //防止重复点击
				
				templateObj: {},
				
			}
		},
		
		onLoad() {
			this.getTemplate();
		},
		
		onShow() {
			this.getPointSet();
			this.getPointList();
			if (uni.getStorageSync('uid')) {
				this.userInfo();
			}else {
				uni.showModal({
					content:"请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},
		
		methods: {
			
			//模板设置
			async getTemplate() {
				const result = await this.$http.post({
					url: this.$api.template,
				});
				if (result.errno == 0) {
					this.templateObj = result.data;
				}
			},
			
			//购买会员
			async but() {
				
				if (!this.pointObj.id) {
					this.$sun.toast("请选择充值的点数!",'none');
					return;
				}
				
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				
				uni.getSetting({
					withSubscriptions: true,
					success: (res) => {
						console.log(res.subscriptionsSetting);
						if (res.subscriptionsSetting.mainSwitch == false) {
							this.save();
						} else {
							// 获取下发权限
							uni.requestSubscribeMessage({
								tmplIds: [this.templateObj.recharge_success_template], //此处写在后台获取的模板ID，可以写多个模板ID，看自己的需求
								success: async (data) => {
									if (data[this.templateObj.recharge_success_template] == 'accept') { //accept--用户同意 reject--用户拒绝 ban--微信后台封禁,可不管
										this.save();
									} else {
										uni.showModal({
											title: '温馨提示',
											content: '您已拒绝授权，将无法在微信中收到通知！',
											showCancel: false,
											success: res => {
												if (res.confirm) {
													// 这里可以写自己的逻辑
													this.save();
													console.log('拒绝授权', data);
												}
											}
										})
									}
								},
								fail: (err) => {
									this.save();
									console.log('失败', err);
								},
								complete: (result) => {
				
									console.log('完成', result);
								}
							});
						}
					}
				});
				
			},
			
			async save() {
				const result = await this.$http.post({
					url: this.$api.addPointLog,
					data: {
						uid: uni.getStorageSync('uid'),
						point_id: this.pointObj.id
					}
				});
				if (result.errno == 0) {
					this.wxPay(result.data)
				} else {
					this.$sun.toast(result.message,'none');
					this.isWhether = true;
				}
			},
			
			/*  微信支付  */
			async wxPay(log_no) {
				const result = await this.$http.post({
					url: this.$api.pay,
					data: {
						openid: uni.getStorageSync('openid'),
						price: this.pointObj.money,
						log_no: log_no,
						name: this.pointObj.point
					}
				});
				if (result.errno == 0) {
					uni.requestPayment({
						provider: 'wxpay',
						timeStamp: result.data.timeStamp,
						nonceStr: result.data.nonceStr,
						package: result.data.package,
						signType: result.data.signType,
						paySign: result.data.paySign,
						success: async (res) => {
							this.$sun.toast("支付成功");
							setTimeout(() => {
								this.isWhether = true;
								uni.navigateBack();
							}, 2000);
						},
						fail: (err) => {
							this.isWhether = true;
							this.$sun.toast("取消支付",'error');
						}
					});
				}else {
					this.isWhether = true;
					if (result.errno == -1){
						this.$sun.toast(result.message,'none');
						return;
					}
					if (result.return_code == 'FAIL'){
						uni.showModal({
							title: '支付配置错误',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									
								}
							}
						})
					}else {
						uni.showModal({
							title: '提示',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									
								}
							}
						})
					}
				}
			},
			
			selList(obj) {
				this.pointObj = obj;
			},
			
			//会员列表
			async getPointList() {
				const result = await this.$http.post({
					url: this.$api.pointList
				});
				if (result.errno == 0) {
					this.pointList = result.data;
				}
			},
			
			//会员设置
			async getPointSet() {
				const result = await this.$http.post({
					url: this.$api.pointSet
				});
				if (result.errno == 0) {
					this.pointSet = result.data;
				}
			},
			
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表
			
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			
			//用户信息
			async userInfo() {
				const result = await this.$http.post({
					url: this.$api.userInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.user = result.data;
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.but {
		width: 650rpx;
		border-radius: 10rpx;
		background: linear-gradient(262.74deg, rgb(30, 108, 235) 1.017%,rgb(106, 104, 247) 103.119%);
		padding: 24rpx 0;
		color: #FFF;
		font-size: 32rpx;
		text-align: center;
	}
	
	.r-list-2 {
		background-color: #FFFFFF;
		color: #6B68F7;
		border: 1px solid #6B68F7;
	}
	
	.r-list-1 {
		background-color: #F7F7F7;
		color: #757575;
	}
	
	.r-list {
		width: 202rpx;
		text-align: center;
		padding: 26rpx 0 30rpx;
		border-radius: 10rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.frame {
		width: 710rpx;
		background-color: #FFF;
		margin: 0 20rpx 20rpx;
		border-radius: 14rpx;
	}
	
	.bg {
		width: 710rpx;
		height: 240rpx;
		background-repeat: no-repeat;
		background-size: contain;
		margin: 0 20rpx 26rpx;
		padding: 60rpx 50rpx 0;
		color: #FFFFFF;
	}
	
	page {
		background-color: #F7F7F7;
	}
	
</style>
