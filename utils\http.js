var Http = {
	/**
	 * [HTTP GET 请求]
	 * @param [第1种使用方法是URL不带参数。第2种使用方法是在请求URL后带参数，如：?id=1&name=ming]
	 * 1. HTTP.get(url).then((data) => {}).catch((error) => {})
	 * 2. HTTP.get({url: url, data: [JSON Object] }).then((data) => {}).catch((error) => {})
	 * 
	 * [HTTP GET/POST请求实例]
	 * @param
	 * 1. const http1 = await this.$http.get(`https://b2.admin168.net/app/index.php?i=5&c=entry&a=wxapp&m=wjyk_clbg&do=qiniuYun`);
	 * 2. const http2 = await this.$http.get({url: 'https://b18.admin168.net/app/index.php?i=4&c=entry&a=wxapp&do=userCarList&m=wjyk_jscf',data: {uid: 2}});
	 */
	get: function(requestHandler) {
		if (typeof requestHandler === 'string') {
			requestHandler = {
				url: String(requestHandler),
				data: {}
			}
		}
		return this.Request('GET', requestHandler)
	},

	/**
	 * [HTTP POST 请求]
	 * @param [可自定义 headers，如需 Authorization 等，默认：'Content-Type': 'application/json']
	 * HTTP.post({url: url, data: [JSON Object], headers: [JSON Object] }).then((data) => {}).catch((error) => {})
	 */
	post: function(requestHandler) {
		return this.Request('POST', requestHandler)
	},

	/**
	 * [HTTP PATCH 请求]
	 * HTTP.patch({url: url, data: [JSON Object], headers: [JSON Object] }).then((data) => {}).catch((error) => {})
	 */
	patch: function(requestHandler) {
		return this.Request('PATCH', requestHandler)
	},

	/**
	 * [HTTP PUT 请求]
	 * HTTP.put({url: url, data: [JSON Object], headers: [JSON Object] }).then((data) => {}).catch((error) => {})
	 */
	put: function(requestHandler) {
		return this.Request('PUT', requestHandler)
	},

	/**
	 * [HTTP DELETE 请求]
	 * HTTP.delete({url: url, data: [JSON Object], headers: [JSON Object] }).then((data) => {}).catch((error) => {})
	 */
	delete: function(requestHandler) {
		return this.Request('DELETE', requestHandler)
	},

	/**
	 * 封装的流式请求函数
	 * @param {String} url - 请求路径 
	 * @param {String} method - 请求方法 GET、POST等
	 * @param {Object} data - 请求数据
	 * @param {Function} onChunk - 每次接收到数据块时的回调函数
	 * @param {Function} onComplete - 数据接收完成时的回调函数
	 * @param {Function} onError - 发生错误时的回调函数
	 * @returns {RequestTask} 请求任务对象，可用于中断请求
	 */
	requestStream: function(url, method = 'GET', data = {}, {
		onChunk = () => {},
		onComplete = () => {},
		onError = () => {},
		options = {}
	} = {}) {
		// 创建请求任务
		const requestTask = uni.request({
			url,
			method,
			data,
			header: {
				'content-type': 'application/json',
				'Accept': 'text/event-stream'
			},
			enableChunked: true, // 开启分块传输
			responseType: "arraybuffer",
			// enableHttp2: true, // 开启HTTP/2
			...options,
			success: (res) => {
				if (res.statusCode !== 200) {
					onError({
						type: 'request_error',
						message: '请求失败',
						details: res
					});
				}
			},
			fail: (err) => {
				onError({
					type: 'network_error',
					message: '网络错误',
					details: err
				});
			},
			complete: () => {
				// 请求完成时，移除监听函数，避免内存泄漏
				if (requestTask && typeof requestTask.offChunkReceived === 'function') {
					requestTask.offChunkReceived(chunkHandler);
				}
				onComplete();
			}
		});

		// 定义数据块处理函数
		const chunkHandler = (response) => {
			try {
				onChunk(response.data);
			} catch (error) {
				onError({
					type: 'chunk_process_error',
					message: '数据块处理错误',
					details: error
				});
			}
		};

		// 监听数据块接收事件
		if (requestTask && typeof requestTask.onChunkReceived === 'function') {
			requestTask.onChunkReceived(chunkHandler);
		}

		// 扩展requestTask对象，添加便捷方法
		requestTask.cancel = () => {
			// 取消请求前先移除监听
			if (typeof requestTask.offChunkReceived === 'function') {
				requestTask.offChunkReceived(chunkHandler);
			}
			requestTask.abort();
		};

		return requestTask;
	},
	// request
	Request: function(method, requestHandler) {
		const {
			url,
			data,
			headers,
			mask,
			title,
			loading
		} = requestHandler

		if (loading === undefined || loading) {
			wx.showLoading && wx.showLoading({
				title: title ? title : 'Loading...',
				mask: mask ? mask : false
			});
			mask ? wx.showNavigationBarLoading() : '';
		}

		return new Promise((resolve, reject) => {
			wx.request({
				url: url,
				data,
				method: ['GET', 'POST', 'PATCH', 'PUT', 'DELETE'].indexOf(method) > -1 ? method : 'GET',
				header: headers ? headers : {
					'Content-Type': 'application/json'
				},
				timeout: 600000,
				// header: headers ? headers : {
				// 	'Content-Type': 'application/json'
				// },
				// Object.assign({
				// 	'Content-Type': 'application/json'
				// }, headers),
				success: function(res) {
					const {
						data,
						statusCode
					} = res
					// 处理数据
					statusCode === 200 ? resolve(data) : reject(data, statusCode)
					// statusCode === 200 ? console.log(`mark is：%c ${mark?mark:'none'} %c`,
					// 	`font-weight:bold;color:#18A05E;font-size:2em;`, `color:#18A05E;`) : console.log('');
				},
				fail: function() {
					reject('Network request failed')
					// wx.showModal({
					// 	title: '提示',
					// 	content: `Network request failed`,
					// 	showCancel: false,
					// 	confirmText: '确认'
					// });
				},
				complete: function() {
					if (loading === undefined || loading) { 
						wx.hideLoading && wx.hideLoading()
						wx.hideNavigationBarLoading() && wx.hideNavigationBarLoading()
					}
				}
			})
		})
	}
}

const unparam = (str = '', unDecodeURI) => {
	let result = {},
		query = str.split('?')[1]

	if (!query) return result

	let arr = query.split('&')

	arr.forEach((item, idx) => {
		let param = item.split('='),
			name = param[0],
			value = param[1] || ''

		if (name) {
			result[name] = unDecodeURI ? value : decodeURIComponent(value)
		}
	})
	return result
}

module.exports = Http;
