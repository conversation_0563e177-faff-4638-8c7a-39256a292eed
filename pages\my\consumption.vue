<template>
	<view v-if="uid">
		<view class="display-a c-top">
			<view class="display-ac-jc" @click="getType(1)">
				<view :class="type == 1 ? 'color_4088FF' : 'color_9B9B9B'">收入明细</view>
				<view class="c-line" :class="type == 1 ? 'c-line-1' : 'c-line-2'"></view>
			</view>
			<view class="display-ac-jc" @click="getType(2)">
				<view :class="type == 2 ? 'color_4088FF' : 'color_9B9B9B'">支出明细</view>
				<view class="c-line" :class="type == 2 ? 'c-line-1' : 'c-line-2'"></view>
			</view>
		</view>
		<scroll-view :scroll-x="true" class="scr-view">
			<view class="display-a">
				<block v-for="(item,index) in arr" :key="index">
					<view @click="selTabs(item.id)" class="c-tabs" :class="item.id == arrId ? 'c-tabs-2' : 'c-tabs-1'">{{item.name}}</view>
				</block>
			</view>
		</scroll-view>
		
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public display-a">
					<image v-if="item.way == 0" class="img-74" :src="imgUrl+'74.png'"></image>
					<image v-if="item.way == 1" class="img-74" :src="imgUrl+'75.png'"></image>
					<image v-if="item.way == 2" class="img-74" :src="imgUrl+'76.png'"></image>
					<image v-if="item.way == 7" class="img-74" :src="imgUrl+'77.png'"></image>
					<image v-if="item.way == 6 || item.way == 10 || item.way == 13" class="img-74" :src="imgUrl+'78.png'"></image>
					<image v-if="item.way == 4" class="img-74" :src="imgUrl+'79.png'"></image>
					<image v-if="item.way == 3 || item.way == 15" class="img-74" :src="imgUrl+'80.png'"></image>
					<image v-if="item.way == 14 || item.way == 16" class="img-74" :src="imgUrl+'361.png'"></image>
					<!-- <image v-if="item.way == 5" class="img-74" :src="imgUrl+'81.png'"></image> -->
					<image v-if="item.way == 8" class="img-74" :src="imgUrl+'82.png'"></image>
					<!-- <image v-if="item.way == 9" class="img-74" :src="imgUrl+'168.png'"></image> -->
					<!-- <image v-if="item.way == 10" class="img-74" :src="imgUrl+'204.png'"></image> -->
					<image v-if="item.way == 11" class="img-74" :src="imgUrl+'307.png'"></image>
					<image v-if="item.way == 12" class="img-74" :src="imgUrl+'321.png'"></image>
					<image v-if="item.way == 17" class="img-74" :src="imgUrl+'401.png'"></image>
					<image v-if="item.way == 31 || item.way == 32" class="img-74" :src="imgUrl+'430.png'"></image>
					<image v-if="item.way == 22 || item.way == 24 || item.way == 26" class="img-74" :src="imgUrl+'402.png'"></image>
					<image v-if="item.way == 23 || item.way == 25 || item.way == 27" class="img-74" :src="imgUrl+'403.png'"></image>
					<image v-if="item.way == 18 || item.way == 20 || item.way == 21" class="img-74" :src="imgUrl+'404.png'"></image>
					<view>
						<view class="color_FFFFFF font-size_32rpx margin-bottom_10rpx">{{item.desc}}</view>
						<view class="color_757575 font-size_26rpx">{{item.create_time}}</view>
					</view>
					<view class="money" :class="item.type == 1 ? 'color_4095FF' : 'color_FF0000'">{{item.type == 1 ? '+' : '-'}}{{item.money}}<span class="company">{{item.b_type == 1 ? '点' : item.b_type == 3 ? '秒' : item.b_type == 4 ? '字' : '次'}}</span></view>
				</view>
			</block>
		</mescroll-body>
		
	</view>
</template>

<script>
	export default {
		
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				type: '1', //1收入 2支出
				
				way: '', //0-后台 1-充值 2-新人赠送 3-声音克隆 4-形象克隆 5-视频换脸 6-视频剪辑 7-会员 8-ai创作
				// way  0-后台  1-充值 2-新人赠送 3-声音克隆  4-形象克隆  5-视频换脸 6-视频剪辑  7-会员  8-ai创作  9-照片克隆 10极速
				arrIncome: [
					{id:'',name:'全部'},
					{id:'0',name:'后台'},
					{id:'1',name:'充值'},
					{id:'2',name:'新人赠送'},
					{id:'7',name:'开通会员'},
				],
				arrExpenditure: [
					{id:'',name:'全部'},
					{id:'0',name:'后台'},
					{id:'6',name:'我的作品'},//13 10
					// {id:'10',name:'标准作品'},
					{id:'4',name:'形象克隆'},
					// {id:'9',name:'照片克隆'},
					{id:'3',name:'声音克隆'},//15
					{id:'14',name:'音频合成'}, // 16
					// {id:'5',name:'ai演员'},
					{id:'8',name:'ai文案'},
					{id:'17',name:'ai标题'},
					// {id:'18',name:'获取二维码'},
					// {id:'19',name:'矩阵账户授权'},
					{id:'20',name:'矩阵任务'},
					{id:'11',name:'二次剪辑'},
					{id:'12',name:'视频提取文案'},
					{id:'31',name:'IP账号采集'},
					
				],
				arr: [],
				arrId: '',
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				uid: uni.getStorageSync('uid'),
				
				list: [],
				
				windowHeight: '',
				
			}
		},
		
		onLoad() {
			this.arr = this.arrIncome;
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 240;
				},
			})
		},
		
		onShow() {
			
			if (uni.getStorageSync('uid')) {
				this.uid = uni.getStorageSync('uid');
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}else {
				uni.showModal({
					content:"请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
							// uni.navigateTo({
							// 	url: '/pages/auth/auth?type=1'
							// })
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
			
		},
		
		methods: {
			
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表
			
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			
			selTabs(id) {
				this.arrId = id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			getType(type) {
				this.type = type;
				this.arrId = '';
				if (this.type == 1) {
					this.arr = this.arrIncome;
				}
				if (this.type == 2) {
					this.arr = this.arrExpenditure;
				}
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.balanceLog,
					data: {
						uid: uni.getStorageSync("uid"),
						type: this.type,
						way: this.arrId,
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.company {
		font-size: 28rpx;
		font-weight: 500;
		margin-left: 4rpx;
	}
	
	.color_4095FF {
		color: #4095FF;
	}
	
	.money {
		font-size: 36rpx;
		font-weight: 600;
		margin-left: auto;
	}
	
	.img-74 {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}
	
	.list-public {
		background-color: #1C1C1C;
		padding: 30rpx 20rpx;
	}
	
	.c-tabs-2 {
		color: #FFF;
		background-color: #1E6CEB;
	}
	
	.c-tabs-1 {
		color: #B2B1B1;
		background-color: #232323;
	}
	
	.c-tabs {
		// width: 160rpx;
		display: inline-block;
		padding: 8rpx 20rpx;
		margin-right: 22rpx;
		font-size: 26rpx;
		border-radius: 10rpx;
	}
	
	.scr-view {
		padding: 30rpx 20rpx;
		width: 710rpx;
		white-space: nowrap;
	}
	
	.c-top {
		padding: 30rpx 0;
		border-bottom: 1px solid rgb(18, 18, 18);;
	}
	
	.color_9B9B9B {
		color: #9B9B9B;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}
	
	.color_4088FF {
		color: #4088FF;
		font-weight: bold;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}
	
	.c-line-2 {
		background: #000;
	}
	
	.c-line-1 {
		background: rgb(30, 108, 235);
	}
	
	.c-line {
		width: 66rpx;
		height: 8rpx;
		border-radius: 100rpx;
	}
	
	.display-ac-jc {
		width: 375rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: 1px solid rgb(56, 56, 56);;
		background: #000000;
	}
	
</style>
