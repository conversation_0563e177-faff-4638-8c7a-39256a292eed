<template>
	<view>
		
		<view class="display-a acc-top">
			<block v-for="(item,index) in tabs" :key="index">
				<view class="tabs" @click="getLabel(item.id)">
					<view class="margin-bottom_20rpx" :style="tabId == item.id ? 'color:#69F8AA;' : ''">{{item.name}}</view>
					<view class="tabs-line" :style="tabId == item.id ? 'background: #69F8AA;' : ''"></view>
				</view>
			</block>
		</view>
		
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="list-top">{{item.name}}</view>
					<view class="display-a" style="padding: 8rpx 0 24rpx;border-bottom: 1px solid #2C2C2C;">
						<view class="width_176rpx-center">
							<view class="font-size_32rpx margin-bottom_10rpx">{{item.account_count}}</view>
							<view class="color_8E8E8E">账户数量</view>
						</view>
						<view class="width_176rpx-center" v-if="tabId != 3">
							<view class="font-size_32rpx margin-bottom_10rpx">{{item.exposure_count}}</view>
							<view class="color_8E8E8E">曝光量</view>
						</view>
						<view class="width_176rpx-center" v-if="tabId != 3">
							<view class="font-size_32rpx margin-bottom_10rpx">{{item.like_count}}</view>
							<view class="color_8E8E8E">点赞数</view>
						</view>
						<view class="width_176rpx-center" v-if="tabId != 3">
							<view class="font-size_32rpx margin-bottom_10rpx">{{item.comment}}</view>
							<view class="color_8E8E8E">评论数</view>
						</view>
					</view>
					<view class="display-a padding_30rpx">
						<view class="edit color_00FFCA" @click="getEdit(item)">编辑</view>
						<view class="line"></view>
						<view class="edit color_FF0000" @click="getDel(item.id,item.account_count)">删除</view>
					</view>
				</view>
			</block>
		</mescroll-body>
		
		<image class="matrix-11" @click="getBatch()" :src="imgUrl+'376.png'"></image>
		
		<sunui-popup ref="pop2">
			<template v-slot:content>
				<view class="display-a-js padding_30rpx p-bo">
					<view class="font-weight_bold font-size_32rpx" style="margin-left: 300rpx;">添加分组</view>
					<view class="color_FF0000" @click="closeBatch()">关闭</view>
				</view>
				<view style="padding: 34rpx;">
					<view class="font-size_32rpx font-weight_bold margin-bottom_30rpx">分组类型</view>
					<view class="display-a margin-bottom_10rpx">
						<block v-for="(item,index) in tabs" :key="index">
							<view class="display-a width_170rpx-center margin-bottom_20rpx" @click="getType(item.id)">
								<image class="matrix-12" :src="groupType == item.id ? imgUrl+'375.png' : imgUrl+'374.png'"></image>
								<view>{{item.name}}</view>
							</view>
						</block>
					</view>
					<view class="font-size_32rpx font-weight_bold margin-bottom_30rpx">分组名称</view>
					<input type="text" class="input-name" placeholder="请输入分组名称" placeholder-class="placeholder" v-model="groupName" />
					<view class="list-but" @click="getAdd()">确认添加</view>
				</view>
			</template>
		</sunui-popup>

		<sunui-tabbar2 v-if="type !== 1" :fixed="true" :current="tabIndex" tintColor="#00FFCA"
			backgroundColor="#1B1B1B"></sunui-tabbar2>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: null,
				tabIndex: 3,

				imgUrl: this.$imgUrl,

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],

				tabs: [{
						id: '1',
						name: 'D音'
					},
					{
						id: '2',
						name: 'K手'
					},
					{
						id: '3',
						name: '视频号'
					},
					{
						id: '4',
						name: '小红薯'
					},
					// {id:'5',name:'B站'}
				],
				tabId: '1',

				groupType: 1, //1.D音 2.K手 3.视频号 4.小红薯 5.B站
				groupName: '', //分组名称

				windowHeight: '',

				isWhether: true, //判断重复点击

			}
		},

		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)
			}
			//获取系统信息
			uni.getSystemInfo({
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 290;
				}
			})
		},
		
		onShow() {
			this.$nextTick(() => {
				this.mescroll.resetUpScroll();
			});
		},
		
		
		methods: {
			
			getEdit(obj) {
				uni.navigateTo({
					url: '/pages/matrix/editGroup?obj='+JSON.stringify(obj)+'&selLabel='+this.tabId
				})
			},
			
			/*  删除  */
			getDel(id,count) {
				
				if (count > 0) {
					this.$sun.toast("分组内有账户数据,删除失败!",'none');
					return;
				}
				
				uni.showModal({
					title: '提示',
					content: '确认删除该分组?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.delAccountGroup,
								data: {
									uid: uni.getStorageSync("uid"),
									id: id,
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			//添加分组
			async getAdd() {
				
				if (!this.groupName) {
					this.$sun.toast("请输入分组名称", 'none');
					return;
				}
				
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				
				const result = await this.$http.post({
					url: this.$api.addAccountGroup,
					data: {
						name: this.groupName,
						type: this.groupType,
						uid: uni.getStorageSync("uid"),
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.tabId = this.groupType;
						this.groupName = '';
						this.$refs.pop2.close();
						this.isWhether = true;
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
					}, 2000);
				}else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},
			
			//分组类型
			getType(id) {
				this.groupType = id;
			},
			
			//更多
			getBatch() {
				this.$refs.pop2.show({
					style: 'background-color:#fff;width:750rpx;height:auto;border-radius: 20px 20px 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},
			closeBatch() {
				this.$refs.pop2.close();
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.accountGroupList,
					data: {
						type: this.tabId,
						uid: uni.getStorageSync("uid"),
						page: scroll.num,
						psize: 12
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			getLabel(type) {
				this.tabId = type;
				this.groupType = type;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
		}
	}
</script>

<style lang="scss">
	
	.list-public {
		background-color: #0F0F0F;
		padding: 0;
		color: #FFF;
	}
	
	.acc-top {
		padding: 24rpx 0;
		border-bottom: 1px solid rgb(73, 73, 73);
		margin-bottom: 20rpx;
	}
	
	.list-but {
		width: 600rpx;
		background-color: #0E8FF3;
		padding: 24rpx;
		font-size: 30rpx;
		font-weight: bold;
		text-align: center;
		color: #FFFFFF;
		margin-left: 42rpx;
		border-radius: 10rpx;
	}
	
	.input-name {
		width: 640rpx;
		border: 1px solid #EBEBEB;
		border-radius: 10rpx;
		padding: 30rpx 20rpx;
		margin-bottom: 40rpx;
	}
	
	.matrix-12 {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	
	.matrix-11 {
		width: 96rpx;
		height: 96rpx;
		position: fixed;
		bottom: 170rpx;
		right: 20rpx;
		z-index: 9;
	}
	
	.line {
		width: 2rpx;
		background-color: #393939;
		height: 36rpx;
	}
	
	.edit {
		width: 354rpx;
		text-align: center;
		font-size: 30rpx;
	}
	
	.color_1E6CEB {
		color: #1E6CEB;
	}
	
	.color_8E8E8E {
		color: #8E8E8E;
	}
	
	.list-label {
		width: 236rpx;
		text-align: center;
	}
	
	.list-top {
		padding: 34rpx 24rpx;
		font-size: 32rpx;
		color: #FFF;
	}
	
	.tabs-line {
		width: 60rpx;
		background-color: #232323;
		height: 6rpx;
		border-radius: 100rpx;
		margin-left: 63rpx;
		// margin-left: 95rpx;
	}
	
	.tabs {
		// width: 250rpx;
		width: 186rpx;
		text-align: center;
		color: #E8E8E8;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}

</style>
