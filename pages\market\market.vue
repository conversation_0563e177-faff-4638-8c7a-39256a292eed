<template>
	<view>
		<view style="height: 30rpx;"></view>
		<swiper class="swiper-2" :indicator-dots="indicatorDots" indicator-active-color="#FFFFFF" :autoplay="autoplay"
			:circular="true" :interval="interval" :duration="duration">
			<swiper-item v-for="(item,index) in banner" :key="index">
				<image class="pic-img-2" :src="item.pic_url" @click="changeUrl(index,item)"></image>
			</swiper-item>
		</swiper>
		
		<view class="display-a color_FFFFFF margin-bottom_30rpx">
			<view class="width_186rpx-center" @click="getNavList(1)">
				<image class="img-258" :src="imgUrl+'258.png'"></image>
				<view>热门形象</view>
			</view>
			<view class="width_186rpx-center" @click="getNavList(2)">
				<image class="img-258" :src="imgUrl+'259.png'"></image>
				<view>最新形象</view>
			</view>
			<view class="width_186rpx-center" @click="getNavList(3)">
				<image class="img-258" :src="imgUrl+'260.png'"></image>
				<view>热门音色</view>
			</view>
			<view class="width_186rpx-center" @click="getNavList(4)">
				<image class="img-258" :src="imgUrl+'261.png'"></image>
				<view>最新音色</view>
			</view>
		</view>
		
		<view class="m-frame" v-if="voiceList.length > 0">
			<view class="m-bg display-a" :style="{'background-image': 'url('+imgUrl+'262.png'+')'}">
				<view>
					<view class="font-size_36rpx font-weight_bold color_FFFFFF margin-bottom_10rpx">热门推荐</view>
					<view class="color_979797">精心为您挑选热门的音色</view>
				</view>
				<!-- <view class="display-a margin-left-auto">
					<view class="color_c7c7c7">查看更多</view>
					<image class="img-265" :src="imgUrl+'265.png'"></image>
				</view> -->
			</view>
			<block v-for="(item,index) in voiceList" :key="index">
				<view class="display-a padding_20rpx color_FFFFFF margin-bottom_20rpx" style="position: relative;">
					<image class="img-266" :src="imgUrl+'266.png'"></image>
					<image class="img-254" :key="updateKey" @click="playAudio(item.isPlay,index,item.fat_id,item.fat_media_url,item.uid)" :src="item.isPlay == 1 ? imgUrl+'255.png' : imgUrl+'254.png'"></image>
					<view style="width: 510rpx;">
						<view class="display-a margin-bottom_20rpx">
							<view>
								<view class="font-size_30rpx">{{item.title}}</view>
								<!-- <view class="color_979797 font-size_24rpx">{{item.fu_nickname}}</view> -->
							</view>
							<view class="margin-left-auto color_00CE55 font-weight_bold">￥<span class="font-size_36rpx">{{item.price[0].price}}</span></view>
						</view>
						<view class="display-a">
							<view class="color_c7c7c7 font-size_26rpx">{{item.sell_number}} 人使用</view>
							<view class="display-a-jc m-buy" @click="getOpen(item)">
								<view>购买</view>
								<image class="img-58" :src="imgUrl+'58.png'"></image>
							</view>
						</view>
					</view>
				</view>
			</block>
			<view class="h_10rpx"></view>
		</view>
		
		<view class="m-frame" style="background: #000;">
			<image class="img-263" :src="imgUrl+'263.png'"></image>
			<view class="color_979797 margin-bottom_20rpx">精心为您挑选热门的形象</view>
			<view class="display-fw-a">
				<block v-for="(item,index) in imgList" :key="index">
					
					<view>
						<view class="frame">
							
							<!-- 标准 -->
							<image @click="getVideo(item.id,item.price,item.fca_url,item.title)" v-if="item.fca_url" class="r-video" mode="widthFix" :src="item.fca_url+'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"></image>
							<!-- 高级  -->
							<image @click="getVideo(item.id,item.price,item.fa_video_url,item.title)" v-else class="r-video" mode="widthFix" :src="item.fa_video_url+'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"></image>
						</view>
						<view class="frame-title">
							<view style="width: 320rpx;" class="font-overflow font-size_30rpx color_FFFFFF margin-bottom_10rpx">{{item.title}}</view>
							<view class="display-a-js margin-bottom_20rpx">
								<view class="color_c7c7c7 font-size_26rpx">{{item.sell_number}} 人使用</view>
								<view class="font-weight_bold color_FF0000 font-size_30rpx">￥{{item.price[0].price}}</view>
							</view>
							
						</view>
					</view>
					
				</block>
			</view>
		</view>
		
		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="text-align_center margin-bottom_60rpx">
					<view class="font-size_36rpx margin-bottom_20rpx">{{voiceObj.title}}</view>
					<view class="font-size_30rpx">￥<span class="font-size_40rpx margin-left_10rpx">{{voiceObj.price[priceIndex].price}}</span></view>
				</view>
				<view style="height: 60rpx;"></view>
				<view class="margin-bottom_20rpx">选择购买时长</view>
				<scroll-view :scroll-x="true" class="margin-bottom_60rpx" style="width: 700rpx; white-space: nowrap;" :scroll-with-animation="true">
					<view class="display-a">
						<block v-for="(item,index) in voiceObj.price" :key="index">
							<view @click="getTabs(item,index)" class="tabs-a" :class="priceIndex == index ? 'tabs-a-2' : 'tabs-a-1'">{{item.name}}</view>
						</block>
					</view>
				</scroll-view>
				<view style="height: 60rpx;"></view>
				<view class="display-a-js">
					<view class="pay-v" @click="save()">支付</view>
					<view class="cancel" @click="getClose()">取消</view>
				</view>
				
			</template>
		</sunui-popup>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				// 轮播图
				indicatorDots: true,
				autoplay: true,
				interval: 4000,
				duration: 500,
				
				banner: [],
				
				imgUrl: this.$imgUrl,
				
				voiceList: [],
				imgList: [],
				
				voiceObj: {}, //选择的声音
				priceIndex: 0,
				
				updateKey: false,
				
				isWhether: true, //判断重复点击
				
				voiceAudioContext: null,
				
			}
		},
		
		onLoad() {
			this.getBanner();
			this.getImgList();
			this.getVoiceList();
		},
		
		onUnload() {
			if (this.voiceAudioContext && (!this.voiceAudioContext.paused)) {
				this.voiceAudioContext.stop();
			}
		},
		
		onShow() {
			
		},
		
		methods: {
			
			getTabs(obj,index) {
				this.priceIndex = index;
			},
			
			getOpen(obj) {
				this.voiceObj = obj;
				this.$refs.pop3.show({
					style: 'background-color:#fff;width:750rpx;border-radius: 10rpx 10rpx 0 0;padding: 50rpx 24rpx',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			
			getClose() {
				this.$refs.pop3.close();
			},
			
			async save() {
				
				if (uni.getStorageSync('uid')) {
					if (!this.isWhether) {
						return;
					}
					this.isWhether = false;
					
					const result = await this.$http.post({
						url: this.$api.createOrder,
						data: {
							uid: uni.getStorageSync('uid'),
							id: this.voiceObj.id,
							price: this.voiceObj.price[this.priceIndex].price,
							day_index: this.voiceObj.price[this.priceIndex].index
						}
					});
					if (result.errno == 0) {
						this.getClose();
						this.wxPay(result.data)
					} else {
						this.$sun.toast(result.message, 'none');
						this.isWhether = true;
					}
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "返回",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								
							}
						}
					})
				}
			},
			
			/*  微信支付  */
			async wxPay(log_no) {
				const result = await this.$http.post({
					url: this.$api.pay,
					data: {
						openid: uni.getStorageSync('openid'),
						price: this.voiceObj.price[this.priceIndex].price,
						log_no: log_no,
						name: '购买 ' + this.voiceObj.name 
					}
				});
				if (result.errno == 0) {
					uni.requestPayment({
						provider: 'wxpay',
						timeStamp: result.data.timeStamp,
						nonceStr: result.data.nonceStr,
						package: result.data.package,
						signType: result.data.signType,
						paySign: result.data.paySign,
						success: async (res) => {
							this.$sun.toast("支付成功");
							setTimeout(() => {
								this.isWhether = true;
							}, 2000);
						},
						fail: (err) => {
							this.isWhether = true;
							this.$sun.toast("取消支付", 'error');
						}
					});
				} else {
					this.isWhether = true;
					if (result.errno == -1) {
						this.$sun.toast(result.message, 'none');
						return;
					}
					if (result.return_code == 'FAIL') {
						uni.showModal({
							title: '支付配置错误',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
			
								}
							}
						})
					} else {
						uni.showModal({
							title: '提示',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
			
								}
							}
						})
					}
				}
			},
			
			//购买形象
			getVideo(id,price,base_video,name) {
				uni.navigateTo({
					url: '/pages/market/videos?id='+id+'&price='+JSON.stringify(price)+'&base_video='+base_video+'&name='+name
				})
			},
			
			getNavList(type) {
				uni.navigateTo({
					url: '/pages/market/list?type='+type
				})
			},
			
			//播放音频
			async playAudio(isPlay,index,id,media_url,uid) {
				
				this.updateKey = false;
				
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				
				let getUrl = '';
				let getDate = '';
				
				getUrl = this.$api.ttsVoice;
				getDate = {
					uid: uid,
					voice_id: id,
					text: '这是你的高保真声音克隆效果，你觉得效果怎么样?'
				}
				
				if (media_url) {
					
					this.getA(isPlay,index,media_url,1);
					
				}else {
					const result = await this.$http.post({
						url: getUrl,
						data: getDate
					});
					if (result.errno == 0) {
								
						if (result.data) {
							
							this.getSetDefaultVoice(result.data,id,uid);
							
							this.getA(isPlay,index,result.data,2);
							
						} else {
							this.$sun.toast("音频生成失败,请联系平台处理!", 'none');
							this.isWhether = true;
						}
					} else {
						if (result.errno == -1) {
							this.$sun.toast("音频播放失败,请重新点击播放", 'none');
						}else {
							this.$sun.toast(result.message, 'none');
						}
						this.isWhether = true;
					}
				}
				
			},
			
			//播放音频
			getA(isPlay,index,media_url,type) {
				
				uni.showLoading({
					title: '正在试听...',
					mask: true
				})
				
				this.voiceAudioContext = null;
				
				this.voiceAudioContext = uni.createInnerAudioContext();
				
				this.voiceAudioContext.src = media_url;
				
				setTimeout(() => {
					if (isPlay == 2) {
						this.voiceList[index].isPlay = 1;
						this.updateKey = true;
						this.voiceAudioContext.play();
						this.voiceAudioContext.onPlay(() => {
							
						});
						this.voiceAudioContext.onEnded(() => {
							this.voiceList[index].isPlay = 2;
							uni.hideLoading();
							this.voiceAudioContext.destroy();
							this.voiceAudioContext = null;
							this.$sun.toast("试听完成");
							this.updateKey = true;
							this.isWhether = true;
							if (type == 2) {
								setTimeout(() => {
									this.getVoiceList();
								}, 1000);
							}
						});
						this.voiceAudioContext.onError((err) => {
							// console.log('播放音频出错：', err);
							this.$sun.toast("音频播放出错:" + err, 'none');
							uni.hideLoading();
							this.voiceAudioContext.destroy();
							this.voiceAudioContext = null;
							this.isWhether = true;
							this.updateKey = true;
						});
					}else {
						this.voiceList[index].isPlay = 2;
						this.updateKey = true;
						this.voiceAudioContext.pause();
						this.voiceAudioContext.onPause(() => {
							// console.log('暂停播放');
						});
					}
				}, 500);
			},
			
			//设置试听
			async getSetDefaultVoice(aUrl,id,uid) {
				const result = await this.$http.post({
					url: this.$api.setDefaultVoice,
					data: {
						uid: uid,
						voice_id: id, 
						media_url: aUrl, // 音频链接 
					}
				});
				if (result.errno == 0) {
					this.isWhether = true;
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},
			
			//热门形象
			async getImgList() {
				
				const result = await this.$http.post({
					url: this.$api.goodsList,
					data: {
						order_field: '',//sell_number 热门排序 create_time 最新排序
						type: 4, //type 1 高级形象 type ：2 标准形象 type 3声音，4 高级形象和 标准形象
						status: '1', //0全部 1上架 ，2下架
						page: 1,
						psize: 4
					}
				});
				if (result.errno == 0) {
					
					this.imgList = result.data.list;
					
				}
			},
			
			//热门声音
			async getVoiceList() {
				
				const result = await this.$http.post({
					url: this.$api.goodsList,
					data: {
						order_field: '',//sell_money 热门排序 create_time 最新排序
						type: 3, //type 1 高级形象 type ：2 标准形象 type 3声音，4 高级形象和 标准形象
						status: '1', //0全部 1上架 ，2下架
						page: 1,
						psize: 4
					}
				});
				if (result.errno == 0) {
					
					this.voiceList = result.data.list;
					
					for (let i = 0 ; i < this.voiceList.length ; i++) {
						this.voiceList[i].isPlay = 2;
					}
					
				}
			},
			
			//轮播图
			async getBanner() {
				const result = await this.$http.post({
					url: this.$api.banner,
					data: {
						b_type: 4
					}
				});
				if (result.errno == 0) {
					this.banner = result.data;
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.cancel {
		background-color: #FA5551;
		width: 320rpx;
		text-align: center;
		padding: 26rpx 0;
		border-radius: 10rpx;
		color: #FFF;
		font-size: 30rpx;
	}
	
	.pay-v {
		background-color: #00CE55;
		width: 320rpx;
		text-align: center;
		padding: 26rpx 0;
		border-radius: 10rpx;
		color: #FFF;
		font-size: 30rpx;
	}
	
	.tabs-a-2 {
		background-color: #FA5551;
		color: #FFF;
	}
	.tabs-a-1{
		background-color: #EDEDED;
		color: #000;
	}
	
	.tabs-a {
		width: 190rpx;
		text-align: center;
		border-radius: 10rpx;
		padding: 20rpx 0;
		margin-right: 20rpx;
	}
	
	.r-video {
		width: 344rpx;
		border-radius: 10rpx 10rpx 0 0;
		position: absolute;
		z-index: 2;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
	
	.frame {
		position: relative;
		width: 344rpx;
		height: 612rpx;
		border-radius: 10rpx 10rpx 0 0;
		margin-right: 10rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: block;
		/* 避免底部空白 */
	}
	
	.frame-title {
		background: linear-gradient(180.00deg, rgb(40, 36, 48),rgb(26, 26, 26) 100%);
		padding: 10rpx;
		width: 344rpx;
		border-radius: 0 0 10rpx 10rpx;
		margin-bottom: 20rpx;
	}
	
	.img-263 {
		width: 298rpx;
		height: 78rpx;
	}
	
	.img-266 {
		width: 140rpx;
		height: 140rpx;
		margin-right: 20rpx;
		border-radius: 10rpx;
	}
	
	.img-58 {
		width: 24rpx;
		height: 24rpx;
		margin-left: 2rpx;
	}
	
	.m-buy {
		width: 120rpx;
		padding: 4rpx 0;
		border: 1px solid rgb(115, 115, 115);
		color: #FFF;
		font-size: 26rpx;
		margin-left: auto;
		border-radius: 100rpx;
	}
	
	.img-254 {
		position: absolute;
		z-index: 9;
		width: 140rpx;
		height: 140rpx;
		border-radius: 10rpx;
		left: 20rpx;
		top: 20rpx;
	}
	
	.img-265 {
		width: 30rpx;
		height: 30rpx;
	}
	
	.m-bg {
		width: 710rpx;
		height: 130rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 20rpx;
	}
	
	.m-frame {
		width: 710rpx;
		border-radius: 20rpx;
		background: linear-gradient(180.00deg, rgb(40, 36, 48),rgb(26, 26, 26) 100%);
		margin: 0 20rpx 20rpx;
	}
	
	.img-258 {
		width: 94rpx;
		height: 94rpx;
		margin-bottom: 10rpx;
	}
	
	.pic-img-2 {
		width: 710rpx;
		height: 340rpx;
		border-radius: 10rpx;
	}
	
	.swiper-2 {
		width: 710rpx;
		height: 340rpx;
		margin: 0 20rpx 44rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
	
</style>
