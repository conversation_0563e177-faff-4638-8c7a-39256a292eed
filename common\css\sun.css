/* 
	sunUI(轻便、简洁、易扩展) - v1.1(email:<EMAIL>) - 2020.03.30
	
	Q. 为什么不把具体的样式写进sunUI.css
	A. 为了方便轻便、简介性、扩展性方便大家的“使用”,sunUI.css仅提供基础样式
	
	Q. 为什么仅仅提供了示例,而非完整的样式?
	A. 方便大家学习并了解其中的原理; 授人以鱼不如授人以渔
	
	Q. 我能为sunUI提供一些组件和样式以及需求吗?
	A. 非常感谢大家能提供样式、组件、模板和bug以及需求,如果提供了版本迭代时就会贴上对应的署名
	
	Tips:
	1.如果样式被flex布局影响了,直接修改display属性为block或其它就可以避免所有布局影响.
	2.公共样式最好不要写上最大权限值important
	3.关于脱离文档流(position:absolute,fixed...)和文档流(font-size、color)要分清顺序(可以减少dom操作影响)
	4.z-index仅当前样式有position属性才生效,z-index:9999
	5.使用别套UI要参考ui前缀,譬如:sunui-xx
	6.样式最好分离管理(最后利用一些工具,例如webpack合成)
	7.button、view、navigator支持hover-class;其余标签则不支持
	8.uniapp 特别是最外层page最好不要使用外边距(margin),最好使用内边距(padding)
	9.元素透明:transparent
	10.currentColor表示“当前的标签所继承的文字颜色”
 */

view {
	font-size: 28rpx;
	/* line-height: 1.7em; */
	font-family: -apple-system-font, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", STHeiti, "Microsoft Yahei", Tahoma, Simsun, sans-serif;
	/* 会对样式造成非常大的影响 */
	box-sizing: border-box;
	-webkit-overflow-scrolling: touch;
}


button,
button:after,
button:before {
	border-radius: 0;
	border: 0;
	margin: 0;
	position: relative;
	color: #fff;
}

button[type=default] {
	color: #fff;
}

image,
video,
progress,
textarea,
input {
	width: 100%;
	/* 为了兼容同层渲染 */
	position: relative;
	z-index: 1;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none !important;
}


/* button禁用样式 */
button[disabled] {
	opacity: .4;
	background-color: #F5F5F5;
	cursor: not-allowed !important;
}

.sunui-line1 {
	line-height: 1;
}

.sunui-line2 {
	line-height: 2;
}

.sunui-sticky {
	position: sticky !important;
	top: 0;
	background-color: #fff;
	z-index: 9990;
}

.sunui-button-mini {
	background-color: #007aff;
	border-radius: 40rpx;
	line-height: 1;
	font-size: 24rpx;
	padding: 20rpx 40rpx;
	display: inline-block;
	color: #fff;
}

.sunui-button-mini:after {
	content: ' ';
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	transform: scale(0.5);
	transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: inherit;
	z-index: 1;
	pointer-events: none;
	border: 1rpx solid currentColor;
}


.sunui-btn-mini {
	display: inline-block;
	width: auto;
	padding: 0 0.75em;
	line-height: 2;
	font-size: 32rpx;
	font-weight: bold;
	border-radius: 8rpx;
	overflow: hidden;
	color: #fff;
}

.sunui-btn-middle {
	position: relative;
	display: block;
	width: 368rpx;
	margin-left: auto;
	margin-right: auto;
	padding: 8px 24px;
	box-sizing: border-box;
	font-weight: bold;
	font-size: 34rpx;
	text-decoration: none;
	color: #fff;
	line-height: 1.2;
	border-radius: 8rpx;
	overflow: hidden;
}


.sunui-btn-middle-buy {
	display: inline-block;
	height: 70rpx;
	line-height: 70rpx;
	font-size: 28rpx;
}

.sunui-btn-radius {
	border-radius: 40rpx;
}

.sunui-network-mtop {
	margin-top: 88rpx;
}

/* 关于ios安全区域(iphone x,iphone xs ,iphone xs max, iphone xr),详情见：https://blog.csdn.net/qq_42354773/article/details/81018615、https://blog.csdn.net/qq_43071910/article/details/85294434 */
.sunui-iphonex {
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

.sunui {
	padding: 20rpx;
}

.sunui-relative {
	position: relative;
}

.sunui-absolute {
	position: absolute;
}

.sunui-fixed {
	position: fixed;
}

.sunui-margin5 {
	margin: 5rpx;
}

.sunui-margin10 {
	margin: 10rpx;
}

.sunui-margin15 {
	margin: 15rpx;
}

.sunui-margin20 {
	margin: 20rpx;
}

.sunui-margin-r10 {
	margin-right: 10rpx;
}

.sunui-margin-r20 {
	margin-right: 20rpx;
}

.sunui-margin-r30 {
	margin-right: 30rpx;
}

.sunui-padding5 {
	padding: 5rpx;
}

.sunui-padding10 {
	padding: 10rpx;
}

.sunui-padding15 {
	padding: 15rpx;
}

.sunui-padding-l10 {
	padding-left: 10rpx;
}

.sunui-padding-l20 {
	padding-left: 20rpx;
}

.sunui-padding-l30 {
	padding-left: 30rpx;
}

.sunui-padding20 {
	padding: 20rpx;
}

.sunui-text-blue {
	color: #038dd8;
}

.sunui-text-green {
	color: #038dd8;
}

.sunui-text-yellow {
	color: #038dd8;
}

.sunui-text-orange {
	color: #038dd8;
}

.sunui-bg-blue {
	background-color: #038dd8 !important;
}

.sunui-bg-green {
	background-color: #009b4d !important;
}

.sunui-bg-f7f7 {
	background-color: #F7F7F7 !important;
}

.sunui-bg-yellow {
	background-color: #fdd100 !important;
}

.sunui-bg-orange {
	background-color: #f18518 !important;
}

.sunui-lgbg-green {
	background-image: linear-gradient(45deg, #39B55A 0%, #8DC63E 80%);
}

.sunui-show {
	display: block !important;
}

.sunui-hidden {
	display: none !important;
}

.sunui-left {
	text-align: left;
}

.sunui-right {
	text-align: right;
}

.sunui-center {
	text-align: center;
}

.sunui-text-left {
	text-align: left;
}

.sunui-text-center {
	text-align: center;
}

.sunui-text-right {
	text-align: right;
}

.sunui-text-white {
	color: #fff;
}

.sunui-text-red {
	color: #e54d42;
}

.sunui-text-orange {
	color: #f37b1d;
}

.sunui-w100 {
	width: 100%;
}

.sunui-w50 {
	width: 50%;
}

.sunui-w33 {
	width: calc(100% / 3);
}

.sunui-w20 {
	width: 20%;
}

.sunui-redius-small {
	border-radius: 20rpx !important;
}

.sunui-redius-middle {
	border-radius: 30rpx !important;
}

.sunui-redius-big {
	border-radius: 100rpx !important;
}

.sunui-flex-grow {
	flex-grow: 1;
}

.sunui-flex-shrink {
	flex-shrink: 0;
}

.sunui-item-order4 {
	order: 1;
}

.sunui-item-order3 {
	order: 2;
}

.sunui-item-order2 {
	order: 3;
}

.sunui-item-order1 {
	order: 4;
}

.sunui-flex {
	display: flex;
}

.sunui-flex-flow {
	display: flex;
	flex-flow: row wrap;
}

.sunui-flex-column {
	display: flex;
	flex-direction: column;
}

.sunui-flex-row {
	display: flex;
	flex-direction: row;
}

.sunui-flex-1 {
	flex: 1;
}

.sunui-flex-wrap {
	display: flex;
	flex-wrap: wrap;
}

.sunui-flex-1-1-0 {
	flex: 1 1 0;
}

.sunui-flex-start {
	display: flex;
	justify-content: flex-start;
}

.sunui-flex-center {
	display: flex;
	justify-content: center;
}

.sunui-flex-end {
	display: flex;
	justify-content: flex-end;
}

.sunui-flex-space-between {
	display: flex;
	justify-content: space-between;
}

.sunui-flex-space-around {
	display: flex;
	justify-content: space-around;
}

.sunui-flex-space-center{
	display: flex;
	justify-content: center;
}

.sunui-flex-space-evenly {
	display: flex;
	justify-content: space-evenly;
}

.sunui-flex-align-center {
	display: flex;
	align-items: center;
}

.sunui-flex-align-baseline {
	display: flex;
	align-items: baseline;
}

.sunui-border-t {
	border-top: 1rpx solid #eee;
}

.sunui-border-l {
	border-left: 1rpx solid #eee;
}

.sunui-border-b {
	border-bottom: 1rpx solid #eee;
}

.sunui-border-r {
	border-right: 1rpx solid #eee;
}

.sunui-text-weight {
	font-weight: bold;
}

/* cell单元格 */
.sunui-cell {
	position: relative;
	display: flex;
	width: 100%;
	padding: 10px 15px;
	font-size: 1em;
	color: #333;
	background-color: #fff;
	box-sizing: border-box;
}

.sunui-cell-label {
	margin-top: 3px;
	font-size: 12px;
	line-height: 18px;
	color: #999;
}

.sunui-cell:after {
	content: ' ';
	position: absolute;
	bottom: 0;
	/* width: 96%; */
	/* border-bottom: 1px solid #eee; */
}

.sunui-cell-title {
	flex: 1;
}

.sunui-cell-value {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	overflow: hidden;
	color: #999;
	text-align: right;
	vertical-align: middle;
}


/* 标题 */
.sunui-title {
	margin: 0;
	margin-bottom: 4%;
	font-weight: bold;
	font-size: 34rpx;
	color: #000;
	padding: 15px 10px 10px;
}

.sunui-badge-msg {
	display: inline-block;
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
}

.sunui-badge-num {
	display: inline-block;
	/* height: 40rpx; */
	line-height: 1;
	padding: 6rpx 12rpx;
	font-size: 24rpx;
	text-align: center;
	color: #fff;
}


/* tag标签 */
.sunui-tag {
	color: #fff;
	font-size: 10px;
	padding: .2em .5em;
	line-height: normal;
	border-radius: .2em;
	display: inline-block;
}

.sunui-tag+.sunui-transpaint {
	background-color: transparent;
}

.sunui-text-ellipsis {
	width: 160rpx;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.sunui-ellipsis-1 {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-line-clamp: 1;
}

.sunui-ellipsis-2 {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-line-clamp: 2;
}

.sunui-ellipsis-3 {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-line-clamp: 3;
}

/* bg背景 */

.sunui-box-shadow {
	box-shadow: 0px 0px 8px #D5D6D8;
}

.sunui-shadow {
	position: relative;
}

.sunui-shadow:before {
	content: "";
	display: block;
	width: 100%;
	height: 100%;
	background: inherit;
	filter: blur(3px);
	position: absolute;
	top: 4px;
	left: 4px;
	z-index: -1;
	opacity: 0.38;
	transform-origin: 0 0;
	border-radius: inherit;
	transform: scale(1, 1);
}

/* hover-class*/
.sunui-hover {
	opacity: .9;
	position: relative;
}

.sunui-hover:after {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background-color: rgba(0, 0, 0, .01);
}

.btn.btn-hover {
	transform: translate(1rpx, 1rpx);
}

.sunui-radius10 {
	border-radius: 10rpx;
}

.sunui-radius40 {
	border-radius: 40rpx;
}

.sunui-radius50per {
	border-radius: 50%;
}

.sunui-avatar-small {
	width: 128rpx;
	height: 128rpx;
}

.sunui-avatar-middle {
	width: 160rpx;
	height: 160rpx;
}

.sunui-avatar-big {
	width: 220rpx;
	height: 220rpx;
}

.sunui-block {
	display: block;
}

.sunui-inline {
	display: inline;
}

.sunui-inlineblock {
	display: inline-block;
}

/* 搜索样式 */
.sunui-search-w {
	display: flex;
	align-items: center;
}

.sunui-search-w>button {
	color: #fff;
	border-radius: 50rpx;
	margin-left: 10rpx;
	background-color: #4a8af4;
	box-shadow: 0rpx 1rpx 10rpx #C8C7CC;
}

.sunui-search-w .sunui-search {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50rpx;
	border: 1rpx solid #eee;
	padding: 4rpx 10rpx;
	width: 70%;
}

.sunui-search-w .sunui-search input,
.sunui-search-w .sunui-search icon {
	font-size: 28rpx;
	padding-left: 10rpx;
}

/* 占位符 */
.sunui-placeholder-wrap {
	padding-top: 31.25%;
	position: relative;
}

.sunui-placeholder-content {
	color: #bbb;
	position: absolute;
	top: 0;
	width: 100%;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	text-align: center;
	height: 100%;
	z-index: -1;
}

/* 上图下文 */
.sunui-layout-seesaw {
	display: flex;
	flex-wrap: wrap;
	margin: 4% 0;
}

.sunui-layout-seesaw>view {
	width: calc(100% / 4);
	text-align: center;
	color: #666;
	font-size: 22rpx;
}

.sunui-layout-seesaw>view image {
	width: 80rpx;
	height: 80rpx;
}

.sunui-layout-seesaw.sunui-layout-seesaw-nine {
	border-top: 1rpx solid #eee;
}

.sunui-layout-seesaw.sunui-layout-seesaw-nine view {
	line-height: 1;
}

.sunui-layout-seesaw.sunui-layout-seesaw-nine>view {
	border-right: 1rpx solid #eee;
	border-bottom: 1rpx solid #eee;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.sunui-layout-seesaw.sunui-layout-seesaw-nine>view:nth-child(4) {
	border-right: 0;
}

/* 左图右文 */
.sunui-layout-lprt {
	display: flex;
	flex-wrap: wrap;
	margin: 4%;
}

.sunui-layout-lprt .layout-right{
	width: 550rpx;
}

.sunui-layout-lprt .layout-right>view {
	line-height: 1;
	padding-left: 10rpx;
}

.sunui-layout-lprt>view {
	display: flex;
	width: 100%;
}

.sunui-layout-lprt>view image {
	width: 160rpx;
	height: 160rpx;
}

/* 轮播 */
.sunui-swiper {
	/* margin: 24rpx 0; */
	height: 360rpx;
	overflow: hidden;
	border-radius: 10rpx;
}

.sunui-swiper image {
	height: 100%;
}

.sunui-relative .title {
	width: 100%;
	color: #fff;
	padding: 10rpx;
	background-color: rgba(0, 0, 0, .5);
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	position: absolute;
	bottom: 0;
	z-index: 999;
}

/* 优惠券 */
.sunui-coupon-top text {
	font-size: 50rpx;
	color: #fff;
}

.sunui-coupon-middle {
	color: #fff;
}

.sunui-coupon-bottom {
	color: #000;
	font-size: 24rpx;
}

.sunui-coupon {
	padding: 10rpx;
	margin-bottom: 5rpx;
}

.sunui-coupon-wrapper {
	margin: 0 auto;
	width: calc(100% - 20rpx);
	display: flex;
	background: linear-gradient(-90deg, rgba(250, 173, 82, 1), rgba(254, 50, 103, 1));
}

.sunui-coupon-content {
	position: relative;
	flex: 1;
	padding: 20rpx;
	text-align: left;
	white-space: nowrap;
	display: flex;
	flex-direction: column;
	justify-content: space-around;
}

.sunui-coupon-tip {
	position: relative;
	padding: 50rpx 30rpx;
	text-align: center;
}

.sunui-coupon-line {
	position: relative;
	flex: 0 0 0;
	margin: 0 10rpx 0 6rpx;
	border-left: 4rpx dashed #eee;
}

.sunui-coupon-line:before,
.sunui-coupon-line:after {
	content: '';
	position: absolute;
	width: 32rpx;
	height: 16rpx;
	background: #fff;
	left: -18rpx;
	z-index: 1;
}

.sunui-coupon-content:before,
.sunui-coupon-content:after {
	content: '';
	position: absolute;
	width: 32rpx;
	height: 16rpx;
	/* background-color: #fff; */
	left: -16rpx;
	z-index: 1;
}

.sunui-coupon-tip:before,
.sunui-coupon-tip:after {
	content: '';
	position: absolute;
	width: 32rpx;
	height: 16rpx;
	/* background-color: #fff; */
	right: -16rpx;
	z-index: 1;
}

.sunui-coupon-content:before,
.sunui-coupon-tip:before,
.sunui-coupon-line:before {
	border-radius: 0 0 16rpx 16rpx;
	top: 0;
}

.sunui-coupon-content:after,
.sunui-coupon-tip:after,
.sunui-coupon-line:after {
	border-radius: 16rpx 16rpx 0 0;
	bottom: 0;
}

.sunui-coupon-conditions {
	color: #eee;
	font-size: 30rpx;
	padding: 15rpx;
}

.sunui-coupon-btn {
	color: rgba(254, 50, 103, 1);
	font-size: 30rpx;
	border-radius: 50rpx;
	border-style: none;
}

/* 页面通知消息 */

.sunui-msg-page-title {
	margin-top: 64rpx;
	margin-bottom: 32rpx;
	font-weight: 700;
	font-size: 44rpx;
	color: rgba(0, 0, 0, 0.9);
	word-wrap: break-word;
	word-break: break-all;
}

.sunui-msg-page-desc {
	font-size: 34rpx;
	color: rgba(0, 0, 0, 0.9);
	word-wrap: break-word;
	word-break: break-all;
	margin-bottom: 16px;
}

.sunui-msg-page-btn {
	background-color: #07c160;
	color: #fff;
	border-radius: 8rpx;
	margin: auto;
	margin-top: 400rpx;
	width: 50%;
	font-size: 34rpx;
	font-weight: bold;
}


/* 提现 */
.sunui-record .sunui-record-head {
	background-color: #ff4d4d;
	padding: 40rpx 20rpx;
	display: flex;
	justify-content: space-between;
}

.sunui-record .sunui-record-head .sunui-record-headleft {
	color: #fff;
}

.sunui-record .sunui-record-head .sunui-record-headleft .sunui-record-balance {
	font-size: 44rpx;
	padding-top: 20rpx;
}

.sunui-record .sunui-record-head .sunui-record-cash {
	background-color: #fff;
	color: #fd6766;
	height: 60rpx;
	line-height: 60rpx;
	width: 180rpx;
	text-align: center;
	border-radius: 40rpx;
	margin-top: 30rpx;
}

.sunui-record .sunui-record-list {
	margin: 30rpx 20rpx;
	box-shadow: 0 0 20rpx #ededed;
	border-radius: 10rpx;
	padding: 30rpx 20rpx;
}

.sunui-record .sunui-record-list .sunui-record-list-left {
	display: flex;
	justify-content: space-between;
	font-size: 32rpx;
	font-weight: 600;
	padding-bottom: 20rpx;
}

.sunui-record .sunui-record-list .sunui-record-list-right {
	display: flex;
	justify-content: space-between;
	color: #9a9a9a;
}

.sunui-record .sunui-record-list .sunui-record-list-right .sunui-record-status {
	color: #fc4f50;
}

/* 遮罩层 */
.sunui-mask {
	background-color: rgba(0, 0, 0, 0.5);
	position: absolute;
	height: 100%;
	width: 100%;
	z-index: 9999;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}
