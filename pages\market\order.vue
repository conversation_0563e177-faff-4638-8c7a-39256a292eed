<template>
	<view>
		
		<view class="display-a c-top margin-bottom_30rpx">
			<block v-for="(item,index) in tabs" :key="index">
				<view class="display-ac-jc width_374rpx-center" @click="getId(item)">
					<view :class="tabsId == item.id ? 'color_4088FF' : 'color_9B9B9B'">{{item.name}}</view>
					<view class="c-line" :class="tabsId == item.id ? 'c-line-1' : 'c-line-2'"></view>
				</view>
			</block>
		</view>
		<view class="display-a margin-bottom_30rpx">
			<block v-for="(items,indexs) in nextList" :key="indexs">
				<view @click="getNextId(items.id)" class="a-tabs"
					:class="tabsNextId == items.id ? 'a-tabs-1' : 'a-tabs-2'">{{items.name}}</view>
			</block>
			<view class="display-a margin-left-auto color_FFFFFF">
				<picker mode="date" fields="month" :value="birth" :start="startDate" :end="endDate"
					@change="bindDateChange">
					<input type="text" style="width: 120rpx;" disabled placeholder="请选择" v-model="birth"
						placeholder-class="font-size_28rpx" />
				</picker>
				<image class="img-267" :src="imgUrl+'267.png'"></image>
			</view>
		</view>
		
		<mescroll-body ref="mescrollRef" :optionIcon="optionIcon" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a color_B7B7B7 padding-bottom_20rpx p-bo margin-bottom_20rpx">
						<image class="img-268" :src="imgUrl+'268.png'"></image>
						<view>订单号: {{item.log_no}}</view>
						<view class="copy" @click="getCopy(item.log_no)">复制</view>
					</view>
					<view class="display" v-if="tabsId == 4">
						<view class="frame">
							<!-- 标准 -->
							<image v-if="item.fca_url" class="r-video" mode="widthFix" :src="item.fca_url+'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"></image>
							<!-- 高级  -->
							<image v-else class="r-video" mode="widthFix" :src="item.fa_video_url+'?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"></image>
						</view>
						<view class="color_FFFFFF">
							<view style="width: 510rpx;" class="font-size_32rpx font-overflow margin-bottom_20rpx">{{item.frg_title}}</view>
							<view class="color_B7B7B7 font-size_26rpx margin-bottom_20rpx">下单时间: {{item.create_time}}</view>
							<view class="color_B7B7B7 font-size_26rpx" style="margin-bottom: 46rpx;">使用期限: {{item.effective_day == 0 ? '永久' : item.effective_day+'天'}}</view>
							<view class="font-size_30rpx">实付金额: <span class="font-size_32rpx font-weight_bold color_FF0000 margin-left_10rpx">￥{{item.price}}</span></view>
						</view>
					</view>
					<view class="display" style="position: relative;" v-if="tabsId == 3">
						<image class="img-266" :src="imgUrl+'266.png'"></image>
						<image class="img-254" :key="updateKey" @click="playAudio(item.isPlay,index,item.fat_id,item.fat_media_url,item.sell_uid)" :src="item.isPlay == 1 ? imgUrl+'255.png' : imgUrl+'254.png'"></image>
						<view class="color_FFFFFF">
							<view style="width: 490rpx;" class="font-size_32rpx font-overflow margin-bottom_20rpx">{{item.frg_title}}</view>
							<view class="color_B7B7B7 font-size_26rpx margin-bottom_20rpx">下单时间: {{item.create_time}}</view>
							<view class="display-a-js">
								<view class="color_B7B7B7 font-size_26rpx">使用期限: {{item.effective_day == 0 ? '永久' : item.effective_day+'天'}}</view>
								<view class="font-size_30rpx">实付金额: <span class="font-size_32rpx font-weight_bold color_FF0000 margin-left_10rpx">￥{{item.price}}</span></view>
							</view>
						</view>
					</view>
				</view>
			</block>
		</mescroll-body>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				tabs: [{
						id: 4,
						name: '形象资产',
						list: []
					},
					{
						id: 3,
						name: '声音资产',
						list: []
					}
				],
				
				tabsId: 4, 
				tabsNextId: 2,
				nextList: [
					{
						id: 2,
						name: '卖出资产'
					}, {
						id: 1,
						name: '买入资产'
					}
				],
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				imgUrl: this.$imgUrl,
				
				updateKey: false,
				
				isWhether: true, //判断重复点击
				
				voiceAudioContext: null,
				
				birth: '',
				yeartime: '',
				monthtime: '',
				
			}
		},
		
		onLoad() {
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 240;
				},
			})
		},
		
		onUnload() {
			if (this.voiceAudioContext && (!this.voiceAudioContext.paused)) {
				this.voiceAudioContext.stop();
			}
		},
		
		onShow() {
			if (uni.getStorageSync('uid')) {
				var myDate = new Date();
				this.yeartime = myDate.getFullYear();
				this.monthtime = myDate.getMonth() + 1;
				if (this.monthtime.toString().length == 1) {
					this.monthtime = '0' + this.monthtime;
				}
				this.birth = this.yeartime + '-' + this.monthtime;
				setTimeout(()=> {
					this.$nextTick(() => {
						this.mescroll.resetUpScroll();
					});
				}, 1000);
			}else {
				uni.showModal({
					content:"请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},
		
		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		
		methods: {
			
			//播放音频
			async playAudio(isPlay,index,id,media_url,uid) {
				
				this.updateKey = false;
				
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;
				
				let getUrl = '';
				let getDate = '';
				
				getUrl = this.$api.ttsVoice;
				getDate = {
					uid: uid,
					voice_id: id,
					text: '这是你的高保真声音克隆效果，你觉得效果怎么样?'
				}
				
				if (media_url) {
					
					this.getA(isPlay,index,media_url,1);
					
				}else {
					const result = await this.$http.post({
						url: getUrl,
						data: getDate
					});
					if (result.errno == 0) {
								
						if (result.data) {
							
							this.getSetDefaultVoice(result.data,id,uid);
							
							
							this.getA(isPlay,index,result.data,2);
							
						} else {
							this.$sun.toast("音频生成失败,请联系平台处理!", 'none');
							this.isWhether = true;
						}
					} else {
						if (result.errno == -1) {
							this.$sun.toast("音频播放失败,请重新点击播放", 'none');
						}else {
							this.$sun.toast(result.message, 'none');
						}
						this.isWhether = true;
					}
				}
				
			},
			
			//播放音频
			getA(isPlay,index,media_url,type) {
				
				console.log("isPlay---->",isPlay,media_url);
				
				uni.showLoading({
					title: '正在试听...',
					mask: true
				})
				
				this.voiceAudioContext = null;
				
				this.voiceAudioContext = uni.createInnerAudioContext();
				
				this.voiceAudioContext.src = media_url;
				
				setTimeout(() => {
					if (isPlay == 2) {
						this.list[index].isPlay = 1;
						console.log("----->",this.list[index].isPlay);
						this.updateKey = true;
						this.voiceAudioContext.play();
						this.voiceAudioContext.onPlay(() => {
							
						});
						this.voiceAudioContext.onEnded(() => {
							this.list[index].isPlay = 2;
							uni.hideLoading();
							this.voiceAudioContext.destroy();
							this.voiceAudioContext = null;
							this.$sun.toast("试听完成");
							this.updateKey = true;
							this.isWhether = true;
							if (type == 2) {
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 1000);
							}
						});
						this.voiceAudioContext.onError((err) => {
							// console.log('播放音频出错：', err);
							this.$sun.toast("音频播放出错:" + err, 'none');
							uni.hideLoading();
							this.voiceAudioContext.destroy();
							this.voiceAudioContext = null;
							this.isWhether = true;
							this.updateKey = true;
						});
					}else {
						this.list[index].isPlay = 2;
						this.isWhether = true;
						this.updateKey = true;
						this.voiceAudioContext.pause();
						this.voiceAudioContext.onPause(() => {
							// console.log('暂停播放');
						});
					}
				}, 500);
			},
			
			//设置试听
			async getSetDefaultVoice(aUrl,id) {
				const result = await this.$http.post({
					url: this.$api.setDefaultVoice,
					data: {
						uid: uid,
						voice_id: id, 
						media_url: aUrl, // 音频链接 
					}
				});
				if (result.errno == 0) {
					this.isWhether = true;
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},
			
			/*  日期选择  */
			bindDateChange(e) {
				// console.log("选择的日期", e.target.value);
				this.birth = e.target.value;
				this.yeartime = e.target.value.split('-')[0];
				this.monthtime = e.target.value.split('-')[1];
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();
			
				if (type === 'start') {
					year = year - 100;
				} else if (type === 'end') {
					year = year;
				}
				month = month > 9 ? month : '0' + month;;
				// day = day > 9 ? day : '0' + day;
				return `${year}-${month}`;
			},
			
			//复制
			getCopy(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						// 复制成功的回调
						this.$sun.toast('复制成功');
						// uni.showToast({
						// 	title: '复制成功',
						// 	icon: 'success',
						// 	duration: 4000
						// });
					},
					fail: (err) => {
						console.log("复制失败原因===>",err);
						// 复制失败的回调
						uni.showToast({
							title: '复制失败：'+err,
							icon: 'none'
						});
					}
				});
			},
			
			//返回
			navig() {
				
				let pages = getCurrentPages();  //获取所有页面栈实例列表
				
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}else {
					uni.navigateBack();
				}
				
			},
			
			//我的形象
			async upCallback(scroll) {
				
				const result = await this.$http.post({
					url: this.$api.orderList,
					data: {
						uid: uni.getStorageSync('uid'),
						order_type: this.tabsNextId,//1 我购买的，2 我售出的
						type: this.tabsId, //type 1 高级形象 type ：2 标准形象 type 3声音，4 高级形象和 标准形象
						year: this.yeartime,
						month: this.monthtime,
						page: 1,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
					
					if (this.tabsId == 3) {
						for (let i = 0 ; i < this.list.length ; i++) {
							this.list[i].isPlay = 2;
						}
					}
					
				}
			},
			
			getId(obj) {
				this.tabsId = obj.id;
				this.list = [];
				// this.nextList = obj.list;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			getNextId(id) {
				this.tabsNextId = id;
				this.list = [];
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
		}
	}
</script>

<style lang="scss">
	
	.img-254 {
		position: absolute;
		z-index: 9;
		width: 160rpx;
		height: 160rpx;
		border-radius: 10rpx;
		left: 0;
		top: 0;
	}
	
	.img-266 {
		width: 160rpx;
		height: 160rpx;
		margin-right: 20rpx;
		border-radius: 10rpx;
	}
	
	.p-bo {
		border-bottom: 1px solid rgb(48, 48, 48);
	}
	
	.r-video {
		width: 140rpx;
		border-radius: 10rpx;
		position: absolute;
		z-index: 2;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
	
	.frame {
		position: relative;
		width: 140rpx;
		height: 249rpx;
		border-radius: 10rpx;
		margin-right: 20rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: block;
		/* 避免底部空白 */
	}
	
	.copy {
		width: 104rpx;
		text-align: center;
		background-color: #585858;
		color: #FFF;
		font-size: 26rpx;
		padding: 6rpx 0;
		border-radius: 100rpx;
		margin-left: auto;
	}
	
	.img-268 {
		width: 30rpx;
		height: 30rpx;
		margin-right: 14rpx;
	}
	
	.list-public {
		background-color: #232323;
		padding: 24rpx 20rpx;
	}
	
	.img-267 {
		width: 20rpx;
		height: 20rpx;
		margin-right: 30rpx;
		margin-top: 2rpx;
	}
	
	.a-tabs-2 {
		color: #B2B1B1;
		background-color: #232323;
	}
	
	.a-tabs-1 {
		color: #FFF;
		background-color: #1E6CEB;
	}
	
	.a-tabs {
		width: 150rpx;
		text-align: center;
		padding: 10rpx 0;
		border-radius: 10rpx;
		margin-left: 30rpx;
		font-size: 26rpx;
	}
	
	.color_9B9B9B {
		color: #B2B1B1;
		font-size: 34rpx;
		margin-bottom: 10rpx;
	}
	
	.color_4088FF {
		color: #FFF;
		font-size: 34rpx;
		font-weight: 600;
		letter-spacing: 0%;
		margin-bottom: 10rpx;
	}
	
	.c-line-2 {
		background: #000;
	}
	
	.c-line-1 {
		background: #FFF;
	}
	
	.c-line {
		width: 66rpx;
		height: 8rpx;
		border-radius: 100rpx;
	}
	
	.c-top {
		padding: 30rpx 0;
		border-bottom: 1px solid rgb(31, 31, 31);
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
	
</style>
