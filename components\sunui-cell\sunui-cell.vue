<template>
	<view class="sunui-cell" :hover-class="hover?'sunui-cell-hover':''" :hover-stay-time="time" :style="cellStyle">
		<slot></slot>
		<text class="sunuicell icon-youjiantou" v-show="arrow" :style="arrowStyle"></text>
	</view>
</template>

<script>
	export default {
		name: 'sunui-cell',
		props: {
			arrow: {
				type: Boolean,
				default: false
			},
			hover: {
				type: Boolean,
				default: false
			},
			time: {
				type: [Number, String],
				default: 140
			},
			cellStyle: {
				type: String
			},
			arrowStyle: {
				type: String
			}
		}
	}
</script>

<style lang="scss">
	@import url('./iconfont');

	.sunui-cell {
		position: relative;
		width: 100%;
		box-sizing: border-box;
		overflow: hidden;
		display: flex;
		align-items: center;

		&>.icon-youjiantou {
			font-weight: bold;
			position: absolute;
			right: 30rpx;
		}

		&-hover {
			background-color: #f5f5f5 !important;
			box-shadow: 0 0 4rpx #f5f5f5;
		}

	}
</style>
