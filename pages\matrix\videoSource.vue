<template>
	<view>
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit"
			@down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view v-if="item.id" class="display-a task-list" >
					<image @click="selGetObj(item)" class="img-346" :src="item.id == selId ? imgUrl+'384.png' : imgUrl+'345.png'"></image>
					<view class="display-a" @click="getVideoList(item)">
						<image class="img-306" :src="imgUrl+'306.png'"></image>
						<view style="width: 450rpx;">
							<view class="display-a margin-bottom_20rpx">
								<view style="max-width: 324rpx;" class="color_FFFFFF font-size_32rpx font-overflow">{{item.title}}</view>
							</view>
							<view class="color_A3A3A3 font-size_26rpx">{{item.create_time}}</view>
						</view>
						<view class="margin-left-auto color_A3A3A3 font-size_26rpx">{{item.video_count}}个</view>
						<image class="img-21" :src="imgUrl+'21.png'"></image>
					</view>
				</view>
			</block>
		</mescroll-body>
		
		<view style="height: 160rpx;"></view>
		<view class="next" @click="returnNav()">确认选择</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				selId: '', //选中的
				selObj: {
					id: '',
					title: '',
					video_count: '',
				},
				
				windowHeight: '',
				
				type: '', //1。D音 2。K手 3。视频号 4.小红薯 5.B站
				
			}
		},
		
		onLoad(options) {
			
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 60;
				},
			})
		},
		
		onShow() {
			let pagearr = getCurrentPages(); //获取应用页面栈
			let currentPage = pagearr[pagearr.length - 1]; //获取当前页面信息
			if (currentPage.options.type) {
				this.type = currentPage.options.type;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}else {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}
		},
		
		methods: {
			
			//返回上一页
			returnNav() {
				var pages = getCurrentPages();
				var prevPage = pages[pages.length - 2]; //上一个页面
				prevPage.$vm.otherVideoFun(this.selObj); //重点$vm
				uni.navigateBack();
			},
			
			//选中
			selGetObj(obj) {
				this.selId = obj.id;
				this.selObj = obj;
			},
			
			//剪辑视频列表
			getVideoList(obj) {
				
				uni.navigateTo({
					url: '/pages/edit/videoList?id='+obj.id+'&name='+obj.title+'&type=2'
				})
				
			},
			
			//
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.clipTaskList,
					data: {
						uid: uni.getStorageSync('uid'),
						page: scroll.num,
						is_publishe: this.type,
						status: 1, 
						psize: 12
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.next {
		position: fixed;
		bottom: 50rpx;
		width: 710rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118),rgb(0, 236, 255) 97.869%);
		color: #000;
		font-size: 30rpx;
		padding: 30rpx 0;
		z-index: 9;
	}
	
	.img-346 {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}
	
	.task-list {
		margin-bottom: 20rpx;
		padding-bottom: 20rpx;
		border-bottom: 1px solid rgb(41, 41, 41);
	}
	
	.img-21 {
		width: 24rpx;
		height: 24rpx;
		margin-left: 6rpx;
	}
	
	.img-306 {
		width: 90rpx;
		height: 90rpx;
		margin-right: 20rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
		padding: 26rpx 20rpx;
	}
	
</style>
