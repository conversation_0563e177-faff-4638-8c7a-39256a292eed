<template>
	<view>
		<view v-if="user.id">
			<!-- <view class="bg" :style="{'background-image': 'url('+imgUrl+'139.png'+')'}"> -->
				<!-- <view
					:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
					class="iconDizhssi">
					<image @click="navig()" class="img-34" :src="imgUrl + '153.png'"></image>
					<view class="font-size_32rpx" @click="navig()">
						会员中心
					</view>
				</view> -->
			<view style="background-color: #201B16;">
				<view style="height: 40rpx;"></view>
				<view class="display-a top-frame" :style="{'background-image': 'url('+imgUrl+'210.png'+')'}">
					<!-- <image class="img-30" :src="user.avatar"></image> -->
					<image class="img-30" :src="user.is_member == 1 ? imgUrl + '160.png' : imgUrl + '159.png'"></image>
					<view>
						<view class="margin-bottom_10rpx display-a">
							<view class="font-weight_bold">{{user.nickname}}</view>
							<image v-if="user.is_member == 1" class="img-31" :src="imgUrl + '31.png'"></image>
						</view>
						<view class="display-a">
							<view class="font-size_24rpx" style="color: #A45510;" v-if="type == 1">剩余点数: {{user.balance}}</view>
							<view class="font-size_24rpx" style="color: #A45510;" v-if="type == 2 && user.is_member == 1">到期时间: {{user.maturity_time}}</view>
							<view class="font-size_24rpx" style="color: #A45510;" v-if="type == 2 && user.is_member != 1">暂未开通会员</view>
							<!-- <view class="display-a" @click="getRecharge()" v-if="payFig.pay_open == 1">
								<view class="go-recharge">去充值</view>
								<image class="img-36" :src="imgUrl + '36.png'"></image>
							</view> -->
							<!-- <view class="display-a" @click="getCard()">
								<view class="go-recharge">卡密激活</view>
								<image class="img-36" :src="imgUrl + '155.png'"></image>
							</view> -->
						</view>
					</view>
					<!-- <image class="img-140" :src="imgUrl + '140.png'"></image> -->
					<view class="renew" @click="getCard()">卡密激活</view>
				</view>

			</view>

			<view class="m-frame">
				<block v-if="!isShow">
					<view class="display-a margin-bottom_30rpx">
						<view class="display-a-jc tabs-left" :class="type == 1 ? 'tabs-1' : 'tabs-2'" @click="getType(1)">
							<image class="img-141" :src="imgUrl + '141.png'"></image>
							<view class="font-size_32rpx" :class="type == 1 ? 'font-weight_bold' : ''">包量套餐</view>
						</view>
						<view class="display-a-jc tabs-right" :class="type == 2 ? 'tabs-1' : 'tabs-2'" @click="getType(2)">
							<image class="img-141" :src="imgUrl + '142.png'"></image>
							<view class="font-size_32rpx" :class="type == 2 ? 'font-weight_bold' : ''">VIP套餐</view>
						</view>
					</view>
				</block>
				<view v-else class="h_20rpx"></view>
				
				<view class="display-a padding_0_20rpx margin-bottom_50rpx">
					<view class="c-tips">i</view>
					<view style="color: #FF5427;font-size: 26rpx;">{{type == 1 ? '包量套餐点数用完为止' : '无限套餐时间内克隆无须扣点,时长按秒扣'}}</view>
					<view v-if="type == 2" class="display-a-jc km-activation" @click="getBuy()">
						<view class="color_666666 font-size_26rpx">开通记录</view>
						<image class="img-21" :src="imgUrl + '21.png'"></image>
					</view>
				</view>
				
				<!-- <view class="color_999999 font-size_24rpx margin-bottom_30rpx text-align_center">
					注:套餐可以多次重复开通,开通后套餐次数/点数叠加到您的账号中</view> -->
				<scroll-view :scroll-x="true" style="width: 720rpx; white-space: nowrap;">
					<view class="display-a">
						<block v-if="type == 1">
							<block v-for="(item,index) in pointList" :key="index">
								<view class="m-list" :class="memberObj.id == item.id ? 'm-list-2' : 'm-list-1'"
									@click="selData(item)">
									<view v-if="type == 2" class="m-recommend">{{item.day}}天</view>
									<view v-if="type == 1" class="m-recommend">送{{item.gift}}点</view>
									<view class="font-size_30rpx margin-bottom_20rpx font-overflow" :class="memberObj.id == item.id ? 'font-weight_bold' : ''">
										{{item.name}}</view>
									<view class="m-money" :class="memberObj.id == item.id ? 'color_FF0000' : 'color_FFFFFF'"><span class="font-size_26rpx">￥</span>{{item.money}}</view>
									<view v-if="type == 1" class="color_999999 font-size_26rpx margin-bottom_20rpx">{{item.point}}(点)</view>
									<view v-if="type == 2" class="m-price margin-bottom_20rpx">￥{{item.price}}</view>
									<view v-if="type == 1" class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">{{'总计:'+(Number(item.point)+Number(item.gift))+'(点)'}}</view>
									<block v-if="type == 2">
										<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">形象克隆:无限次</view>
										<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">声音克隆:无限次</view>
										<block v-if="cloneSet.voice_high_open == 1">
											<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">高保真声音:{{item.voice_twin_count}}次</view>
										</block>
										<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">视频合成:{{item.second_infinite == 1 ? '无限' : item.second?(item.second/60).toFixed(0):0}}分</view>
									</block>
								</view>
							</block>
						</block>
						<block v-if="type == 2">
							<block v-for="(item,index) in memberList" :key="index">
								<view class="m-list" :class="memberObj.id == item.id ? 'm-list-2' : 'm-list-1'"
									@click="selData(item)">
									<view v-if="type == 2" class="m-recommend">{{item.day}}天</view>
									<view v-if="type == 1" class="m-recommend">送{{item.gift}}点</view>
									<view class="font-size_30rpx margin-bottom_20rpx font-overflow" :class="memberObj.id == item.id ? 'font-weight_bold' : ''">
										{{item.name}}</view>
									<view class="m-money" :class="memberObj.id == item.id ? 'color_FF0000' : 'color_FFFFFF'"><span class="font-size_26rpx">￥</span>{{item.money}}</view>
									<view v-if="type == 1" class="color_999999 font-size_26rpx margin-bottom_20rpx">{{item.point}}(点)</view>
									<view v-if="type == 2" class="m-price margin-bottom_20rpx">￥{{item.price}}</view>
									<view v-if="type == 1" class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">{{'总计:'+(Number(item.point)+Number(item.gift))+'(点)'}}</view>
									<block v-if="type == 2">
										<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">形象克隆:无限次</view>
										<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">声音克隆:无限次</view>
										<block v-if="cloneSet.xunfei_sound_clone_swich == 1">
											<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">专业版声音:{{item.xunfei_sound_clone_words_number}}次</view>
											<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">专业版合成:{{item.xunfei_fidelity_words_number}}字</view>
										</block>
										<block v-if="cloneSet.voice_high_open == 1">
											<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">高保真声音:{{item.voice_twin_count}}次</view>
											<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">高保真合成:{{item.high_fidelity_words_number}}字</view>
										</block>
										<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">AI文案:{{item.ai_copywriting_times}}次</view>
										<view class="m-total" :class="memberObj.id == item.id ? 'm-total-2' : 'm-total-1'">视频合成:{{item.second_infinite == 1 ? '无限' : item.second?(item.second/60).toFixed(2):0}}分</view>
									</block>
								</view>
							</block>
						</block>
					</view>
				</scroll-view>
				<view class="m-desc display-a">
					<image class="img-152" :src="imgUrl + '152.png'"></image>
					<view class="font-size_26rpx">{{type == 1 ? pointSet.desc : memberSet.desc}}</view>
				</view>
				<block>
					<view class="display-a-jc margin-bottom_10rpx">
						<image class="img-38" :src="type == 1 ? imgUrl + '143.png' : imgUrl + '38.png'"></image>
					</view>
					<!-- <image class="img-145" :src="type == 1 ? imgUrl + '145.png' : imgUrl + '144.png'"></image> -->
					<view class="padding_20rpx">
						<rich-parser :html="type == 1 ? pointSet.explain : memberSet.rights" domain="https://6874-html-foe72-1259071903.tcb.qcloud.la"
							lazy-load ref="article" selectable show-with-animation use-anchor>
							<!-- 加载中... -->
						</rich-parser>
					</view>
				</block>
			</view>

			<view style="height: 180rpx;"></view>
			
			<block v-if="isOsName == 1">
				<view class="but" @click="but(1)">{{type == 1 ? '立即购买' : '立即开通'}}</view>
			</block>
			<block v-if="isOsName == 2">
				<view v-if="payFig.pay_open == 1" class="but" @click="but(1)">{{type == 1 ? '立即购买' : '立即开通'}}</view>
				<view v-else class="but" @click="getContactUs()">请联系客服</view>
			</block>
			
			

			<sunui-popup ref="rescuePop">
				<template v-slot:content>
					<view style="padding: 20rpx 20rpx 40rpx;">
						<view class="p-top">卡密激活</view>
						<view class="color_999999 text-align_center font-size_24rpx margin-bottom_50rpx">请输入卡密激活对应的套餐
						</view>
						<input type="text" v-model="card" class="p-input" placeholder="请输入卡密"
							placeholder-class="placeholder" />
						<view class="p-but" @click="but(2)">立即激活</view>
					</view>
				</template>
			</sunui-popup>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

				user: {},
				
				pointSet: {},
				memberSet: {},
				
				memberList: [],
				memberObj: {},
				
				pointList: [],

				heightSystemss: '',
				statusBarHeightss: '',

				imgUrl: this.$imgUrl,

				card: '', //卡密

				isWhether: true, //防止重复点击
				
				templateObj: {},
				
				type: '1', //1点数充值 2开通会员
				
				payFig: {},
				
				isOsName: '', //1安卓 2ios
				
				cloneSet: {},
				
				isShow: '', //

			}
		},

		onLoad(options) {
			if (options.mId) {
				this.type = 2;
				this.memberObj = {
					id: options.mId,
					name: options.mName,
					money: options.mMoney
				}
			}
			this.getSystemInfo();
			this.getMemberSet();
			this.getPointSet();
			this.getPointList();
			this.getCloneSet();
			this.getMemberList();
		},

		onShow() {
			this.getPayconfig();
			this.getTemplate();
			if (uni.getStorageSync('uid')) {
				this.userInfo();
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
							// uni.navigateTo({
							// 	url: '/pages/auth/auth?type=1'
							// })
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},

		methods: {
			
			//克隆设置
			async getCloneSet() {
				const result = await this.$http.post({
					url: this.$api.cloneSet
				});
				if (result.errno == 0) {
					this.cloneSet = result.data;
				}
			},
			
			
			getContactUs() {
				uni.navigateTo({
					url: '/pages/my/contactUs'
				})
			},
			
			//点数列表
			async getPointList() {
				const result = await this.$http.post({
					url: this.$api.pointList
				});
				if (result.errno == 0) {
					this.pointList = result.data;
					
					if (result.data.length == 0) {
						this.type = 2;
						this.isShow = 1;
					}
					
				}
			},
			
			//点数设置
			async getPointSet() {
				const result = await this.$http.post({
					url: this.$api.pointSet
				});
				if (result.errno == 0) {
					this.pointSet = result.data;
				}
			},
			
			getType(type) {
				this.memberObj = {};
				this.memberList = [];
				this.type = type;
				if (this.type == 1) {
					this.getPointList();
				}
				if (this.type == 2) {
					this.getMemberList();
				}
			},
			
			//支付设置
			async getPayconfig() {
				const result = await this.$http.post({
					url: this.$api.payconfig,
				});
				if (result.errno == 0) {
					this.payFig = result.data;
				}
			},
			
			//模板设置
			async getTemplate() {
				const result = await this.$http.post({
					url: this.$api.template,
				});
				if (result.errno == 0) {
					this.templateObj = result.data;
				}
			},
			
			//立即激活
			async pBut() {

				const result = await this.$http.post({
					url: this.$api.useCard,
					data: {
						uid: uni.getStorageSync('uid'),
						card: this.card
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.isWhether = true;
						this.$refs.rescuePop.close();
						this.userInfo();
					}, 2000);
				} else {
					this.$sun.toast(result.message, 'none');
					this.isWhether = true;
				}

			},

			//卡密激活
			getCard() {
				this.$refs.rescuePop.show({
					style: 'width:700rpx; height:auto;background-color: #FFFFFF;border-radius: 5px;',
					anim: 'center',
					shadeClose: false, //使用户不能点击其它关闭页面
					bottomClose: true
				});
			},

			//购买记录
			getBuy() {
				uni.navigateTo({
					url: '/pages/my/memberRecord'
				})
			},

			//充值点数
			getRecharge() {
				uni.navigateTo({
					url: '/pages/my/recharge'
				})
			},

			//购买会员
			but(type) {
				
				if (type == 1) {
					if (!this.memberObj.id) {
						this.$sun.toast("请选择套餐!", 'none');
						return;
					}
				}
				
				if (type == 2) {
					if (!this.card) {
						this.$sun.toast("请输入卡密!", 'none');
						return;
					}
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				uni.getSetting({
					withSubscriptions: true,
					success: (res) => {
						console.log(res.subscriptionsSetting);
						if (res.subscriptionsSetting.mainSwitch == false) {
							if (type == 1) {
								this.save();
							}
							if (type == 2) {
								this.pBut();
							}
						} else {
							// 获取下发权限
							uni.requestSubscribeMessage({
								tmplIds: [this.templateObj.recharge_success_template,this.templateObj.open_member_template], //此处写在后台获取的模板ID，可以写多个模板ID，看自己的需求
								success: async (data) => {
									if (data[this.templateObj.recharge_success_template] == 'accept') { //accept--用户同意 reject--用户拒绝 ban--微信后台封禁,可不管
										if (type == 1) {
											this.save();
										}
										if (type == 2) {
											this.pBut();
										}
									} else {
										uni.showModal({
											title: '温馨提示',
											content: '您已拒绝授权，将无法在微信中收到通知！',
											showCancel: false,
											success: res => {
												if (res.confirm) {
													// 这里可以写自己的逻辑
													if (type == 1) {
														this.save();
													}
													if (type == 2) {
														this.pBut();
													}
													console.log('拒绝授权', data);
												}
											}
										})
									}
								},
								fail: (err) => {
									if (type == 1) {
										this.save();
									}
									if (type == 2) {
										this.pBut();
									}
									console.log('失败', err);
								},
								complete: (result) => {

									console.log('完成', result);
								}
							});
						}
					}
				});

			},
			
			async save() {
				const result = await this.$http.post({
					url: this.type == 1 ? this.$api.addPointLog : this.$api.addMemberLog,
					data: {
						uid: uni.getStorageSync('uid'),
						member_id: this.type == 2 ? this.memberObj.id : '',
						point_id: this.type == 1 ? this.memberObj.id : '',
					}
				});
				if (result.errno == 0) {
					this.wxPay(result.data)
				} else {
					this.$sun.toast(result.message, 'none');
					this.isWhether = true;
				}
			},

			/*  微信支付  */
			async wxPay(log_no) {
				const result = await this.$http.post({
					url: this.$api.pay,
					data: {
						openid: uni.getStorageSync('openid'),
						price: this.memberObj.money,
						log_no: log_no,
						name: this.memberObj.name
					}
				});
				if (result.errno == 0) {
					uni.requestPayment({
						provider: 'wxpay',
						timeStamp: result.data.timeStamp,
						nonceStr: result.data.nonceStr,
						package: result.data.package,
						signType: result.data.signType,
						paySign: result.data.paySign,
						success: async (res) => {
							this.$sun.toast("支付成功");
							setTimeout(() => {
								this.isWhether = true;
								uni.navigateBack();
							}, 2000);
						},
						fail: (err) => {
							this.isWhether = true;
							this.$sun.toast("取消支付", 'error');
						}
					});
				} else {
					this.isWhether = true;
					if (result.errno == -1) {
						this.$sun.toast(result.message, 'none');
						return;
					}
					if (result.return_code == 'FAIL') {
						uni.showModal({
							title: '支付配置错误',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {

								}
							}
						})
					} else {
						uni.showModal({
							title: '提示',
							content: result.return_msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {

								}
							}
						})
					}
				}
			},

			//选中会员套餐
			selData(item) {
				
				this.memberObj = item;
			},

			//会员列表
			async getMemberList() {
				const result = await this.$http.post({
					url: this.$api.memberList,
					data: {
						page: 1,
						psize: 100
					}
				});
				if (result.errno == 0) {
					this.memberList = result.data.list;
					if (result.data.list.length == 0) {
						this.type = 1;
						this.isShow = 1;
					}
				}
			},

			//会员设置
			async getMemberSet() {
				const result = await this.$http.post({
					url: this.$api.memberSet
				});
				if (result.errno == 0) {
					this.memberSet = result.data;
				}
			},

			//用户信息
			async userInfo() {
				const result = await this.$http.post({
					url: this.$api.userInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.user = result.data;
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						if (res.osName == 'ios' || res.osName == 'macos') {
							this.isOsName = 2;
						}else {
							this.isOsName = 1;
						}
						console.log("isOsName==>",this.isOsName);
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	
	.renew {
		width: 150rpx;
		text-align: center;
		background-color: #000000;
		border-radius: 100rpx;
		padding: 14rpx 0;
		color: #FDD6A8;
		margin-left: auto;
		font-size: 26rpx;
	}
	
	.img-145 {
		width: 710rpx;
		height: 226rpx;
		margin: 0 20rpx;
	}
	
	.img-152 {
		width: 30rpx;
		height: 30rpx;
		margin-right: 6rpx;
	}
	
	.m-total-2 {
		background-color: #fff;
		color: #644747;
	}
	
	.m-total-1 {
		background-color: #2C2C2C;
		color: #999999;
	}
	
	.m-total {
		width: 220rpx;
		text-align: center;
		// display: inline-table;
		font-size: 22rpx;
		padding: 6rpx 0;
		border-radius: 100rpx;
		margin: 10rpx 10rpx 0;
	}
	
	.m-price {
		font-size: 26rpx;
		color: #999;
		text-decoration: line-through;
	}
	
	.c-tips {
		width: 30rpx;
		line-height: 30rpx;
		height: 30rpx;
		text-align: center;
		background-color: #FF5427;
		border-radius: 100rpx;
		margin-right: 14rpx;
		font-size: 24rpx;
		color: #FFF;
	}
	
	.tabs-2 {
		background-color: #000;
		color: #ADADAD;
	}
	
	.tabs-1 {
		background-color: #201B16;
		color: #FFF;
	}
	
	.tabs-right {
		width: 376rpx;
		border-radius: 0 20rpx 0 20rpx;
		padding: 30rpx 0;
	}
	
	.tabs-left {
		width: 374rpx;
		border-radius: 20rpx 0 20rpx 0;
		padding: 30rpx 0;
	}
	
	.img-141 {
		width: 50rpx;
		height: 50rpx;
		margin-right: 14rpx;
	}
	
	.img-140 {
		width: 120rpx;
		height: 120rpx;
		margin-left: auto;
		margin-top: -100rpx;
	}
	
	.top-frame {
		width: 710rpx;
		height: 184rpx;
		margin: 0 20rpx;
		border-radius: 10rpx;
		box-shadow: 0px 8rpx 8rpx 0px rgba(0, 0, 0, 0.07);
		background-repeat: no-repeat;
		background-size: contain;
		// backdrop-filter: blur(8rpx);
		// background: rgb(255, 255, 255);
		// opacity: 0.53;
		padding: 40rpx 20rpx;
	}
	
	.p-input {
		width: 560rpx;
		background-color: #F4F3F3;
		padding: 20rpx;
		margin: 0 20rpx 60rpx;
	}

	.p-top {
		color: #535353;
		font-size: 32rpx;
		text-align: center;
		margin-bottom: 20rpx;
	}

	.p-but {
		width: 600rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(262.74deg, rgb(30, 108, 235) 1.017%, rgb(106, 104, 247) 103.119%);
		color: #FFF;
		font-size: 30rpx;
		margin-left: 30rpx;
		padding: 24rpx 0;
	}

	.but {
		width: 700rpx;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(254, 222, 135), rgb(244, 201, 96) 100%);
		text-align: center;
		padding: 28rpx 0;
		color: #535353;
		font-size: 30rpx;
		font-weight: bold;
		position: fixed;
		bottom: 60rpx;
		left: 25rpx;
		z-index: 9;
	}

	.img-38 {
		width: 400rpx;
		height: 60rpx;
	}

	.m-desc {
		color: #999;
		padding: 0 24rpx;
		font-size: 26rpx;
		margin-bottom: 34rpx;
	}

	.m-recommend {
		display: inline-block;
		border-radius: 20rpx 0 20rpx 0;
		background: rgb(255, 0, 0);
		// width: 112rpx;
		// text-align: center;
		font-size: 24rpx;
		color: #FFF;
		padding: 2rpx 20rpx;
		position: absolute;
		top: -20rpx;
		left: 0;
	}

	.m-money {
		font-size: 34rpx;
		font-weight: bold;
		margin-bottom: 14rpx;
	}

	.m-count {
		width: 214rpx;
		color: #999;
		font-size: 24rpx;
	}

	.m-list-2 {
		border: 1px solid rgb(151, 151, 151);
		background: #1A1A1A;
	}

	.m-list-1 {
		border: 1px solid #1A1A1A;
		background-color: #1A1A1A;
	}

	.m-list {
		width: 240rpx !important;
		border-radius: 20rpx;
		margin: 20rpx 0 30rpx 20rpx;
		padding: 40rpx 0 20rpx;
		position: relative;
		z-index: 1;
		display: inline-table;
		text-align: center;
	}

	.img-21 {
		width: 24rpx;
		height: 24rpx;
		margin-left: 4rpx;
	}

	.title-css {
		width: 12rpx;
		height: 42rpx;
		background-color: #DBB55C;
		margin-right: 20rpx;
	}

	.m-frame {
		background-color: #000;
		border-radius: 20rpx 20rpx 0 0;
		width: 750rpx;
		margin-top: -30rpx;
		padding: 0 0 20rpx;
		color: #FFF;
		// position: relative;
		// z-index: 9;
	}

	.km-activation {
		width: 150rpx;
		// background-color: #FFFFFF;
		color: #999999;
		border-radius: 100rpx;
		padding: 8rpx 0;
		margin-left: auto;
	}

	.img-31 {
		width: 108rpx;
		height: 28rpx;
		margin-left: 20rpx;
	}

	.go-recharge {
		color: #0084FF;
		font-size: 24rpx;
		margin-left: 20rpx;
	}

	.img-36 {
		width: 30rpx;
		height: 30rpx;
	}

	.img-32 {
		width: 28rpx;
		height: 24rpx;
		margin-right: 6rpx;
	}

	.img-30 {
		width: 100rpx;
		height: 100rpx;
		border-radius: 100rpx;
		margin-right: 20rpx;
	}

	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.bg {
		width: 750rpx;
		height: 456rpx;
		background-repeat: no-repeat;
		background-size: cover;
		// background: #0C134E;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #000;
		// font-size: 30rpx;
		// font-weight: bold;
		display: flex;
		align-items: center;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
</style>