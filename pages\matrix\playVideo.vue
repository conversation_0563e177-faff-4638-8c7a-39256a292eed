<template>
	<view>
		<video :src="url" :autoplay="true" :style="'height:'+windowHeight+'rpx;'"></video>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				url: '',
				windowHeight: '',
			}
		},
		
		onLoad(options) {
			uni.getSystemInfo({
				success: res => {
					this.windowHeight = res.windowHeight * 2;
				}
			})
			if (options.url) {
				this.url = options.url;
			}
		},
		
		onShow() {
			
		},
		
		methods: {
			
		}
	}
</script>

<style lang="scss">
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}
	
</style>
