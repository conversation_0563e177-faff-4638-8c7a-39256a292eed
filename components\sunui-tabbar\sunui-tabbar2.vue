<template>
	<view class="sunui-tabbar" :class="[fixed ? 'fixed' : '']">
		<view class="tablist flexbox flex_alignc sunui-bottom" :style="[{ 'background-color': backgroundColor ? backgroundColor : '' }]">
			<block v-for="(item, index) in tabList" :key="index">
				<view class="navigator flex-column-center" :class="current == index ? 'on' : ''" @tap="switchTab(index,item)">
					<block v-if="item.name == '添加账户'">
						<image class="icon-fen" :src="item.icoActive"></image>
					</block>
					<block v-else>
						<image style="width: 50rpx;height: 50rpx;margin-top: 16rpx;" v-if="current == index" :src="item.icoActive"></image>
						<image style="width: 50rpx;height: 50rpx;margin-top: 16rpx;" v-if="current != index" :src="item.ico"></image>
					</block>
					<view class="text name-text" :style="[current == index ? { color: tintColor } : { color: color }]">{{ item.name }}</view>
				</view>
			</block>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// tabList: [],
				tabList: [
					{ico: this.$imgUrl+'tabbar/23.png',icoActive: this.$imgUrl+'tabbar/24.png',name: '矩阵首页',url: '/pages/matrix/matrix'},
					{ico: this.$imgUrl+'tabbar/27.png',icoActive: this.$imgUrl+'tabbar/28.png',name: '账号列表',url: '/pages/matrix/account'},
					{ico: this.$imgUrl+'tabbar/31.png',icoActive: this.$imgUrl+'tabbar/31.png',name: '添加账户',url: '/pages/matrix/authorize'},
					{ico: this.$imgUrl+'tabbar/25.png',icoActive: this.$imgUrl+'tabbar/26.png',name: '分组管理',url: '/pages/matrix/grouping'},
					{ico: this.$imgUrl+'tabbar/29.png',icoActive: this.$imgUrl+'tabbar/30.png',name: '发布管理',url: '/pages/matrix/release'},
				],
				platform: '',
				
			};
		},
		name: 'sunui-tabbar',
		props: {
			current: {
				type: [Number, String],
				default: 0
			},
			backgroundColor: {
				type: String,
				default: '#1B1B1B'
			},
			color: {
				type: String,
				default: '#FFF'
			},
			tintColor: {
				type: String,
				default: '#E496FD'
			},
			fixed: {
				type: [Boolean, String],
				default: false
			}
		},
		created() {
			uni.getSystemInfo({
			    success: (res) => {
					this.platform = res.platform;
			    }
			});
			//首页底部菜单
			// this.indexMenu();
		},
		methods: {
			switchTab(index, obj) {
				this.$emit('click', index);
				uni.redirectTo({
					url: obj.url
				});
				// if (obj.type == 1) {
				// 	uni.redirectTo({
				// 		url: obj.url + '?index=' + index
				// 	});
				// }
				// if (obj.type == 2) {
				// 	if (obj.appid){
				// 		uni.navigateToMiniProgram({
				// 			appId: obj.appid,
				// 			link: obj.url
				// 		})
				// 	}else {
				// 		uni.redirectTo({
				// 			url: obj.url + '?index=' + index
				// 		});
				// 	}
				// }
				
			},
			// 首页底部菜单
			async indexMenu() {
				const result = await this.$http.post({
					url: this.$api.indexBottom
				});
				if (result.errno == 0) {
					this.tabList = result.data;
					// for (let i = 0; i < result.data.length; i++) {
					// 	if (result.data[i].url == '/wjyk_recycle/pages/my/my') {
					// 		uni.setStorageSync('tabIndex', i)
					// 	}
					// }
					// console.log('首页底部菜单', result, this.tabList);
				}
			}
		}
	};
</script>
<style scoped>
	/* view {
		line-height: 1.5;
	} */

	.icon-fen {
		width: 80rpx;
		height: 80rpx;
		border-radius: 100rpx;
		margin-top: -20rpx;
		margin-bottom: 6rpx;
	}
	
	.name-text {
		/* width: 120rpx; */
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		text-align: center;
	}
	
	.flexbox {
		display: flex;
	}
	
	.sunui-position {
		position: relative;
	}
	
	.sunui-badge {
		background-color: #ff3e3e;
		border-radius: 50upx;
		box-sizing: border-box;
		color: #fff;
		font-size: 12px;
		font-family: arial;
		padding: 6upx 12upx;
		line-height: 1.08;
	}
	
	.sunui-badge_dot {
		border-radius: 100%;
		font-size: 0;
		overflow: hidden;
		padding: 0;
		height: 18upx;
		width: 18upx;
	}
	
	.sunui-bottom,
	.sunui-bottombar {
		position: relative;
	}
	
	.sunui-bottom:before {
		content: '';
		background: #1B1B1B !important;
		height: 1rpx;
		width: 100%;
		position: absolute;
		left: 0;
		top: 0;
		transform: scaleY(0.5);
		transform-origin: 0 0;
	}
	
	.sunui-bottombar:after {
		content: '';
		background: #1B1B1B !important;
		height: 1rpx;
		width: 100%;
		position: absolute;
		left: 0;
		bottom: 0;
		transform: scaleY(0.5);
		transform-origin: 0 100%;
	}
	
	.sunui-tabbar {
		display: flex;
		width: 100%;
	}
	
	.sunui-tabbar .tablist {
		background-color: #1B1B1B;
		height: 144upx;
		width: 750upx;
		position: relative;
		z-index: 900;
		padding-bottom: 20rpx;
	}
	
	.sunui-tabbar .tablist.sunui-bottom:before {
		background: #bbb;
	}
	
	.sunui-tabbar.fixed {
		padding-top: 150upx;
	}
	
	.sunui-tabbar.fixed .tablist {
		position: fixed;
		bottom: 0;
		left: 0;
	}
	
	.sunui-tabbar .tablist .navigator {
		flex: 1;
		text-align: center;
		height: 144upx;
	}
	
	.sunui-tabbar .tablist .icon {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto;
		margin-top: 10upx;
		height: 50upx;
		width: 50upx;
		position: relative;
	}
	
	.sunui-tabbar .tablist .icon .iconfont {
		color: #D9D9D9;
		font-size: 44upx;
	}
	
	.sunui-tabbar .tablist .text {
		color: #D9D9D9;
		font-size: 28upx;
	}
	
	.sunui-tabbar .tablist .navigator.on .icon .iconfont {
		color: #ff592e;
	}
	
	.sunui-tabbar .tablist .navigator.on .text {
		color: #ff592e;
	}
	
	.sunui-tabbar .tablist .sunui-badge {
		position: absolute;
		top: -3upx;
		left: 32upx;
	}
	
	.sunui-tabbar .tablist .sunui-badge_dot {
		left: 36upx;
	}
</style>
