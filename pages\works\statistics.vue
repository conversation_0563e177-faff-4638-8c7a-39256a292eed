<template>
	<view>
		<view class="h_20rpx"></view>
		<view class="display-a-js padding_20rpx">
			<view class="s-top">
				<view class="display-a margin-bottom_30rpx">
					<view class="line-d"></view>
					<view class="color_FFFFFF font-size_30rpx">D音累计数据</view>
				</view>
				<view class="display-a margin-bottom_30rpx">
					<view class="width_176rpx-center">
						<view class="font-size_30rpx color_FFFFFF">{{obj.dy_like}}</view>
						<view class="color_B7B7B7">点赞数</view>
					</view>
					<view class="width_176rpx-center">
						<view class="font-size_30rpx color_FFFFFF">{{obj.dy_comment}}</view>
						<view class="color_B7B7B7">评论数</view>
					</view>
				</view>
				<view class="display-a">
					<view class="width_176rpx-center">
						<view class="font-size_30rpx color_FFFFFF">{{obj.dy_play}}</view>
						<view class="color_B7B7B7">播放数</view>
					</view>
					<view class="width_176rpx-center">
						<view class="font-size_30rpx color_FFFFFF">{{obj.dy_share}}</view>
						<view class="color_B7B7B7">分享数</view>
					</view>
				</view>
			</view>
			<view class="s-top">
				<view class="display-a margin-bottom_30rpx">
					<view class="line-d" style="background-color: #FF9E00;"></view>
					<view class="color_FFFFFF font-size_30rpx">K手累计数据</view>
				</view>
				<view class="display-a margin-bottom_30rpx">
					<view class="width_176rpx-center">
						<view class="font-size_30rpx color_FFFFFF">{{obj.ks_like}}</view>
						<view class="color_B7B7B7">点赞数</view>
					</view>
					<view class="width_176rpx-center">
						<view class="font-size_30rpx color_FFFFFF">{{obj.ks_comment}}</view>
						<view class="color_B7B7B7">评论数</view>
					</view>
				</view>
				<view class="display-a">
					<view class="width_176rpx-center">
						<view class="font-size_30rpx color_FFFFFF">{{obj.ks_play}}</view>
						<view class="color_B7B7B7">播放数</view>
					</view>
					<!-- <view class="width_176rpx-center">
						<view class="font-size_30rpx color_FFFFFF">{{obj.ks_share}}</view>
						<view class="color_B7B7B7">分享数</view>
					</view> -->
				</view>
			</view>
		</view>

		<view class="list-public">
			<view class="display-a padding_0_20rpx margin-bottom_60rpx">
				<view class="display-a">
					<view class="line-d"></view>
					<view class="color_FFFFFF font-size_30rpx">转发视频统计</view>
				</view>
				<!-- <view class="tb-title"></view> -->
				<view class="display-a margin-left-auto">
					<picker mode="date" fields="month" :value="tbBirth" :start="startDate" :end="endDate"
						@change="bindTbChange">
						<input type="text" style="width: 120rpx;color: #FFF;" disabled placeholder="日期筛选"
							v-model="tbBirth" placeholder-class="font-size_28rpx" />
					</picker>
					<image class="img-99" :src="imgUrl+'187.png'"></image>
				</view>
			</view>
			<view class="charts-box" v-show="isFlag">
				<qiun-data-charts type="column" background="rgba(16, 20, 50,0)" :opts="opts" :chartData="chartData" :ontouch="true" />
			</view>
		</view>

		<view class="list-public">
			<view class="display-a margin-bottom_50rpx padding_0_20rpx">
				<view class="line-d"></view>
				<view class="color_FFFFFF font-size_30rpx">D音数据</view>
				<view class="display-a margin-left-auto">
					<picker mode="date" fields="month" :value="dyBirth" :start="startDate" :end="endDate"
						@change="bindDyChange">
						<input type="text" style="width: 120rpx;color: #FFF;" disabled placeholder="日期筛选"
							v-model="dyBirth" placeholder-class="font-size_28rpx" />
					</picker>
					<image class="img-99" :src="imgUrl+'187.png'"></image>
				</view>
			</view>
			<view class="display-a">
				<view class="width_176rpx-center">
					<image class="img-189" :src="imgUrl+'190.png'"></image>
					<view class="color_FFFFFF font-size_30rpx margin-bottom_10rpx">{{dyObj.like}}</view>
					<view class="color_B7B7B7">点赞数</view>
				</view>
				<view class="width_176rpx-center">
					<image class="img-189" :src="imgUrl+'194.png'"></image>
					<view class="color_FFFFFF font-size_30rpx margin-bottom_10rpx">{{dyObj.comment}}</view>
					<view class="color_B7B7B7">评论数</view>
				</view>
				<view class="width_176rpx-center">
					<image class="img-189" :src="imgUrl+'189.png'"></image>
					<view class="color_FFFFFF font-size_30rpx margin-bottom_10rpx">{{dyObj.play}}</view>
					<view class="color_B7B7B7">播放数</view>
				</view>
				<view class="width_176rpx-center">
					<image class="img-189" :src="imgUrl+'195.png'"></image>
					<view class="color_FFFFFF font-size_30rpx margin-bottom_10rpx">{{dyObj.share}}</view>
					<view class="color_B7B7B7">分享数</view>
				</view>
			</view>
		</view>

		<view class="list-public">
			<view class="display-a margin-bottom_50rpx padding_0_20rpx">
				<view class="line-d" style="background-color: #FF9E00;"></view>
				<view class="color_FFFFFF font-size_30rpx">K手数据</view>
				<view class="display-a margin-left-auto">
					<picker mode="date" fields="month" :value="ksBirth" :start="startDate" :end="endDate"
						@change="bindKsChange">
						<input type="text" style="width: 120rpx;color: #FFF;" disabled placeholder="日期筛选"
							v-model="ksBirth" placeholder-class="font-size_28rpx" />
					</picker>
					<image class="img-99" :src="imgUrl+'187.png'"></image>
				</view>
			</view>
			<view class="display-a">
				<view class="width_236rpx-center">
					<image class="img-189" :src="imgUrl+'193.png'"></image>
					<view class="color_FFFFFF font-size_30rpx margin-bottom_10rpx">{{ksObj.like}}</view>
					<view class="color_B7B7B7">点赞数</view>
				</view>
				<view class="width_236rpx-center">
					<image class="img-189" :src="imgUrl+'191.png'"></image>
					<view class="color_FFFFFF font-size_30rpx margin-bottom_10rpx">{{ksObj.comment}}</view>
					<view class="color_B7B7B7">评论数</view>
				</view>
				<view class="width_236rpx-center">
					<image class="img-189" :src="imgUrl+'192.png'"></image>
					<view class="color_FFFFFF font-size_30rpx margin-bottom_10rpx">{{ksObj.play}}</view>
					<view class="color_B7B7B7">播放数</view>
				</view>
			</view>
		</view>
		
		<view class="h_20rpx"></view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {

				imgUrl: this.$imgUrl,

				obj: {},

				dyObj: {},

				ksObj: {},

				dyBirth: '',
				ksBirth: '',
				tbBirth: '',

				yeartime: '',
				monthtime: '',

				chartData: {},
				//您可以通过修改 config-ucharts.js 文件中下标为 ['column'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 15, 0, 5],
					background: '#101432',
					touchMoveLimit: 24,
					enableScroll: true,
					legend: {},
					xAxis: {
						disableGrid: true,
						axisLine: false,
						scrollShow: false,
						// 单屏数据密度   一行显示几个数据
						itemCount: 5,
					
					},
					
					yAxis: {
						disableGrid: true,
						data: [{
							axisLine: false
						}, ],
					},
					extra: {
						// 柱状图
						column: {
							type: "group",
							width: 8,
							seriesGap: 10,
							linearType: 'custom',
							barBorderCircle: true,
							customColor: ['#3A8EEF', '#21F3E2'],
							colorStop: 1,
							activeBgColor: "#101432",
							activeBgOpacity: 1
						},
					}
				},

				statistics: {},

				isFlag: false,
				
			}
		},

		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},

		onLoad() {

		},

		onShow() {

			if (uni.getStorageSync('uid')) {
				var myDate = new Date();
				this.yeartime = myDate.getFullYear();
				this.monthtime = myDate.getMonth() + 1;
				if (this.monthtime.toString().length == 1) {
					this.monthtime = '0' + this.monthtime;
				}
				this.dyBirth = this.yeartime + '-' + this.monthtime;
				this.ksBirth = this.yeartime + '-' + this.monthtime;
				this.tbBirth = this.yeartime + '-' + this.monthtime;
				this.getDataStatis();
				this.getDyRepostData();
				this.getKsRepostData();
				this.getAllStatis();
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},

		methods: {
			
			//图表
			async getAllStatis() {
				const result = await this.$http.post({
					url: this.$api.workClipStatis,
					data: {
						uid: uni.getStorageSync('uid'),
						year: this.yeartime,
						month: this.monthtime
					}
				});
				if (result.errno == 0) {

					this.statistics = result.data;

					let res = {

						categories: result.data.date,

						series: [{
								name: "D音",
								data: result.data.dy,
								color: '#3A8EEF',
								textColor: '#3A8EEF',
								textSize: 11,
								textOffset: -1,
							},
							{
								name: "K手",
								data: result.data.ks,
								color: '#21F3E2',
								textColor: '#21F3E2',
								textSize: 11,
								textOffset: -1,
							}
						]
					};

					this.chartData = JSON.parse(JSON.stringify(res));

					this.isFlag = true;
				}
			},

			/*  日期选择  */
			bindDyChange(e) {
				this.dyBirth = e.target.value;
				this.yeartime = e.target.value.split('-')[0];
				this.monthtime = e.target.value.split('-')[1];
				this.getDyRepostData();
			},
			/*  日期选择  */
			bindKsChange(e) {
				this.ksBirth = e.target.value;
				this.yeartime = e.target.value.split('-')[0];
				this.monthtime = e.target.value.split('-')[1];
				this.getKsRepostData();
			},
			/*  日期选择  */
			bindTbChange(e) {
				this.tbBirth = e.target.value;
				this.yeartime = e.target.value.split('-')[0];
				this.monthtime = e.target.value.split('-')[1];
				this.getAllStatis();
			},

			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 100;
				} else if (type === 'end') {
					year = year;
				}
				month = month > 9 ? month : '0' + month;;
				// day = day > 9 ? day : '0' + day;
				return `${year}-${month}`;
			},

			//抖音转发数据
			async getDyRepostData() {
				const result = await this.$http.post({
					url: this.$api.repostClipData,
					data: {
						uid: uni.getStorageSync('uid'),
						type: 1,
						year: this.yeartime,
						month: this.monthtime
					}
				});
				if (result.errno == 0) {
					this.dyObj = result.data;
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},

			//快手转发数据
			async getKsRepostData() {
				const result = await this.$http.post({
					url: this.$api.repostClipData,
					data: {
						uid: uni.getStorageSync('uid'),
						type: 2,
						year: this.yeartime,
						month: this.monthtime
					}
				});
				if (result.errno == 0) {
					this.ksObj = result.data;
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},

			//汇总统计
			async getDataStatis() {
				const result = await this.$http.post({
					url: this.$api.dataClipStatis,
					data: {
						uid: uni.getStorageSync('uid'),
						video_id: '',
						type: ''
					}
				});
				if (result.errno == 0) {
					this.obj = result.data;
				} else {
					this.$sun.toast(result.message, 'none');
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

		}
	}
</script>

<style lang="scss">
	
	/deep/ .chartsview.data-v-fe947b98 {
		background: rgba(16, 20, 50,0) !important;
	}

	
	.tb-title {
		color: #FFF;
		font-style: 30rpx;
	}
	
	.charts-box {
		width: 670rpx;
		height: 400rpx;
		background-color: #101432;
		margin: 0 20rpx;
	}

	.tb-frame {
		width: 14rpx;
		height: 14rpx;
		margin-right: 10rpx;
	}

	.img-99 {
		width: 20rpx;
		height: 20rpx;
		margin-top: 2rpx;
	}

	.img-189 {
		width: 66rpx;
		height: 66rpx;
		margin-bottom: 6rpx;
	}

	.list-public {
		background-color: #101432;
		padding: 20rpx 0;
	}

	.width_176rpx-center {
		width: 172rpx;
	}

	.line-d {
		width: 6rpx;
		height: 18rpx;
		background-color: #00B9FF;
		border-radius: 4rpx;
		margin-right: 10rpx;
	}

	.s-top {
		width: 346rpx;
		background-color: #101432;
		border-radius: 10rpx;
		padding: 20rpx 0;
	}

	page {
		background-color: #0B1126;
		border: none;
	}
</style>