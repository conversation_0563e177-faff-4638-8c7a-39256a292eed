<template>
	<view>
		<view class="h_20rpx"></view>
		<view class="edit-top" v-if="selLabel != 3">
			<view class="display-a">
				<view class="width_236rpx-center">
					<view class="color_00DCFF">{{obj.exposure_count?obj.exposure_count:0}}</view>
					<view class="color_c6c6c6 font-size_26rpx">曝光量</view>
				</view>
				<view class="width_236rpx-center">
					<view class="color_00DCFF">{{obj.like_count?obj.like_count:0}}</view>
					<view class="color_c6c6c6 font-size_26rpx">点赞数</view>
				</view>
				<view class="width_236rpx-center">
					<view class="color_00DCFF">{{obj.comment?obj.comment:0}}
					</view>
					<view class="color_c6c6c6 font-size_26rpx">评论数</view>
				</view>
			</view>
		</view>

		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view style="padding: 20rpx;">
						<view class="display-a margin-bottom_10rpx">
							<image class="head" :src="item.avatar"></image>
							<view style="width: 440rpx;">
								<view class="font-size_32rpx margin-bottom_10rpx">{{item.account_name}}</view>
								<view :class="item.account_group_name ? 'color_00FFCA' : 'color_999999'">所在分组:
									{{item.account_group_name?item.account_group_name:'--'}}</view>
							</view>
							<view class="empower" :style="isEmpower(item) ? '' : 'background: #F00;'">
								{{isEmpower(item) ? '授权中' : '已失效'}}
							</view>
						</view>
					</view>
					<view class="display-a padding-bottom_20rpx ">
						<view class="width_236rpx-center">
							<view class="font-size_32rpx margin-bottom_10rpx">{{item.exposure_count?item.exposure_count:0}}</view>
							<view class="color_c6c6c6">曝光量</view>
						</view>
						<view class="width_236rpx-center">
							<view class="font-size_32rpx margin-bottom_10rpx">{{item.like_count?item.like_count:0}}</view>
							<view class="color_c6c6c6">点赞数</view>
						</view>
						<view class="width_236rpx-center">
							<view class="font-size_32rpx margin-bottom_10rpx">{{item.comment?item.comment:0}}</view>
							<view class="color_c6c6c6">评论数</view>
						</view>
					</view>
					
					<view class="display-a-js edit-bott">
						<view class="color_999999 font-size_26rpx">到期时间:{{item.expires_in}}</view>
						<view class="display-a">
							<picker mode="selector" @change="bindPickerChange2" :data-userId="item.id" :range="groupList"
								:range-key="'name'">
								<view class="display-a">
									<image class="img-395" :src="imgUrl+'395.png'"></image>
									<view class="color_30F99F font-size_26rpx margin-right_40rpx">换组</view>
								</view>
							</picker>
							
							<image @click="getDel(item.id)" class="img-395" :src="imgUrl+'396.png'"></image>
							<view @click="getDel(item.id)" class="color_FF0000 font-size_26rpx">删除</view>
						</view>
					</view>
				</view>
			</block>
		</mescroll-body>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				obj: {},

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				selLabel: '', //1.D音 2.K手 3.视频号 4.小红薯 5.B站

				list: [],

				windowHeight: '',

				groupList: [], //分组列表

			}
		},

		computed: {

			// 是否已授权
			isEmpower() {
				return function(row) { //使用函数返回
					let isEmpower = false;
					// true,已授权 ,为false，则为已失效
					// row.type  1D音2K手
					isEmpower = new Date(this.msToDate(new Date()).hasTime.replace(/-/g, "\/")) < new Date(this
						.date(row.expires_in).replace(/-/g, "\/"))
					return isEmpower;
				}
			},
			
		},

		onLoad(options) {
			//获取系统信息
			this.selLabel = options.selLabel;
			uni.getSystemInfo({
				success: res => {
					if (this.selLabel != 3) {
						this.windowHeight = res.windowHeight * 2 - 190;
					}else {
						this.windowHeight = res.windowHeight * 2 - 24;
					}
				}
			})
			this.obj = JSON.parse(options.obj);
			if (this.obj.id) {
				this.$sun.title("编辑" + this.obj.name);
				this.groupId = this.obj.id;
				this.getGroup();
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}
		},

		onShow() {

		},

		methods: {
			
			/*  删除  */
			getDel(id) {
				
				uni.showModal({
					title: '提示',
					content: '确认删除该账户?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.delAccount,
								data: {
									id: [id],
									uid: uni.getStorageSync("uid"),
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			//账号分组选择
			bindPickerChange2(e) {
				let group_id = this.groupList[e.detail.value].id;
				let user_id = e.target.dataset.userid;
				this.updataGroup(group_id,user_id);
			},

			//换分组
			async updataGroup(group_id,id) {
				const result = await this.$http.post({
					url: this.$api.accountExchangeGroup,
					data: {
						account_group_id: group_id,
						id: [id],
						uid: uni.getStorageSync("uid"),
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
					}, 2000);
				}else {
					this.$sun.toast(result.message, 'none');
				}
			},
			
			//分组
			async getGroup() {
				const result = await this.$http.post({
					url: this.$api.accountGroupList,
					data: {
						type: this.selLabel,
						uid: uni.getStorageSync("uid"),
						page: 1,
						psize: 1000
					}
				});
				if (result.errno == 0) {
					this.groupList = result.data.list;
				}
			},

			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.accountList,
					data: {
						account_group_id: this.groupId,
						type: this.selLabel,
						uid: uni.getStorageSync("uid"),
						page: scroll.num,
						limit: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.count);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},

			// date:创建日期 time：授权过期时间（需加）
			date(addTime) {
				// 日期或中国标准时间转毫秒数：
				// let result = new Date(date).getTime();
				let allDate = new Date(addTime).getTime(); // 按秒计算，所以需要*1000（原始毫秒）
				// 预计到期时间计算
				let endtime = this.msToDate(allDate).hasTime
				// 去除秒数截取最后：的前面数字
				let index = endtime.lastIndexOf(":")
				endtime = endtime.substring(0, index);
				return endtime
			},
			// 毫秒数或中国标准时间转日期
			msToDate(msec) {
				let datetime = new Date(msec);
				let year = datetime.getFullYear();
				let month = datetime.getMonth();
				let date = datetime.getDate();
				let hour = datetime.getHours();
				let minute = datetime.getMinutes();
				let second = datetime.getSeconds();
				let result1 = year +
					'-' +
					((month + 1) >= 10 ? (month + 1) : '0' + (month + 1)) +
					'-' +
					((date + 1) < 10 ? '0' + date : date) +
					' ' +
					((hour + 1) < 10 ? '0' + hour : hour) +
					':' +
					((minute + 1) < 10 ? '0' + minute : minute) +
					':' +
					((second + 1) < 10 ? '0' + second : second);

				let result2 = year +
					'-' +
					((month + 1) >= 10 ? (month + 1) : '0' + (month + 1)) +
					'-' +
					((date + 1) < 10 ? '0' + date : date);

				let result = {
					hasTime: result1,
					withoutTime: result2
				};

				return result;
			},


		}
	}
</script>

<style lang="scss">
	
	.edit-bott {
		padding: 20rpx;
		border-top: 1px solid #292929;
	}
	
	.img-395 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 4rpx;
	}
	
	.list-public {
		padding: 0;
		background-color: #0F0F0F;
		color: #FFF;
	}
	
	.edit-top {
		width: 710rpx;
		padding: 34rpx 0 24rpx;
		background-color: #0F0F0F;
		border-radius: 10rpx;
		margin: 0 20rpx 20rpx;
	}


	.matrix-14 {
		width: 32rpx;
		height: 32rpx;
	}
	
	.empower {
		background-color: #0086FF;
		width: 96rpx;
		text-align: center;
		border-radius: 10rpx;
		padding: 8rpx;
		font-size: 24rpx;
		color: #FFFFFF;
		margin-left: auto;
	}

	.head {
		width: 90rpx;
		height: 90rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}

	.color_1E6CEB {
		color: #1E6CEB;
	}
	
	.color_00DCFF {
		font-size: 32rpx;
		font-weight: 600;
		margin-bottom: 10rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}
	
</style>
