<template>
	<view>
		<view class="h_20rpx"></view>
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a margin-bottom_30rpx">
						<image class="avatar" :src="item.user.avatar_thumb.url_list[4]"></image>
						<view>
							<view class="font-size_32rpx font-weight_bold margin-bottom_10rpx">{{item.user.nickname}}</view>
							<view class="font-size_26rpx color_999999">{{item.create_time}}</view>
						</view>
					</view>
					<view class="font-size_30rpx color_c6c6c6">{{item.text}}</view>
				</view>
			</block>
		</mescroll-body>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				tabId: '', ////1抖音 2快手 3视频号 4小红书
				id: '',
				
			}
		},
		
		onLoad() {
			//获取系统信息
			uni.getSystemInfo({
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 30;
				}
			})
		},
		
		onShow() {
			let pagearr = getCurrentPages(); //获取应用页面栈
			let currentPage = pagearr[pagearr.length - 1]; //获取当前页面信息
			if (currentPage.options.tabId) {
				this.tabId = currentPage.options.tabId;
				this.id = currentPage.options.id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}else {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}
		},
		
		methods: {
			
			async upCallback(scroll) {
				
				
				let getUrl = '';
				let getData = '';
				
				if (this.tabId == 1) {
					getUrl = this.$api.douyinCommentList;
					getData = {
						id: this.id,
						uid: uni.getStorageSync("uid"),
						cursor: scroll.num-1,
						count: 12
					}
				}
				
				if (this.tabId == 4) {
					getUrl = this.$api.xiaohongshuCommentList;
					getData = {
						id: this.id,
						uid: uni.getStorageSync("uid"),
						cursor: scroll.num-1,
					}
				}
				
				const result = await this.$http.post({
					url: getUrl,
					data: getData
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.comments.length, result.data.cursor);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.comments);
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.avatar {
		width: 92rpx;
		height: 92rpx;
		border-radius: 100rpx;
		margin-right: 20rpx;
	}
	
	.list-public {
		background-color: #0F0F0F;
		padding: 20rpx;
		color: #FFF;
		position: relative;
		z-index: 1;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}
	
</style>
