<template>
	<view>

		<view class="padding_30rpx">
			<liu-step-bar :step="stepIndex" :stepList="stepList" @clickStep="clickStep"></liu-step-bar>
		</view>

		<block v-if="stepIndex == 3">
			<view style="height: 160rpx;"></view>
			<view class="display-ac-jc">
				<view class="display-a margin-bottom_20rpx">
					<image class="img-232" src="../../../uni_modules/liu-step-bar/static/checkedImg.png"></image>
					<view class="color_FFFFFF font-size_32rpx">提交成功</view>
				</view>
				<image class="nodata" :src="imgUrl+'250.png'"></image>
				<view class="nodata-tips">即将跳到作品中心 <span class="color_FFFFFF margin-left_10rpx">{{count}}秒</span></view>
				<view class="nodata-but" @click="getWorks()">跳转作品</view>
			</view>

		</block>
		<block v-else>
			<view class="padding_20rpx margin-top_30rpx">
				<!-- <view class="font-weight_bold font-size_32rpx margin-bottom_20rpx color_FFFFFF">视频标题<span
						class="color_FF0000 margin-left_10rpx">*</span></view> -->
				<input type="text" v-model="name" class="c-input" placeholder="请输入视频标题"
					placeholder-class="placeholder" />

				<view class="c-frame">

					<view class="display-a padding-bottom_30rpx">
						<view class="display-ac-jc" style="width: 355rpx;" @click="getType(2)">
							<view :class="driveType == 2 ? 'c-font' : 'c-font-1'">语音驱动</view>
							<view class="c-line" :class="driveType == 2 ? 'c-line-1' : 'c-line-2'"></view>
						</view>
						<view class="display-ac-jc" style="width: 355rpx;" @click="getType(1)">
							<view :class="driveType == 1 ? 'c-font' : 'c-font-1'">标准文本</view>
							<view class="c-line" :class="driveType == 1 ? 'c-line-1' : 'c-line-2'"></view>
						</view>
					</view>

					<block v-if="driveType == 1">
						<view v-if="soundObj.id">
							<view class="display-a sel-voice">
								<block v-if="soundObj.url">
									<view class="display-a-jc r-play" @click="playAudio(soundObj.url)">
										<image class="img-95" :key="updateKey"
											:src="isPlay == 1 ? imgUrl+'95.png' : imgUrl+'94.png'"></image>
									</view>
								</block>
								<block v-else>
									<view class="display-a-jc r-play" @click="getSoundRefresh()">
										<view class="circle-loading">
											<view class="dot">
												<view class="first-dot"></view>
											</view>
											<view class="dot"></view>
											<view class="dot"></view>
											<view class="dot"></view>
										</view>
									</view>
								</block>
								<block v-if="soundObj.url">
									<view>
										<view class="color_FFFFFF font-size_32rpx font-weight_bold">{{soundObj.name}}
										</view>
									</view>
									<view class="display-a-jc voice-listening" @click="playAudio(soundObj.url)">
										<image class="img-247" :src="imgUrl+'247.png'"></image>
										<view class="font-size_26rpx">试听</view>
									</view>
								</block>
								<block v-else>
									<view style="width: 400rpx;">
										<view class="color_FFFFFF font-size_32rpx font-weight_bold margin-bottom_20rpx">
											{{soundObj.name}}
										</view>
										<progress :percent="percent" border-radius="10" stroke-width="5"
											activeColor="#437EFF" backgroundColor="#FFF" />
									</view>
									<view class="margin-left-auto text-align_center">
										<view class="font-size_30rpx color_FFFFFF margin-bottom_10rpx">{{percent+'%'}}
										</view>
										<view class="color_93B5FF font-size_24rpx">合成进度</view>
									</view>
								</block>
								<!-- <view>
									<view class="color_FFFFFF font-size_32rpx font-weight_bold">{{soundObj.name}}</view>
								</view>
								
								<view class="display-a-jc voice-listening" @click="getSoundRefresh()">
									<image class="img-247" :src="imgUrl+'247.png'"></image>
									<view class="font-size_26rpx">试听</view>
								</view> -->
							</view>
							<view class="display-a-jc c-info-3" @click="getAudio(2)">
								<image class="img-239" :src="imgUrl+'239.png'"></image>
								<view class="color_28C07D font-size_30rpx">重新合成音频</view>
							</view>
						</view>

						<view class="display-a-jc c-info-2" @click="getAudio(1)" v-else>
							<image class="img-239" :src="imgUrl+'239.png'"></image>
							<view class="color_28C07D font-size_30rpx">合成音频</view>
						</view>
						<view class="h_20rpx"></view>
					</block>
					<block v-if="driveType == 2">
						<block v-if="audioUrl">
							<view class="display-a v-play" v-if="audioUrl">
								<image class="img-93" :src="imgUrl + '93.png'"></image>
								<view class="v-name">{{audioName}}</view>
								<image @click="delAudio()" class="img-96" :src="imgUrl + '96.png'"></image>
							</view>
							<view class="display-ac-jc padding-bottom_30rpx" @click="playAudio(audioUrl)">
								<image class="img-play"
									:src="isPlay == 1 ? imgUrl+'sound-recording/pause.png' : imgUrl+'sound-recording/play.png'">
								</image>
								<view class="color_A1A1A1 margin-top_10rpx">{{isPlay == 1 ? '暂停' : '开始播放'}}</view>
							</view>
						</block>
						<block v-else>
							<sound-recording colorType="2" maximum="180" bgColor="#323232" backgroundColor="#323232"
								inactiveColor="#323232" @confirm="getConfirm"></sound-recording>
							<view style="height: 40rpx;"></view>
						</block>
						<view class="v-upload" @click="chooseFile()">
							<view class="v-add margin-bottom_20rpx"><span
									class="font-weight_bold margin-right_16rpx">+</span>
								{{audioUrl ? '点击重新上传mp3文件' : '点击上传mp3文件'}}
							</view>
						</view>
					</block>
				</view>

				<view class="display-a margin-top_20rpx margin-bottom_30rpx">
					<view class="a-line"></view>
					<view class="a-tilte">标准版声音克隆要求</view>
					<image class="img-243" :src="imgUrl+'243.png'"></image>
				</view>

				<view class="display-a margin-bottom_30rpx">
					<view class="c-tips"></view>
					<view class="font-size_26rpx color_FFFFFF">支持在线录音或上传音频，音频要mp3(小写)格式</view>
				</view>
				<view class="display-a margin-bottom_30rpx">
					<view class="c-tips"></view>
					<view class="font-size_26rpx color_FFFFFF">音频长度1-180秒，上传音频容量10M之内</view>
				</view>
				<view class="display-a margin-bottom_30rpx">
					<view class="c-tips"></view>
					<view class="font-size_26rpx color_FFFFFF">录制过程中要保证环境安静不得有明显噪音</view>
				</view>
				<view class="display-a margin-bottom_30rpx">
					<view class="c-tips"></view>
					<view class="font-size_26rpx color_FFFFFF">声音不要进行混响和任何特效处理要保持自然</view>
				</view>
				<view class="display-a margin-bottom_30rpx">
					<view class="c-tips"></view>
					<view class="font-size_26rpx color_FFFFFF">录制过程中只能有一个人的声音禁止多人人声</view>
				</view>


			</view>


			<view style="height: 220rpx;"></view>

			<view class="bott-pos">
				<view class="s-but display-a-jc" @click="getAvatarStatus()">
					<image class="img-249" :src="imgUrl+'249.png'"></image>
					<view class="font-size_32rpx">视频合成</view>
				</view>
				<view class="display-a-jc margin-bottom_20rpx" v-if="cloneSet.video_notice">
					<image @click="getIsProtocol()" class="img-219"
						:src="isProtocol ? imgUrl+'220.png' : imgUrl+'219.png'">
					</image>
					<view @click="openProtocol()" class="color_CBCACA">同意并且确认<span class="color_4BA2FF">《视频合成协议》</span>
					</view>
				</view>
			</view>
		</block>

		<sunui-popup ref="pop5">
			<template v-slot:content>
				<image class="img-qr-code" :src="customerConfig.customer_qr_code"></image>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop">
			<template v-slot:content>
				<view style="overflow:auto;padding:10rpx 30rpx 20rpx;">
					<scroll-view :scroll-y="true" style="height: 700rpx;">
						<rich-parser :html="cloneSet.video_notice"
							domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load ref="article" selectable
							show-with-animation use-anchor>
							<!-- 加载中... -->
						</rich-parser>
					</scroll-view>

					<view class="display-a-js margin-top_20rpx">
						<view class="c-close" @click="close(1)">关闭</view>
						<view class="c-agree" @click="close(2)">同意</view>
					</view>

				</view>
			</template>
		</sunui-popup>

	</view>
</template>

<script>
	const innerAudioContext2 = uni.createInnerAudioContext();
	export default {
		data() {
			return {

				stepList: [{
					name: '1.形象克隆',
					id: 1
				}, {
					name: '2.语音配置',
					id: 2
				}, {
					name: '3.提交合成',
					id: 3
				}], //步骤列

				stepIndex: 2,

				imgUrl: this.$imgUrl,

				videoId: '', //形象ID

				percentTime: '',
				percent: 0, //合成进度
				audioUrl: '', //音频路径
				audioName: '', //音频名称
				isPlay: '2', //1播放 2暂停

				//音频信息
				soundObj: {
					name: '',
					url: '', //
					id: '', //
					type: '', //1 历史音频返回 2生成音频返回
				},

				name: '',

				driveType: '2', //1标准 2语音

				cloneSet: uni.getStorageSync("cloneSet"), //克隆开关设置


				setLine: '', //1 线路1 2 线路2 高级形象
				current_status: '',
				new_current_status: '',

				web_people_width: '', //标准形象 宽
				web_people_height: '', //标准形象 高

				isProtocol: false, //

				count: 3, // 初始倒计时秒数
				timer: null, // 计时器

				isWhether: true, //判断重复点击

				isKefu: true, //true隐藏 false展开

				customerConfig: uni.getStorageSync('customerConfig'), //客服配置

			}
		},

		onLoad(options) {
			if (options.videoId) {
				this.videoId = options.videoId;
			}
			if (options.video_cover) {
				console.log(options.video_cover);
				this.getImage(options.video_cover + '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto');
			}

		},

		onShow() {

		},

		onUnload() {

			if (this.timer) {
				clearInterval(this.timer);
			}

			this.stopProgress();

			if (!innerAudioContext2.paused) {
				innerAudioContext2.stop();
			}

		},

		methods: {

			//获取图片信息
			getImage(url) {

				uni.getImageInfo({
					src: url,
					success: (image) => {

						uni.showLoading({
							mask: true,
							title: '加载中...'
						})

						this.web_people_width = image.width;
						this.web_people_height = image.height;

						console.log("图片信息---->", this.web_people_width, this.web_people_height);

						uni.hideLoading();

					},
					fail: err => {
						uni.hideLoading();
						this.$sun.toast(err, 'none');
						console.log(err);
					}
				});
			},

			//选择音频
			getAudio(type) {
				if (type == 1) {
					uni.navigateTo({
						url: '/pages/index/clip/audio'
					})
				}
				if (type == 2) {
					uni.navigateTo({
						url: '/pages/index/clip/audio?tabsId=2&id=' + this.soundObj.id
					})
				}
			},

			//上一页音频信息
			otherFun(obj) {
				console.log("音频信息-----", obj);
				this.percent == 0;
				if (obj.type == 1) {
					this.soundObj = obj;
				}
				if (obj.type == 2) {
					this.getVideoSoundList();
					this.startProgress();
				}
			},

			startProgress() {
				this.percentTime = setInterval(() => {
					if (this.percent < 100) {
						if (this.percent == 90) {
							this.getSoundRefresh();
						} else {
							this.percent = Math.min(this.percent + 10, 100);
						}
					} else {
						this.stopProgress();
					}
				}, 3000); // 每秒执行一次
			},
			stopProgress() {
				if (this.percentTime) {
					clearInterval(this.percentTime);
					this.percentTime = null;
				}
			},

			//历史音频
			async getVideoSoundList() {
				const result = await this.$http.post({
					url: this.$api.videoSoundList,
					data: {
						uid: uni.getStorageSync('uid'),
						page: 1,
						psize: 10
					}
				});
				if (result.errno == 0) {

					this.soundObj = {
						name: result.data.list[0].name,
						url: result.data.list[0].url, //
						id: result.data.list[0].id, //
					};

					this.$forceUpdate();
				}
			},

			//音频刷新
			async getSoundRefresh() {

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.soundRefresh,
					data: {
						uid: uni.getStorageSync('uid'),
						sound_id: this.soundObj.id,
					}
				});
				if (result.errno == 0) {
					this.isWhether = true;
					if (result.data) {
						// this.$sun.toast("音频合成成功");
						this.getVideoSoundList();
						this.percent = 100;
					}
					// else {
					// 	this.$sun.toast("正在合成中，请稍后重试",'none');
					// }
					// setTimeout(() => {
					// 	if (result.data) {
					// 		this.playAudio(result.data);
					// 	}
					// 	this.isWhether = true;
					// }, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//
			getType(type) {
				this.driveType = type;
				this.isPlay = 2;
				this.audioUrl = ''; //音频路径
				this.audioName = ''; //音频名称
			},

			getKefu() {
				if (this.isKefu) {
					this.isKefu = false;
				} else {
					if (this.customerConfig.customer_type == 'on_line') {
						return;
					} else if (this.customerConfig.customer_type == 'phone') {
						if (this.customerConfig.customer_phone) {
							this.$sun.phone(this.customerConfig.customer_phone);
						} else {
							this.$sun.toast("暂无联系方式", 'error');
						}
					} else if (this.customerConfig.customer_type == 'qr_code') {
						this.$refs.pop5.show({
							style: 'background-color:#000;width:600rpx;border-radius:10rpx;',
							bottomClose: true,
							shadeClose: false,
						});
					}
				}
			},

			getWorks() {

				uni.redirectTo({
					url: '/pages/assets/digital-assets?tabsId=2&type=1&tabsNextId=2'
				})

			},

			//查询形象是否克隆成功
			async getAvatarStatus() {

				if (!innerAudioContext2.paused) {
					innerAudioContext2.stop();
				}

				if (!this.name) {
					this.$sun.toast("请先输入视频标题", 'none');
					return;
				}

				if (this.driveType == 2 && (!this.audioUrl)) {
					this.$sun.toast("请先上传或录制音频", 'none');
					return;
				}

				if (this.driveType == 1 && (!this.soundObj.url)) {
					this.$sun.toast("音频合成中,请耐心等待!", 'none');
					return;
				}

				if (!this.isProtocol) {
					this.$sun.toast("请先勾选协议", 'none');
					return;
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				if (this.driveType == 1) {
					this.getSpeedSoundAdd(this.soundObj.url);
				}
				if (this.driveType == 2) {
					this.getSpeedSoundAdd(this.audioUrl);
				}

			},

			//极速视频添加声音 线路三/线路二
			async getSpeedSoundAdd(aUrl) {

				const result = await this.$http.post({
					url: this.$api.speedSoundAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: this.name, //视频标题	必传
						url: aUrl, // 音频链接 从/video/sendTts接口返回	必传
					}
				});
				if (result.errno == 0) {
					this.getGenerate(result.data);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//生成高级视频 线路二
			async getGenerate(id) {

				const result = await this.$http.post({
					url: this.$api.generateTwo,
					data: {

						uid: uni.getStorageSync('uid'),
						name: this.name, //视频标题	必传
						image_id: this.videoId, //形象id		必传
						sound_id: id,
						width: this.web_people_width,
						height: this.web_people_height,
						combination: '',
						caption_josn: '',

					}
				});
				if (result.errno == 0) {
					// this.$sun.toast(result.message);
					this.stepIndex = 3;
					setTimeout(() => {
						this.getTime();
						this.isWhether = true;
					}, 1000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
			},

			//倒计时
			getTime() {
				this.timer = setInterval(() => {
					if (this.count > 0) {
						this.count -= 1;
					} else {
						clearInterval(this.timer); // 清除计时器
						this.getWorks(); // 跳转到下一个页面
					}
				}, 1000);
			},

			//协议
			getIsProtocol() {
				this.isProtocol = !this.isProtocol;
			},

			/*  克隆协议  */
			close(type) {
				if (type == 2) {
					this.isProtocol = true;
				}
				this.$refs.pop.close();
			},
			openProtocol() {
				// this.isProtocol = true;
				this.$refs.pop.show({
					title: '视频合成协议',
					style: 'background-color:#fff;width:700rpx;border-radius:10rpx;',
					// bottomClose: true,
					shadeClose: false,
				});

			},

			//播放音频
			playAudio(audioUrl) {

				uni.showLoading({
					title: '正在试听...',
					// mask: true
				})

				innerAudioContext2.src = audioUrl;

				if (this.isPlay == 2) {
					this.isPlay = 1;
					innerAudioContext2.play();
					innerAudioContext2.onPlay(() => {
						// console.log('开始播放');
					});
					innerAudioContext2.onEnded(() => {
						this.isPlay = 2;
						innerAudioContext2.destroy();
						// innerAudioContext2 = null;
						uni.hideLoading();
						// this.$sun.toast("音频播放完成");
						// console.log('音频播放结束：');
					});
					innerAudioContext2.onError((err) => {
						innerAudioContext2.destroy();
						innerAudioContext2 = null;
						uni.hideLoading();
						// console.log('播放音频出错：', err);
					});
				} else {
					this.isPlay = 2;
					uni.hideLoading();
					innerAudioContext2.pause();
					innerAudioContext2.onPause(() => {
						// console.log('暂停播放');
					});
				}

				// console.log("---->", this.isPlay);

			},

			//录音上传
			getConfirm(e) {
				// console.log("录音文件==>", e);
				this.upload(e);
			},

			//音频上传
			chooseFile() {
				uni.chooseMessageFile({
					count: 1,
					type: 'file',
					// extension: ['.mp3', '.m4a', '.wav', 'wav', 'mp3', 'm4a', '.MP3', '.M4A', '.WAV', 'WAV', 'MP3',
					// 	'M4A'
					// ],
					extension: ['.mp3', 'mp3'],
					success: (res) => {
						// console.log("从聊天记录中获取文件", res);
						const {
							errMsg,
							tempFiles
						} = res;
						if (errMsg == "chooseMessageFile:ok" && tempFiles.length) {
							const {
								name,
								size,
								path
							} = tempFiles[0];
							// console.log("临时文件", {
							// 	size,
							// 	path
							// });
							if ((name.slice(-4) != ".mp3")) {
								return uni.showToast({
									icon: "none",
									title: "请上传mp3格式音频文件！",
									duration: 2000,
								});
							}
							if (size / 1024 / 1024 > 10) {
								return uni.showToast({
									icon: "none",
									title: "音频文件过大，请重新选择！",
								});
							}
							// console.log("从聊天记录中获取文件", name,path);
							// formData.value.audioName = name
							this.upload(path);
						}
					},
				});
			},
			upload(path) {
				uni.showLoading()
				// 调用uni.uploadFile将文件上传至服务器
				uni.uploadFile({
					url: this.$api.upload, // 设置上传接口地址
					filePath: path, // 需要上传的文件路径
					name: 'file', // 后台接收文件时对应的字段名称
					fileType: 'audio', // 指定文件类型
					header: {
						// "Content-Type": "multipart/form-data",
					},
					success: (response) => {
						// TODO: 处理上传成功后的操作
						const data = JSON.parse(response.data)
						// console.log('文件上传成功',data);
						// formData.value.accompanyInfo = data.data.url
						if (data.errno != 0) {
							uni.showToast({
								title: '文件上传失败',
								icon: 'none'
							});
						} else {
							this.audioUrl = data.data;
							let index = this.audioUrl.lastIndexOf('/'); // 获取最后一个/的位置
							let lastSegment = this.audioUrl.substring(index + 1); // 截取最后一个/后的值
							this.audioName = lastSegment;
							this.$sun.toast("文件上传成功");
							// console.log('上传成功', this.audioUrl, this.audioName);
						}
						uni.hideLoading()
						return
					},
					fail(error) {
						uni.hideLoading()
						// console.log('文件上传失败');
						// console.log(error);

						// TODO: 处理上传失败后的操作
					}
				});
			},

			//点击步骤
			clickStep(e) {
				// console.log('所点击步骤信息:', e)
				// this.stepIndex = e.id;
			},

		}
	}
</script>

<style lang="scss">
	.voice-listening {
		background-color: #262323;
		width: 150rpx;
		border-radius: 100rpx;
		padding: 18rpx 0;
		margin-left: auto;
		color: #FFF;
	}

	.c-info-3 {
		padding: 30rpx 0;
		// border: 1px dashed rgb(67, 67, 67);
		margin: 20rpx;
		border-radius: 10rpx;
	}

	.c-info-2 {
		padding: 60rpx 0;
		border: 1px dashed rgb(67, 67, 67);
		margin: 20rpx;
		border-radius: 10rpx;
	}

	.img-247 {
		width: 30rpx;
		height: 30rpx;
		margin-right: 10rpx;
	}

	/*圆形加载*/
	.circle-loading {
		width: 44rpx;
		height: 44rpx;
		position: relative;
		margin: auto;

		.dot {
			position: absolute;
			top: 0;
			left: 0;
			width: 44rpx;
			height: 44rpx;
			animation: 1.5s loadrotate cubic-bezier(0.800, 0.005, 0.500, 1.000) infinite;

			&:after,
			.first-dot {
				content: '';
				position: absolute;
				width: 12rpx;
				height: 12rpx;
				background: #FFF;
				border-radius: 50%;
				left: 50%;
			}

			.first-dot {
				background: #fff;
				animation: 1.5s dotscale cubic-bezier(0.800, 0.005, 0.500, 1.000) infinite;

			}
		}
	}

	@for $i from 1 through 4 {
		.circle-loading {
			&>.dot:nth-child(#{$i}) {
				animation-delay: 0.15s*$i;
			}
		}
	}

	@keyframes loadrotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	@keyframes dotscale {

		0%,
		10% {
			width: 28rpx;
			height: 28rpx;
			margin-left: -2rpx;
			margin-top: -5rpx;
		}

		50% {
			width: 16rpx;
			height: 16rpx;
			margin-left: 0rpx;
			margin-top: 0rpx;
		}

		90%,
		100% {
			width: 28rpx;
			height: 28rpx;
			margin-left: -2rpx;
			margin-top: -5rpx;
		}
	}

	.img-95 {
		width: 30rpx;
		height: 30rpx;
	}

	.r-play {
		width: 70rpx;
		height: 70rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(61, 108, 255), rgb(136, 144, 248) 100%);
		margin-right: 20rpx;
	}

	.img-239 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 14rpx;
	}

	.sel-voice {
		padding: 40rpx 20rpx 20rpx;
		border-top: 1px dashed rgb(67, 67, 67);

	}

	.c-line-2 {
		background-color: #323232;
	}

	.c-line-1 {
		background: linear-gradient(180.00deg, rgb(205, 133, 250), rgb(90, 244, 255) 100%);
	}

	.c-line {
		width: 60rpx;
		height: 6rpx;
		border-radius: 100rpx;
		margin-top: 10rpx;
	}

	.c-font-1 {
		color: #FFF;
		font-size: 32rpx;
	}

	.c-font {
		background: linear-gradient(90.00deg, rgb(90, 244, 255), rgb(133, 153, 250));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-size: 32rpx;
		font-weight: 600;
		letter-spacing: 0%;
	}

	.img-qr-code {
		width: 500rpx;
		height: 500rpx;
		margin: 50rpx;
	}

	.img-232 {
		width: 48rpx;
		height: 48rpx;
		margin-right: 6rpx;
		margin-top: 4rpx;
	}

	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}

	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}

	.nodata {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 10rpx;
	}

	.c-agree {
		width: 310rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		color: #FFF;
		font-size: 32rpx;
		padding: 20rpx 0;
	}

	.c-close {
		width: 310rpx;
		text-align: center;
		border: 1px solid rgb(203, 202, 202);
		border-radius: 10rpx;
		background: rgb(255, 255, 255);
		font-size: 32rpx;
		color: #929292;
		padding: 20rpx 0;
	}

	.img-249 {
		width: 40rpx;
		height: 40rpx;
		margin-right: 14rpx;
	}

	.color_4BA2FF {
		color: #4BA2FF;
		font-size: 26rpx;
	}

	.color_CBCACA {
		color: #cbcaca;
		font-size: 26rpx;
	}

	.img-219 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 12rpx;
	}

	.img-play {
		width: 140rpx;
		height: 140rpx;
		border-radius: 100rpx;
	}

	.c-tips {
		background: #17DA70;
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		margin-right: 12rpx;
	}

	.img-243 {
		width: 82rpx;
		height: 48rpx;
	}

	.bott-pos {
		width: 750rpx;
		position: fixed;
		bottom: 0;
		padding: 30rpx 50rpx;
	}

	.s-but {
		width: 650rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		color: #FFF;
		padding: 24rpx 0;
		margin-bottom: 10rpx;
	}

	.a-tilte {
		background: linear-gradient(90.00deg, rgb(53, 221, 235), rgb(63, 182, 254));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-weight: 700;
		letter-spacing: 0%;
		font-size: 34rpx;
	}

	.a-line {
		width: 6rpx;
		height: 26rpx;
		background-color: #35DDEB;
		border-radius: 4rpx;
		margin-right: 10rpx;
	}

	.c-frame {
		width: 710rpx;
		background-color: #323232;
		border-radius: 10rpx;
		padding-top: 40rpx;
	}

	.color_B7B7B7 {
		color: #B7B7B7;
		font-size: 24rpx;
	}

	.v-add {
		color: #40CAFE;
		font-size: 30rpx;
	}

	.v-upload {
		width: 710rpx;
		text-align: center;
		padding: 30rpx 0;
		background-color: #191919;
		border-radius: 0 0 10rpx 10rpx;
	}

	.img-96 {
		width: 34rpx;
		height: 34rpx;
		margin-left: auto;
	}

	.img-94 {
		width: 22rpx;
		height: 22rpx;
		margin-right: 4rpx;
	}

	.v-play {
		width: 670rpx;
		border-radius: 10rpx;
		background: #1F1F1F;
		padding: 40rpx 20rpx;
		color: #FFF;
		margin: 0 20rpx 50rpx;
	}

	.v-name {
		width: 400rpx;
		color: #FFF;
		font-size: 30rpx;
	}

	.img-93 {
		width: 48rpx;
		height: 48rpx;
		margin-right: 16rpx;
	}

	.c-input {
		width: 670rpx;
		// background-color: #434343;
		background-color: #2A2A2A;
		border-radius: 10rpx;
		padding: 20rpx;
		// margin: 20rpx 0;
		margin-bottom: 20rpx;
		color: #FFF;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		background-color: #000;
		border: none;
	}
</style>