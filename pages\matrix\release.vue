<template>
	<view>
		
		<view class="display-a acc-top">
			<block v-for="(item,index) in tabs" :key="index">
				<view class="tabs" @click="getLabel(item.id)">
					<view class="margin-bottom_20rpx" :style="tabId == item.id ? 'color:#69F8AA;' : ''">{{item.name}}</view>
					<view class="tabs-line" :style="tabId == item.id ? 'background: #69F8AA;' : ''"></view>
				</view>
			</block>
		</view>
		
		<view class="display-a search-x">
			<icon type="search" size="16" style="margin-left: 240rpx;margin-right: 20rpx;"></icon>
			<input type="text" placeholder="请输入任务名称" v-model="name" placeholder-class="color_8b8b8b"
				@input="inputChange" @confirm="confirmChange" />
		</view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="list-name">{{item.name}}</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="list-type list-type-1" v-if="item.channle_type == 1">D音</view>
						<view class="list-type list-type-2" v-if="item.channle_type == 2">K手</view>
						<view class="list-type list-type-3" v-if="item.channle_type == 3">视频号</view>
						<view class="list-type list-type-4" v-if="item.channle_type == 4">小红薯</view>
						<view class="font-size_26rpx color_999999">{{item.create_time}}</view>
					</view>
					<view class="display-a">
						<view style="width: 580rpx;">
							<progress :percent="item.percentage" border-radius="10" stroke-width="10"
								activeColor="#166DFD" backgroundColor="#FFF" />
						</view>
						<view class="margin-left-auto text-align_center">
							<view class="font-size_30rpx">{{item.percentage+'%'}}</view>
						</view>
					</view>
					<view class="display-a list-data">
						<view class="width_230rpx-center">
							<view>{{item.published_task}}</view>
							<view>发布成功</view>
						</view>
						<view class="width_230rpx-center">
							<view>{{item.published_wait_task}}</view>
							<view>待发布</view>
						</view>
						<view class="width_230rpx-center">
							<view>{{item.published_fail_task}}</view>
							<view>发布失败</view>
						</view>
					</view>
					<view class="display-a padding_30rpx_0">
						
						<block v-if="item.channle_type == 1 || item.channle_type == 2 || item.channle_type == 4">
							<view class="width_230rpx-center" @click="getAllTask(item.id)">
								<view class="color_00FFCA">查看任务</view>
							</view>
							<view class="width_230rpx-center">
								<view class="color_FF0000" @click="termination(item.id)" v-if="item.status == 1">终止任务</view>
								<view class="color_00FFCA" v-if="item.status == 3">已完成</view>
								<view class="color_00FFCA" v-if="item.status == 2">已终止</view>
							</view>
							<view class="width_230rpx-center">
								<view class="color_FF0000" @click="del(item.id)" v-if="item.status == 2 || item.status == 3">删除任务</view>
								<view class="color_FF0_rgb" v-else>删除任务</view>
							</view>
						</block>
						
						<block v-if="item.channle_type == 3">
							<view class="width_230rpx-center">
								
							</view>
							<view class="width_230rpx-center">
								
							</view>
							<view class="width_230rpx-center">
								<view class="color_FF0000" @click="del(item.id)" v-if="item.status == 3">删除任务</view>
								<view class="color_FF0_rgb" v-else>删除任务</view>
							</view>
						</block>
						
					</view>
				</view>
			</block>
		</mescroll-body>
		
		
		<image class="matrix-11" @click="getBatch()" :src="imgUrl+'376.png'"></image>
		
		<sunui-popup ref="pop2">
			<template v-slot:content>
				<view class="display-a-jc padding_30rpx margin-bottom_30rpx">
					<view class="font-weight_bold font-size_32rpx">请选择发布渠道</view>
				</view>
				<view class="display-fw-a p-bo">
					<block v-for="(item,index) in tabs" :key="index">
						<view class="width_186rpx-center margin-bottom_30rpx" @click="getAdd(item.id)">
							<image class="matrix-17" :src="imgUrl+item.url"></image>
							<view>{{item.name}}</view>
						</view>
					</block>
				</view>
				<view class="cancel" @click="closeBatch()">取消</view>
			</template>
		</sunui-popup>

		<sunui-tabbar2 v-if="type !== 1" :fixed="true" :current="tabIndex" tintColor="#00FFCA"
			backgroundColor="#1B1B1B"></sunui-tabbar2>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: null,
				tabIndex: 4,

				imgUrl: this.$imgUrl,

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],

				name: '',

				tabs: [{
						id: '1',
						name: 'D音',
						url: '378.png'
					},
					{
						id: '2',
						name: 'K手',
						url: '379.png'
					},
					{
						id: '3',
						name: '视频号',
						url: '380.png'
					},
					{
						id: '4',
						name: '小红薯',
						url: '381.png'
					},
					// {id:'5',name:'B站',url:'382.png'}
				],
				tabId: '1', //1。D音 2。K手 3。视频号 4.小红薯 5.B站

				windowHeight: '',

				isWhether: true, //判断重复点击

			}
		},

		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)
			}
			//获取系统信息
			uni.getSystemInfo({
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 400;
				}
			})
		},
		
		onShow() {
			this.$nextTick(() => {
				this.mescroll.resetUpScroll();
			});
		},
		
		methods: {
			
			//查看任务
			getAllTask(id) {
				uni.navigateTo({
					url: '/pages/matrix/allTask?taskId='+id+'&type=2'+'&selLabel='+this.tabId
				})
			},
			
			//删除任务
			del(id) {
				uni.showModal({
					title: '提示',
					content: '是否确认删除该任务?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.delTask,
								data: {
									uid: uni.getStorageSync("uid"),
									id: id,
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			//终止任务
			termination(id) {
				uni.showModal({
					title: '提示',
					content: '任务一旦终止后不可启用,是否确认?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.endTask,
								data: {
									uid: uni.getStorageSync("uid"),
									id: id,
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.VideoTaskList,
					data: {
						type: this.tabId,
						uid: uni.getStorageSync("uid"),
						name: this.name,
						page: scroll.num,
						psize: 12
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			getAdd(type) {
				this.tabId = type;
				this.closeBatch();
				uni.navigateTo({
					url: '/pages/matrix/releaseAdd?type='+type
				})
			},
			
			//更多
			getBatch() {
				this.$refs.pop2.show({
					style: 'background-color:#fff;width:750rpx;height:auto;border-radius: 20px 20px 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
				});
			},
			closeBatch() {
				this.$refs.pop2.close();
			},
			
			// 输入事件
			inputChange(e) {
				this.name = e.detail.value;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			
			},
			confirmChange(e) {
				this.name = e.detail.value;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			getLabel(type) {
				this.tabId = type;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
		}
	}
</script>

<style lang="scss">
	
	.color_FF0_rgb {
		color: rgba(255, 0, 0, 0.34);
	}
	
	.cancel {
		width: 750prx;
		text-align: center;
		color: #0C91F3;
		padding: 40rpx;
	}
	
	.matrix-17 {
		width: 96rpx;
		height: 96rpx;
		margin-bottom: 20rpx;
	}
	
	.search-x {
		width: 710rpx;
		margin: 0 20rpx 20rpx;
		background-color: #323232;
		border-radius: 100rpx;
		padding: 20rpx;
		color: #FFF;
	}
	
	.list-type-4 {
		background-color: #FF0000;
	}
	
	.list-type-3 {
		background-color: #FFDC00;
	}
	
	.list-type-2 {
		background-color: #FF7B00;
	}
	
	.list-type-1 {
		background-color: #166DFD;
	}
	
	.list-type {
		display: inline-block;
		padding: 4rpx 12rpx;
		border-radius: 10rpx;
		margin-right: 18rpx;
		font-size: 24rpx;
	}
	
	.list-name {
		font-size: 32rpx;
		padding: 26rpx 0 20rpx;
	}
	
	.list-data {
		padding: 20rpx 0;
		border-bottom: 1px solid #2C2C2C;
	}
	
	.list-public {
		background-color: #0F0F0F;
		padding: 0 20rpx;
		color: #FFF;
	}
	
	.matrix-11 {
		width: 96rpx;
		height: 96rpx;
		position: fixed;
		bottom: 170rpx;
		right: 20rpx;
		z-index: 9;
	}
	
	.tabs-line {
		width: 60rpx;
		background-color: #232323;
		height: 6rpx;
		border-radius: 100rpx;
		margin-left: 63rpx;
		// margin-left: 95rpx;
	}
	
	.tabs {
		width: 186rpx;
		// width: 250rpx;
		text-align: center;
		color: #E8E8E8;
	}
	
	.acc-top {
		padding: 24rpx 0;
		border-bottom: 1px solid rgb(73, 73, 73);
		margin-bottom: 20rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}

</style>
