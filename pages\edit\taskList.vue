<template>
	<view>
		<view class="h_20rpx"></view>
		<mescroll-body ref="mescrollRef" :isShowEmptys="true" :height="windowHeight+'rpx'" @init="mescrollInit"
			@down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
			<block v-if="list.length > 0">
				<block v-for="(item,index) in list" :key="index">
					<view class="list-public" @click="getVideoList(item)">
						<view class="color_FFFFFF font-size_32rpx font-weight_bold margin-bottom_20rpx">{{item.title}}</view>
						<view class="color_A1A1A1">{{item.create_time}}</view>
					</view>
				</block>
			</block>
			<block v-else>
				<view class="display-ac-jc">
					<image class="nodata" src="../../static/nodata.png"></image>
					<view class="nodata-tips">您还没有剪辑任务，快去剪辑一个吧~</view>
					<view class="nodata-but">去创建剪辑任务</view>
				</view>
			</block>
		</mescroll-body>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				imgUrl: this.$imgUrl,
			}
		},
		
		onLoad() {
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 20;
				},
			})
		},
		
		onShow() {
			if (uni.getStorageSync('uid')) {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
							// uni.navigateTo({
							// 	url: '/pages/auth/auth?type=1'
							// })
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},
		
		methods: {
			
			getVideoList(obj) {
				uni.navigateTo({
					url: '/pages/edit/videoList?id='+obj.id+'&name='+obj.title
				})
			},
			
			
			async upCallback(scroll) {
				
				let getUrl = '';
				let getData = {};
				
				getUrl = this.$api.clipTaskList;
				getData = {
					uid: uni.getStorageSync('uid'),
					page: scroll.num,
					psize: 12
				}
			
				const result = await this.$http.post({
					url: getUrl,
					data: getData
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表
			
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.list-public {
		background-color: #313131;
		padding: 30rpx 20rpx;
	}
	
	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}
	
	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}
	
	.nodata {
		width: 400rpx;
		height: 400rpx;
		margin-bottom: 10rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
	
</style>
