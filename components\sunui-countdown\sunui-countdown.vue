<template>
	<view>
		<view class="sunui-countdown">
			<view v-if="showDay"><text>{{countTime.day}}</text>{{format ? format :  '天'}}</view>
			<view v-if="showHour"><text>{{countTime.hour}}</text>{{format ? format :  '时'}}</view>
			<view v-if="showMin"><text>{{countTime.min}}</text>{{format ? format :  '分'}}</view>
			<view v-if="showSec"><text>{{countTime.sec}}</text>{{format ? '' :  '秒'}}</view>
		</view>
	</view>
</template>

<script>
	import CountDown from './countdown.js';
	export default {
		data() {
			return {
				countTime: ''
			}
		},
		name: 'sunui-countdown',
		props: {
			showDay: {
				type: Boolean,
				default: true
			},
			showHour: {
				type: Boolean,
				default: true
			},
			showMin: {
				type: Boolean,
				default: true
			},
			showSec: {
				type: Boolean,
				default: true
			},
			format: {
				type: null,
				default: ''
			},
			timeid: {
				type: [Number, String],
				default: 0
			},
			time: {
				type: null,
				default: 0
			}
		},
		created() {
			new CountDown(this.time).run(t => {
				this.countTime = t;
			}).end(() => {
				this.$emit('end', {
					id: this.timeid
				});
			});
		}
	}
</script>

<style>
	.sunui-countdown {
		display: flex;
		align-items: center;
		margin-left: 10rpx;
	}
</style>
