<template>
	<view>
		<view class="h_20rpx"></view>
		<view class="display-a padding_20rpx">
			<view class="m-line"></view>
			<view class="s-tips">{{type == 1 ? 'AI数字人' : '声音克隆'}}</view>
		</view>
		<view class="m-tips">
			<block v-if="type == 3">
				通过文本语音驱动让数字人帮你创作口播视频
			</block>
			<block v-else>
				让每个人都有属于自己的专属{{type == 1 ? '数字人形象' : '声音'}}
			</block>
		</view>
		
		<block v-if="type == 1">
			
			<view class="list-public display-a-js" @click="getClone(1)" v-if="cloneSet.avatar_high_open == 1">
				<view style="width: 470rpx;">
					<view class="display-a margin-bottom_20rpx">
						<image class="img-147" :src="imgUrl+'147.png'"></image>
						<view class="m-name">高级克隆</view>
						<view class="m-label">效果好</view>
					</view>
					<view class="color_C3C2C2">上传一段30秒以上视频，经过几分钟训练即可生成高端逼真的高清数字人分身</view>
				</view>
				<view class="m-but m-but-1">一键克隆</view>
			</view>
			
			<view class="list-public display-a-js" @click="getClone(3)" v-if="cloneSet.avatar_open == 1">
				<view style="width: 470rpx;">
					<view class="display-a margin-bottom_20rpx">
						<image class="img-147" :src="imgUrl+'202.png'"></image>
						<view class="m-name">极速克隆</view>
						<view class="m-label">高性价</view>
					</view>
					<view class="color_C3C2C2">上传一段15秒以上视频，无需训练一键合成真人出镜的标清口播视频</view>
				</view>
				<view class="m-but m-but-6">一键克隆</view>
			</view>
			
			<!-- <view class="list-public display-a-js" @click="getClone(2)">
				<view style="width: 470rpx;">
					<view class="display-a margin-bottom_20rpx">
						<image class="img-147" :src="imgUrl+'148.png'"></image>
						<view class="m-name">照片数字人克隆</view>
					</view>
					<view class="color_C3C2C2">只需上传一张可识别人物照片,即可快速克隆生成一个会说话的照片数字人</view>
				</view>
				<view class="m-but m-but-2">一键克隆</view>
			</view> -->
			
			<!-- <view class="list-public display-a-js" @click="getFace()">
				<view style="width: 470rpx;">
					<view class="display-a margin-bottom_20rpx">
						<image class="img-147" :src="imgUrl+'149.png'"></image>
						<view class="m-name">AI演员</view>
					</view>
					<view class="color_C3C2C2">只需上传一段视频和一张正面人脸照片每户人脸完美融合生成一个新的数字人形象</view>
				</view>
				<view class="m-but m-but-3">AI换脸</view>
			</view> -->
			
		</block>
		
		<block v-if="type == 2">
			<view class="list-public display-a-js" @click="getVoice()">
				<view style="width: 470rpx;">
					<view class="display-a margin-bottom_20rpx">
						<image class="img-147" :src="imgUrl+'333.png'"></image>
						<view class="m-name">入门体验版克隆</view>
					</view>
					<view class="color_C3C2C2">基于自研模型，合成效果和音色一般，适合客户体验测试用， 本模型非实时合成，高峰值有可能需要长时间等待。</view>
				</view>
				<view class="m-but m-but-10" style="color: #000;">一键克隆</view>
			</view>
			<view class="list-public display-a-js" @click="getHF()" v-if="cloneSet.xunfei_sound_clone_swich == 1">
				<view style="width: 470rpx;">
					<view class="display-a margin-bottom_20rpx">
						<image class="img-147" :src="imgUrl+'334.png'"></image>
						<view class="m-name">专业轻量版克隆</view>
					</view>
					<view class="color_C3C2C2">采用大厂语音模型，合成效果和音色较好、适合有一定要求 的用户，支持500字长文本和多国语言，实时合成体验好。</view>
				</view>
				<view class="m-but m-but-4" style="color: #000;">一键克隆</view>
			</view>
			<view class="list-public display-a-js" @click="getSenior()" v-if="cloneSet.voice_high_open == 1">
				<view style="width: 470rpx;">
					<view class="display-a margin-bottom_20rpx">
						<image class="img-147" :src="imgUrl+'162.png'"></image>
						<view class="m-name">高保真音色克隆</view>
					</view>
					<view class="color_C3C2C2">基于火山引擎模型，专为高端客户量身打造，兼容性极佳，克 隆效果逼真，实时语音合成，用户体验极佳。</view>
				</view>
				<view class="m-but m-but-5" style="color: #000;">一键克隆</view>
			</view>
		</block>
		
		<block v-if="type == 3">
			<view class="list-public display-a" @click="getClip(3)" v-if="cloneSet.avatar_open == 1">
				<image class="img-206" :src="imgUrl+'206.png'"></image>
				<view class="m-name">极速合成</view>
				<view class="m-but m-but-7 margin-left-auto">立即合成</view>
			</view>
			<view class="list-public display-a" @click="getClip(2)" v-if="cloneSet.avatar_high_open == 1">
				<image class="img-206" :src="imgUrl+'207.png'"></image>
				<view class="m-name">高级合成</view>
				<view class="m-but m-but-8 margin-left-auto">立即合成</view>
			</view>
			<!-- <view class="list-public display-a">
				<image class="img-206" :src="imgUrl+'208.png'"></image>
				<view class="m-name">照片合成</view>
				<view class="m-but m-but-9 margin-left-auto">立即合成</view>
			</view> -->
		</block>
		
		<view style="position: fixed;bottom: 20rpx;">
			<block v-if="system.banner_id">
				<view style="width: 710rpx;height: 240rpx;border-radius: 10rpx;margin-left: 20rpx;">
					<ad v-if="system.banner_id" :unit-id="system.banner_id"></ad>
				</view>
			</block>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				type: '', //1形象克隆 2声音克隆 3视频合成
				
				imgUrl: this.$imgUrl,
				
				system: uni.getStorageSync('system'),
				
				cloneSet: {},
				
			}
		},
		
		onLoad(options) {
			if (options.type) {
				this.type = options.type;
				this.type == 1 ? this.$sun.title("形象克隆") : this.type == 2 ? this.$sun.title("声音克隆") : this.$sun.title("视频合成");
			}
		},
		
		onShow() {
			this.getCloneSet();
		},
		
		methods: {
			
			getClip(type) {
				uni.navigateTo({
					url: '/pages/index/clip/clip?type='+type
				})
			},
			
			//克隆设置
			async getCloneSet() {
				const result = await this.$http.post({
					url: this.$api.cloneSet
				});
				if (result.errno == 0) {
					this.cloneSet = result.data;
				}
			},
			
			getSenior() {
				uni.navigateTo({
					url: '/pages/index/voice/senior'
				})
			},
			
			getHF() {
				uni.navigateTo({
					url: '/pages/index/voice/highFidelity'
				})
			},
			
			getVoice() {
				uni.navigateTo({
					url: '/pages/index/voice/voice'
				})
			},
			
			getClone(type) {
				
				// uni.showModal({
				// 	content:"算法升级，暂停服务",
				// 	confirmText: "确认",
				// 	showCancel: false,
				// 	success: (res) => {
				// 		if (res.confirm) {
							
				// 		} else if (res.cancel) {
							
				// 		}
				// 	}
				// })
				
				// return;
				
				uni.navigateTo({
					url: '/pages/index/clone/clone?isSel='+type
				})
			},
			
			getFace() {
				uni.navigateTo({
					url: '/pages/index/changeFace/changeFace'
				})
			},
			
		}
	}
</script>

<style lang="scss">
	
	.m-label {
		width: 90rpx;
		text-align: center;
		background-color: #FF0000;
		color: #FFF;
		font-size: 24rpx;
		border-radius: 10px 0px 10px 0px;
		padding: 4rpx 0;
		margin-left: 16rpx;
	}
	
	.img-206 {
		width: 68rpx;
		height: 68rpx;
		margin-right: 14rpx;
	}
	
	.s-tips {
		background: linear-gradient(96.34deg, rgb(210, 174, 255),rgb(154, 93, 228));
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
		text-fill-color: transparent;
		font-size: 40rpx;
		font-weight: bold;
	}
	
	.m-line {
		width: 6rpx;
		height: 26rpx;
		background-color: #9F57F7;
		border-radius: 10rpx;
		margin-right: 14rpx;
	}
	
	.m-tips {
		padding: 0 20rpx 40rpx;
		color: #FFF;
	}
	
	.m-name {
		font-weight: 600;
		font-size: 36rpx;
		color: #FFF;
	}
	
	.m-but-10 {
		box-shadow: 0px -1px 11px 4px rgba(27, 255, 216, 0.2);
		background: linear-gradient(90.00deg, rgb(112, 255, 86),rgb(79, 243, 247) 100%);
	}
	
	.m-but-9 {
		box-shadow: 0px -1px 11px 4px rgba(84, 131, 255, 0.36);
		background: linear-gradient(180.00deg, rgb(90, 244, 255) 0.763%,rgb(83, 121, 255) 100%);
	}
	
	.m-but-8 {
		box-shadow: 0px -1px 11px 4px rgba(188, 63, 243, 0.47);
		background: linear-gradient(180.00deg, rgb(213, 95, 253) 0.763%,rgb(168, 37, 234) 100%);
	}
	
	.m-but-7 {
		box-shadow: 0px -1px 11px 4px rgba(121, 48, 235, 0.56);
		background: linear-gradient(180.00deg, rgb(159, 95, 253),rgb(100, 23, 226) 100%);
	}
	
	.m-but-6 {
		box-shadow: 0px -1px 11px 4px rgba(127, 27, 255, 0.2);
		background: linear-gradient(180.00deg, rgb(94, 246, 199),rgb(45, 228, 183) 100%);
	}
	
	.m-but-5 {
		box-shadow: 0px -1px 11px 4px rgba(239, 255, 27, 0.2);
		background: linear-gradient(180.00deg, rgb(255, 223, 95) 0.763%,rgb(247, 222, 3) 100%);
	}
	
	.m-but-4 {
		box-shadow: 0px -1px 11px 4px rgba(27, 255, 216, 0.2);
		background: linear-gradient(180.00deg, rgb(100, 254, 197),rgb(22, 226, 194) 100%);
	}
	
	.m-but-3 {
		box-shadow: 0px -1px 11px 4px rgba(255, 35, 27, 0.2);
		background: linear-gradient(180.00deg, rgb(255, 117, 59),rgb(254, 49, 52) 100%);
	}
	
	.m-but-2 {
		box-shadow: 0px -1px 11px 4px rgba(27, 224, 255, 0.2);
		background: linear-gradient(180.00deg, rgb(59, 187, 255),rgb(10, 147, 220) 100%);
	}
	
	.m-but-1 {
		box-shadow: 0px -1px 11px 4px rgba(127, 27, 255, 0.2);
		background: linear-gradient(180.00deg, rgb(163, 94, 246),rgb(129, 27, 254) 100%);
	}
	
	.m-but {
		width: 170rpx;
		border-radius: 100rpx;
		text-align: center;
		padding: 22rpx 0;
		color: #FFF;
		font-weight: 600;
	}
	
	.color_C3C2C2 {
		color: #C3C2C2;
		font-size: 26rpx;
	}
	
	.img-147 {
		width: 60rpx;
		height: 60rpx;
		margin-right: 16rpx;
	}
	
	.list-public {
		background-color: #181818;
		padding: 30rpx 20rpx;
	}
	
	page {
		border-top: none;
		background-color: #000;
	}
	
</style>
