<template>
	<view>
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'405.png'+')'}">

			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_30rpx" @click="navig()">
					热点追踪
				</view>
			</view>

			<view class="display-a" style="padding: 0 26rpx 34rpx;">
				<image class="img-427" :src="imgUrl+'427.png'"></image>
				<view class="color_FFFFFF">每一个小时更新一次</view>
				<image @click="getHotspots()" class="img-427 margin-left-auto" :src="imgUrl+'428.png'"></image>
				<view @click="getHotspots()" class="color_12E3A2">换一换</view>
			</view>

			<scroll-view :scroll-y="true" :style="{'height': ''+windowHeight+'rpx'}">
				<block v-for="(item,index) in list" :key="index">
					<view @click="getAccountInfo(item.word)" class="display-a list-data"
						:class="index == 0 ? 'list-data-1' : index == 1 ? 'list-data-2' : index == 2 ? 'list-data-3' : 'list-data-4'">
						<image v-if="index == 0" class="img-424" :src="imgUrl+'424.png'"></image>
						<image v-if="index == 1" class="img-424" :src="imgUrl+'425.png'"></image>
						<image v-if="index == 2" class="img-424" :src="imgUrl+'426.png'"></image>
						<view v-if="index > 2" class="color_AFAFAF img-424 text-align_center">{{index+1}}</view>
						<view class="font-size_30rpx font-overflow" style="width: 450rpx;">{{item.word}}</view>
						<view class="color_AFAFAF margin-left-auto">{{item.hotindex}}</view>
						<image class="img-429" :src="imgUrl+'429.png'"></image>
					</view>

				</block>
				<view class="h_30rpx"></view>
			</scroll-view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

				imgUrl: this.$imgUrl,

				heightSystemss: '',
				statusBarHeightss: '',
				windowHeight: '',

				list: [],

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				isWhether: true, //判断重复点击

			}
		},

		onLoad() {
			this.getSystemInfo();
		},

		onShow() {
			this.getHotspots();
		},

		methods: {

			//查询点数是否足够
			async getAccountInfo(word) {

				if (uni.getStorageSync('uid')) {
					// uni.showModal({
					// 	content: this.tallySetObj.ai_create_deduct + '点/1次,是否确认?',
					// 	cancelText: "取消",
					// 	confirmText: "确认",
					// 	success: async (res) => {
					// 		if (res.confirm) {
					if (!this.isWhether) {
						return;
					}
					this.isWhether = false;

					const result = await this.$http.post({
						url: this.$api.accountInfo,
						data: {
							type: 1,
							uid: uni.getStorageSync('uid'),
						}
					});
					if (result.errno == 0) {
						this.getCopyImitation(word);
					} else {
						this.isWhether = true;
						if (result.errno == -2) {
							uni.showModal({
								content: result.message,
								cancelText: "取消",
								confirmText: "去开通",
								success: (res) => {
									if (res.confirm) {
										uni.navigateTo({
											url: '/pages/my/member'
										})
									} else if (res.cancel) {

									}
								}
							})
						} else {
							this.$sun.toast(result.message, 'none');
						}
					}
					// } else if (res.cancel) {

					// 		}
					// 	}
					// })
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "返回",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.redirectTo({
									url: '/pages/auth/auth?type=1'
								})
								// uni.navigateTo({
								// 	url: '/pages/auth/auth?type=1'
								// })
							} else if (res.cancel) {
								this.navig();
							}
						}
					})
				}

			},

			//一键仿写
			async getCopyImitation(word) {
				const result = await this.$http.post({
					url: 'https://vapi.weijuyunke.com/api/doBao/copyImitation',
					data: {
						content: '请根据【' + word + '】热点内容写1条长度500字以内的文案，不要附加话题，只生成纯文案。',
						ipStatus: 2,
						domainName: 'vapi.weijuyunke.com',
						requestIp: '**************'
					},
					mask: true,
					title: '请求中...'
				});
				if (result.code == 200) {
					this.getAICreation(result.data, word);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//生成AI文案
			async getAICreation(answer, word) {

				const result = await this.$http.post({
					url: this.$api.aiCreateAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: "热点追踪一键仿写",
						type: 17,
						question: this.msgText, //拼接的文本
						answer: answer, // 接口返回的文本
						words: answer.length
					}
				});
				if (result.errno == 0) {
					this.isWhether = true;
					uni.setStorageSync("answer", answer);
					uni.navigateTo({
						url: '/pages/index/hotspot/detail?name=' + word
					})
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//热点
			async getHotspots() {

				const result = await this.$http.post({
					url: this.$api.getHotspot
				});
				if (result.errno == 0) {
					this.list = result.data;
				} else {
					this.$sun.toast(result.message, 'none');
				}

			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
						this.windowHeight = res.windowHeight * 2 - 276;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	.img-424 {
		width: 48rpx;
		height: 48rpx;
		margin-right: 14rpx;
	}

	.list-data-1 {
		background: linear-gradient(90.00deg, rgba(255, 155, 155, 0.51), rgba(255, 255, 255, 0) 100%);
	}

	.list-data-2 {
		background: linear-gradient(78.57deg, rgba(49, 63, 89, 1), rgba(255, 255, 255, 0) 100%);
	}

	.list-data-3 {
		background: linear-gradient(90.00deg, rgba(67, 65, 61, 1), rgba(255, 255, 255, 0) 100%);
	}

	.list-data-4 {
		background: rgba(25, 28, 37, 1);
	}

	.list-data {
		width: 750rpx;
		padding: 30rpx 24rpx;
		margin-bottom: 20rpx;
		color: #fff;
	}

	.img-429 {
		width: 34rpx;
		height: 34rpx;
	}

	.img-427 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 10rpx;
	}

	.bg {
		width: 750rpx;
		height: 580rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 200rpx 0 0;
	}

	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		display: flex;
		align-items: center;
	}

	page {
		border: none;
		background: rgba(48, 48, 48, 1);
		width: 100%;
		overflow-x: hidden !important;
	}
</style>