<template>
	<view>
		<!-- <block v-if="isPrivacy">
			<xh-privacy title="隐私保护指引" theme="direction" @onHandleAgree="onHandle" background="rgba(0, 0, 0, .5)"
				color="#12CDC2"></xh-privacy>
		</block> -->
		<!-- <block v-else> -->

		<block v-if="template_type == 1 || template_type == 2">
			<swiper class="swiper-1" :autoplay="autoplay" :circular="true" :interval="interval" :duration="duration">
				<swiper-item v-for="(item,index) in banner" :key="index">
					<image class="pic-img" :src="item.pic_url" @click="changeUrl(index,item)"></image>
				</swiper-item>
			</swiper>
		</block>

		<block v-if="template_type == 5 || template_type == 4">
			<swiper class="swiper-5" :autoplay="autoplay" :circular="true" :interval="interval" :duration="duration">
				<swiper-item v-for="(item,index) in banner" :key="index">
					<image class="pic-img-5" :src="item.pic_url" @click="changeUrl(index,item)"></image>
				</swiper-item>
			</swiper>
		</block>

		<block v-if="template_type == 4">
			<view class="display-a-jc i-but-3" @click="getMiddle()"
				v-if="cloneSet.separation_swich == 1 && twoOpen == 1">
				<image class="img-308" :src="imgUrl+'308.png'"></image>
				<view class="font-size_32rpx font-weight_bold">一键分身</view>
			</view>
			<view class="h_20rpx" v-else></view>
			<view class="display-a-js margin_20rpx">
				<view style="width: 344rpx;">
					<image class="img-3" mode="widthFix" @click="changeUrl(0,serviceList[0])"
						:src="serviceList[0].pic_url"></image>
				</view>
				<view style="width: 344rpx;">
					<image class="img-3 margin-bottom_30rpx" mode="widthFix" @click="changeUrl(1,serviceList[1])"
						:src="serviceList[1].pic_url"></image>
					<image class="img-3" mode="widthFix" @click="changeUrl(2,serviceList[2])"
						:src="serviceList[2].pic_url"></image>
				</view>
				<!-- <block v-for="(item,index) in serviceList" :key="index">
						<view v-if="index == 0">
							<image class="img-2" mode="widthFix" @click="changeUrl(index,item)" :src="item.pic_url"></image>
						</view>
						<view v-else>
							<image class="img-2" mode="widthFix" @click="changeUrl(index,item)" :src="item.pic_url"></image>
						</view>
					</block> -->
			</view>
			<view class="margin_0_20rpx">
				<view class="color_FFFFFF font-size_32rpx font-weight_bold margin-bottom_20rpx">热门助手</view>
				<view class="display-fw-js">
					<block v-for="(item, index) in navigationList" :key="index">
						<image @click="changeUrl(index, item)" class="img-2" mode="widthFix" :src="item.pic_url">
						</image>
					</block>
				</view>
			</view>
			<view class="h_10rpx"></view>
		</block>

		<block v-if="template_type == 5">
			<view style="background-color: #020001">
				<view class="display-a-jc i-but-2" @click="getMiddle()"
					v-if="cloneSet.separation_swich == 1 && twoOpen == 1">
					<image class="img-308" :src="imgUrl + '308.png'"></image>
					<view class="font-size_32rpx font-weight_bold">一键分身</view>
				</view>
				<view class="h_20rpx" v-else></view>
				<view class="display-fw-js margin_0_20rpx">
					<block v-for="(item, index) in serviceList" :key="index">
						<image class="img-2" mode="widthFix" @click="changeUrl(index, item)" :src="item.pic_url">
						</image>
					</block>
				</view>
				<view class="display-fw-js margin_0_20rpx">
					<block v-for="(item, index) in navigationList" :key="index">
						<image @click="changeUrl(index, item)" class="img-2" mode="widthFix" :src="item.pic_url">
						</image>
					</block>
				</view>
				<view class="h_10rpx"></view>
			</view>
		</block>

		<block v-if="template_type == 3">
			<view :style="{
          height: '' + heightSystemss + 'px',
          top: '' + statusBarHeightss + 'px',
          lineHeight: '' + heightSystemss + 'px',
          left: '' + 10 + 'px',
        }" class="iconDizhssi">
				{{ systems.name }}
			</view>
			<view style="height: 190rpx"></view>
			<swiper class="swiper-2" :autoplay="autoplay" :circular="true" :interval="interval" :duration="duration">
				<swiper-item v-for="(item, index) in banner" :key="index">
					<image class="pic-img-2" :src="item.pic_url" @click="changeUrl(index, item)"></image>
				</swiper-item>
			</swiper>
		</block>

		<block v-if="template_type == 1">
			<view class="display-a-jc i-but" @click="getMiddle()" v-if="cloneSet.separation_swich == 1 && twoOpen == 1">
				<image class="img-205" :src="imgUrl + '205.png'"></image>
				<view class="font-size_32rpx">一键分身</view>
			</view>
			<view class="h_20rpx" v-else></view>

			<view class="display-fw-js margin_0_20rpx">
				<block v-for="(item, index) in serviceList" :key="index">
					<image class="img-2" mode="widthFix" @click="changeUrl(index, item)" :src="item.pic_url"></image>
				</block>
			</view>
		</block>

		<block v-if="template_type == 2">
			<view class="display-fw-js" style="margin: -50rpx 20rpx 0">
				<block v-for="(item, index) in serviceList" :key="index">
					<image class="img-2" mode="widthFix" @click="changeUrl(index, item)" :src="item.pic_url"></image>
				</block>
			</view>
		</block>
		<block v-if="template_type == 3 || template_type == 2 || template_type == 1">
			<!-- <view class="display-a" style="margin: 20rpx 20rpx 40rpx">
				<view class="a-line"></view>
				<view class="font-size_32rpx font-weight_bold color_FFFFFF">常用工具</view>
			</view> -->
			<view class="display-fw-a margin-bottom_20rpx">
				<block v-for="(item, index) in navigationList" :key="index">
					<view class="width_186rpx-center margin-bottom_20rpx" @click="changeUrl(index, item)">
						<image class="img-4" :src="item.pic_url"></image>
						<view class="color_FFFFFF">{{ item.name }}</view>
					</view>
				</block>
			</view>
		</block>
		<block v-if="template_type == 3">
			<view class="grid-style" style="margin: 0 20rpx; padding: 0 6rpx;">
				<image v-for="(item, index) in serviceList" :key="index" class="img-2"
					:class="index === 0 ? 'big-img-2' : ''"
					:style="index === 0 ? 'grid-area: item1;' : index === 1 ? 'grid-area: item2;' : 'grid-area: item3;'"
					@click="changeUrl(index, item)" :src="item.pic_url"></image>
			</view>
		</block>




		<block>
			<view class="a-top">
				<view class="display-a margin-bottom_30rpx">
					<image class="line-img" :src="imgUrl + '436.png'">
					</image>
					<view class="font-size_32rpx color_FFFFFF">定位 · 用户极准</view>
				</view>
				<view class="display-fw-js">
					<view class="a-frame display-a" @click="getAdd(1)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '437.png'">
						</image>
						<view class="text-view">
							<view class="text">
								我的定位
							</view>
						</view>
					</view>
					<!-- <view class="a-frame display-a" @click="getAdd(2)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '438.png'">
						</image>
						<view class="text-view">
							<view class="text">
								待办事项
							</view>
						</view>
					</view> -->
					<view class="a-frame display-a" @click="getAdd(3)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '439.png'">
						</image>
						<view class="text-view">
							<view class="text">
								目标人群
							</view>
						</view>
					</view>
					<!-- <view class="a-frame display-a" @click="getAdd(4)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '440.png'">
						</image>
						<view class="text-view">
							<view class="text">
								营销节点
							</view>
						</view>
					</view> -->
				</view>
			</view>

			<view class="m-frame" v-if="imgList.length > 0">
				<view class="display-a margin-bottom_30rpx">
					<image class="line-img" :src="imgUrl + '436.png'">
					</image>
					<view class="font-size_32rpx color_FFFFFF">精心为您挑选热门的形象</view>
				</view>
				<view class="display-fw-a">
					<block v-for="(item,index) in imgList" :key="index">

						<view>
							<view class="frame">
								<image @click="getVideo(item)" class="r-video" mode="widthFix" :src="item.video_cover">
								</image>
							</view>
							<view class="frame-title">
								<view style="width: 320rpx;"
									class="font-overflow font-size_30rpx color_FFFFFF margin-bottom_10rpx">
									{{item.name}}
								</view>
							</view>
						</view>

					</block>
				</view>
			</view>

			<view class="a-top">
				<view class="display-a margin-bottom_30rpx">
					<image class="line-img" :src="imgUrl + '436.png'">
					</image>
					<view class="font-size_32rpx color_FFFFFF">公域 · 获客极快</view>
				</view>
				<view class="display-fw-js">
					<view class="a-frame display-a" @click="getAdd(5)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '441.png'">
						</image>
						<view class="text-view">
							<view class="text">
								内容选题
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(6)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '442.png'">
						</image>
						<view class="text-view">
							<view class="text">
								文案编导
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(7)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '443.png'">
						</image>
						<view class="text-view">
							<view class="text">
								视频剪辑师
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(8)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '444.png'">
						</image>
						<view class="text-view">
							<view class="text">
								视频发布员
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(9)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '445.png'">
						</image>
						<view class="text-view">
							<view class="text">
								AI流量厂长
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(17)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '446.png'">
						</image>
						<view class="text-view">
							<view class="text">
								创作小组
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(18)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '454.png'">
						</image>
						<view class="text-view">
							<view class="text">
								小红书文案
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(19)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '455.png'">
						</image>
						<view class="text-view">
							<view class="text">
								爆款跟拍
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="a-top">
				<view class="display-a margin-bottom_30rpx">
					<image class="line-img" :src="imgUrl + '436.png'">
					</image>
					<view class="font-size_32rpx color_FFFFFF">私域 · 销转极高</view>
				</view>
				<view class="display-fw-js">
					<view class="a-frame display-a" @click="getAdd(10)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '447.png'">
						</image>
						<view class="text-view">
							<view class="text">
								私域营销管家
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(11)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '448.png'">
						</image>
						<view class="text-view">
							<view class="text">
								活动策划专家
							</view>
						</view>
					</view>
					<!-- <view class="a-frame display-a" @click="getAdd(12)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '449.png'">
						</image>
						<view class="text-view">
							<view class="text">
								客资转化专家
							</view>
						</view>
					</view> -->
				</view>
			</view>
			<view class="a-top">
				<view class="display-a margin-bottom_30rpx">
					<image class="line-img" :src="imgUrl + '436.png'">
					</image>
					<view class="font-size_32rpx color_FFFFFF">AI员工 · 成本极省</view>
				</view>
				<view class="display-fw-js">
					<view class="a-frame display-a" @click="getAdd(13)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '450.png'">
						</image>
						<view class="text-view">
							<view class="text">
								图片生成
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(14)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '451.png'">
						</image>
						<view class="text-view">
							<view class="text">
								法务专员
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(15)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '452.png'">
						</image>
						<view class="text-view">
							<view class="text">
								会议记录
							</view>
						</view>
					</view>
					<view class="a-frame display-a" @click="getAdd(20)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '456.png'">
						</image>
						<view class="text-view">
							<view class="text">
								AI直播
							</view>
						</view>
					</view>
					<!-- <view class="a-frame display-a" @click="getAdd(16)">
						<image class="margin-bottom_40rpx bgc" :src="imgUrl + '453.png'">
						</image>
						<view class="text-view">
							<view class="text">
								员工培训
							</view>
						</view>
					</view> -->
				</view>
			</view>
		</block>



		<sunui-tabbar :fixed="true" :current="tabIndex" :types="1" tintColor="#E496FD"
			backgroundColor="#1B1B1B"></sunui-tabbar>
		<!-- </block> -->

		<block v-if="customerConfig.customer_type == 'on_line'">
			<button open-type="contact">
				<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl + 'kefu.gif'">
				</image>
			</button>
		</block>
		<block v-else>
			<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl + 'kefu.gif'">
			</image>
		</block>

		<sunui-popup ref="pop5">
			<template v-slot:content>
				<image show-menu-by-longpress class="img-qr-code" :src="customerConfig.customer_qr_code"></image>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop6">
			<template v-slot:content>
				<view style="overflow: auto; padding: 20rpx">
					<scroll-view :scroll-y="true" style="height: 600rpx">
						<rich-parser :html="kfSet.upload_describe"
							domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load ref="article" selectable
							show-with-animation use-anchor>
							<!-- 加载中... -->
						</rich-parser>
					</scroll-view>
				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="rescuePop">
			<template v-slot:content>
				<image @click="getNewUser()" class="img-new" :src="userSet.new_pic"></image>
			</template>
		</sunui-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {

				isPrivacy: uni.getStorageSync("privacy") ? false : true,

				isOpen: '', // 1未弹出 2已弹出

				tabIndex: 0,

				// 轮播图
				indicatorDots: true,
				autoplay: true,
				interval: 4000,
				duration: 500,

				heightSystemss: '',
				statusBarHeightss: '',

				banner: [],

				imgUrl: this.$imgUrl,

				systems: {},

				userSet: {}, //新人设置

				user: {},

				windowHeight: '',

				navigationList: [], //分类列表
				serviceList: [], //服务列表


				isWhether: true, //判断重复点击

				is_open: '', //分销 1开启

				twoOpen: '2', // 线路二 1开启

				partner_is_open: '', //合伙人 1开启

				template_type: '', //首页模板

				cloneSet: {},

				isKefu: true, //true隐藏 false展开

				customerConfig: {}, //客服配置

				kfSet: {}, //更新公告

				imgList: [],

			}
		},

		onLoad(options) {
			this.getNavigation();
			this.getService();
			this.getSystemInfo();
			this.getBanner();
			this.indexkfSet();
			if (options.scene) {
				let scene = options.scene;
				uni.setStorageSync('pid', scene);
			}
			if (uni.getStorageSync('uid')) {
				this.userInfo();
			}
			this.getImgList();
		},

		onShow() {

			this.getCustomerConfig();
			this.getSystem();
			this.getTallySet();
			this.getBrokerageSet();
			this.getPartnerSet();
			this.getIndexSet();
			this.getCloneSet();
			this.getIndexWay();
		},

		methods: {
			//购买形象
			getVideo(item) {
				//高级 快速版
				let param = {
					base_video: item.video_url,
					id: item.id
				};

				uni.navigateTo({
					url: '/pages/index/videos?type=1&param=' + encodeURIComponent(JSON.stringify(param))
				})
			},
			//热门形象
			async getImgList() {

				const result = await this.$http.get({
					url: this.$api.getAvatarList
				});
				if (result.errno == 0) {

					this.imgList = result.data.list;

				}
			},
			getAiTitle() {
				uni.navigateTo({
					url: "/pages/index/AICreation/aiTitle",
				});
			},

			async getAdd(type) {
				if (uni.getStorageSync("uid")) {
					if (type === 1) {
						uni.navigateTo({
							url: "/subPackages/subPackageA/report?type=1",
						});
						return
					} else if ([2, 4, 12, 16].includes(type)) {
						this.$sun.toast('开发中', 'none');
						return
					}

					let res = await this.$http.post({
						url: this.$api.getAIProjectResult,
						data: {
							uid: uni.getStorageSync('uid')
						}
					});
					if (res.errno === 0) {
						if (res.data) {
							if (type === 3) {
								uni.navigateTo({
									url: "/subPackages/subPackageA/report?type=3",
								});
							} else if (type === 7) {
								uni.navigateTo({
									url: "/pages/edit/edit",
								});
							} else if (type === 8) {
								uni.navigateTo({
									url: "/pages/matrix/matrix",
								});
							} else if (type === 9) {
								uni.navigateTo({
									url: "/subPackages/subPackageA/generationsImg?title=DeepSeek&type=" + 9,
								});
							} else if (type === 13) {
								uni.navigateTo({
									url: "/subPackages/subPackageA/generationsImg?title=AI设计师&type=" + 14,
								});
							} else if (type === 14) {
								uni.navigateTo({
									url: "/subPackages/subPackageA/generationsImg?title=法务专员&type=" + 8,
								});
							} else if (type === 15) {
								uni.navigateTo({
									url: "/subPackages/subPackageA/meeting",
								});
							} else if (type === 17) {
								uni.navigateTo({
									url: "/subPackages/subPackageA/generationsImg?title=群聊会话&type=" + 13,
								});
							} else if (type === 18) {
								uni.navigateTo({
									url: "/subPackages/subPackageA/generationsImg?title=小红书文案&type=" + 15,
								});
							} else if (type === 19) {
								uni.navigateTo({
									url: "/pages/index/selling/selling",
								});
							} else if (type === 20) {
								uni.navigateTo({
									url: '/pages/my/course'
								})
							} else {
								uni.navigateTo({
									url: "/subPackages/subPackageA/twoPage?type=" + type,
								});
							}
						} else {
							uni.showModal({
								content: "请先完成定位",
								cancelText: "取消",
								confirmText: "去定位",
								success: (res) => {
									if (res.confirm) {
										uni.navigateTo({
											url: "/subPackages/subPackageA/report?type=1",
										});
									} else if (res.cancel) {
										// this.navig();
									}
								},
							});
						}
					} else {
						this.$sun.toast(res.message, 'none');
					}

				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: "/pages/auth/auth?type=1",
								});
							} else if (res.cancel) {
								// this.navig();
							}
						},
					});
				}
			},

			getOpen() {
				this.$refs.pop6.show({
					style: "background-color:#fff;width:600rpx;border-radius:10rpx;",
					bottomClose: true,
					shadeClose: false,
				});
			},

			// 客服设置接口
			async indexkfSet() {
				const resolu = await this.$http.get({
					url: this.$api.kfSet
				})
				if (resolu.errno == 0) {
					this.kfSet = resolu.data;
					if (this.kfSet.upload_describe_swich == 1 && uni.getStorageSync("isOpen") != 2) {
						uni.setStorageSync('isOpen', 2);
						this.getOpen();
					}
				} else {
					this.$sun.toast(resolu.message, "none")
				}
			},

			getKefu() {
				if (this.isKefu) {
					this.isKefu = false;
				} else {
					if (this.customerConfig.customer_type == 'on_line') {
						return;
					} else if (this.customerConfig.customer_type == 'phone') {
						if (this.customerConfig.customer_phone) {
							this.$sun.phone(this.customerConfig.customer_phone);
						} else {
							this.$sun.toast("暂无联系方式", 'error');
						}
					} else if (this.customerConfig.customer_type == 'qr_code') {
						this.$refs.pop5.show({
							style: 'background-color:#000;width:600rpx;border-radius:10rpx;',
							bottomClose: true,
							shadeClose: false,
						});
					}
				}
			},

			//一键分身
			getMiddle() {

				uni.navigateTo({
					url: '/pages/index/synthesis/synthesis'
				})

			},

			//克隆设置
			async getCloneSet() {
				const result = await this.$http.post({
					url: this.$api.cloneSet
				});
				if (result.errno == 0) {
					this.cloneSet = result.data;
					uni.setStorageSync('cloneSet', result.data);
				}
			},

			//线路自定义名称
			async getIndexWay() {
				const result = await this.$http.post({
					url: this.$api.indexWay
				});
				if (result.errno == 0) {
					for (let i = 0; i < result.data.length; i++) {
						if (result.data[i].id == 2) {
							this.twoOpen = 1;
						}
					}
					uni.setStorageSync('indexWay', result.data);
				}
			},

			//首页模板
			async getIndexSet() {
				const result = await this.$http.post({
					url: this.$api.indexSet
				});
				if (result.errno == 0) {
					this.template_type = result.data.template_type;
					uni.setStorageSync('indexSet', result.data.template_type);
				}
			},

			//客服配置
			async getCustomerConfig() {
				const result = await this.$http.post({
					url: this.$api.customerConfig
				});
				if (result.errno == 0) {
					this.customerConfig = result.data;
					uni.setStorageSync('customerConfig', result.data);
				}
			},

			//扣点设置
			async getTallySet() {

				const result = await this.$http.post({
					url: this.$api.tallySet
				});
				if (result.errno == 0) {
					uni.setStorageSync('tallySetObj', result.data);
				}
			},

			//导航
			async getNavigation() {
				const result = await this.$http.post({
					url: this.$api.navigation
				});
				if (result.errno == 0) {
					this.navigationList = result.data;
				}
			},

			//分销设置
			async getBrokerageSet() {
				const result = await this.$http.post({
					url: this.$api.brokerageSet,
				});
				if (result.errno == 0) {
					this.is_open = result.data.is_open;
				}
			},

			//合伙人设置
			async getPartnerSet() {
				const result = await this.$http.post({
					url: this.$api.partnerSet
				});
				if (result.errno == 0) {
					this.partner_is_open = result.data.is_open;
				}
			},

			//导航
			async getService() {
				const result = await this.$http.post({
					url: this.$api.service
				});
				if (result.errno == 0) {
					this.serviceList = result.data;
				}
			},

			isVideoLink(url) {
				return /\.(mp4|mov|avi|mkv)$/i.test(url);
			},

			isImageLink(url) {
				return /\.(jpg|jpeg|png|gif)$/i.test(url);
			},

			onHandle(e) {
				this.isPrivacy = e;
			},

			//领取新人奖励
			async getNewUser() {
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.userNewGet,
					data: {
						uid: uni.getStorageSync('uid'),
					}
				});
				if (result.errno == 0) {
					this.$refs.rescuePop.close();
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.userInfo();
						this.isWhether = true;
					}, 2000);

				} else {
					this.$refs.rescuePop.close();
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//轮播图
			async getBanner() {
				const result = await this.$http.post({
					url: this.$api.banner,
					data: {
						b_type: 1
					}
				});
				if (result.errno == 0) {
					this.banner = result.data;
				}
			},

			//用户信息
			async userInfo() {
				const result = await this.$http.post({
					url: this.$api.userInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.user = result.data;
					uni.setStorageSync('isMember', result.data.is_member);
					if (result.data.is_new == 1) {
						this.getUserSet();
					}
				}
			},

			//新人设置
			async getUserSet() {
				const result = await this.$http.post({
					url: this.$api.userSet
				});
				if (result.errno == 0) {
					this.userSet = result.data;
					if (this.userSet.new_open == 1) {
						this.newPop();
					}
				}
			},

			//新人弹窗
			newPop() {
				this.$refs.rescuePop.show({
					style: 'width:700rpx; height:650rpx;border-radius: 5px;',
					anim: 'center',
					shadeClose: false, //使用户不能点击其它关闭页面
					bottomClose: true,
					rgba: 'rgba(50,50,50,.6)'
				});
			},

			//自定义跳转
			changeUrl(index, values) {
				if (values.type == 1) {
					if (values.url == "/pages/my/partner/index") {
						if (this.partner_is_open != 1) {
							uni.showModal({
								content: "该功能暂未开放,尽请期待!",
								confirmText: "确认",
								showCancel: false,
								success: (res) => {
									if (res.confirm) {} else if (res.cancel) {}
								},
							});
							return;
						}
						this.getPartner();
					} else {
						if (
							values.url == "/pages/my/distribution/distribution" &&
							this.is_open != 1
						) {
							uni.showModal({
								content: "该功能暂未开放,尽请期待!",
								confirmText: "确认",
								showCancel: false,
								success: (res) => {
									if (res.confirm) {} else if (res.cancel) {}
								},
							});
							return;
						}

						// if (values.url === "/pages/index/AICreation/AICreation") {
						// 	this.getInfo((result) => {
						// 		if (!result.data) {
						// 			uni.showModal({
						// 				content: "使用AI文案，请先填写个人信息",
						// 				confirmText: "确认",
						// 				success: (res) => {
						// 					if (res.confirm) {
						// 						uni.navigateTo({
						// 							url: "/pages/my/userInfo",
						// 						});
						// 					} else if (res.cancel) {}
						// 				},
						// 			});
						// 		} else {
						// 			uni.navigateTo({
						// 				url: "/pages/index/AICreation/AICreation",
						// 			});
						// 		}
						// 	});
						// 	return;
						// }

						uni.navigateTo({
							url: values.url,
						});
					}
				}
				if (values.type == 2) {
					if (values.appid) {
						wx.navigateToMiniProgram({
							appId: values.appid,
							success: (res) => {
								// 打开成功
								// console.log('成功', res);
							},
							fail: (err) => {
								// console.log('失败', err);
							},
						});
					} else {
						this.$sun.toast("请检查跳转外部小程序的APPID是否正确", "none");
					}
				}
			},

			// 是否填写个人信息
			async getInfo(succCallBack) {
				const result = await this.$http.get({
					url: this.$api.getUserInfo,
					data: {
						uid: uni.getStorageSync("uid"),
					},
				});
				if (result.errno == 0) {
					succCallBack(result);
				} else {
					this.$sun.toast(result.message, "none");
				}
			},

			//合伙人
			getPartner() {
				if (uni.getStorageSync("uid")) {
					//正常
					if (this.user.partner_status == 2 && this.user.partner_freeze == 1) {
						uni.setStorageSync("partnerId", this.user.partner_id);
						uni.navigateTo({
							url: "/pages/my/partner/index",
						});
					}
					//冻结
					if (this.user.partner_status == 2 && this.user.partner_freeze == 2) {
						uni.showModal({
							content: "您的合伙人已冻结,如有疑问请联系管理员!",
							confirmText: "确认",
							showCancel: false,
							success: (res) => {
								if (res.confirm) {

								} else if (res.cancel) {

								}
							}
						})
					}
					//未申请
					if (this.user.partner_status == 0) {
						uni.navigateTo({
							url: '/pages/my/partner/partner'
						})
					}
					//审核中
					if (this.user.partner_status == 1) {
						uni.showModal({
							content: "您的申请正在审核中,请耐心等待!",
							confirmText: "确认",
							showCancel: false,
							success: (res) => {
								if (res.confirm) {

								} else if (res.cancel) {

								}
							}
						})
					}
					//驳回
					if (this.user.partner_status == 3) {
						uni.showModal({
							title: '审核驳回',
							content: this.user.partner_refuse,
							cancelText: "确认",
							confirmText: "重新申请",
							success: (res) => {
								if (res.confirm) {
									this.updataStatus();
								} else if (res.cancel) {

								}
							}
						})
					}

				} else {
					uni.navigateTo({
						url: '/pages/auth/auth?type=1'
					})
				}
			},

			//合伙人状态变更
			async updataStatus() {
				const result = await this.$http.post({
					url: this.$api.partnerUpdateStatus,
					data: {
						partner_id: this.user.partner_id,
						name: this.user.partner_name,
						telphone: this.user.partner_telphone
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message, 'none');
				} else {
					this.$sun.toast("认证状态变更失败", 'none');
				}
			},

			//系统设置
			async getSystem() {
				const result = await this.$http.post({
					url: this.$api.system
				});
				if (result.errno == 0) {
					this.systems = result.data;
					this.$sun.title(this.systems.name);
					uni.setStorageSync('system', result.data);
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
						// this.windowHeight = res.windowHeight * 2 - 920;
					},
					fail(err) {
						// console.log(err);
					},
				});
			},
		},
	};
</script>

<style lang="scss">
	.frame-title {
		background: linear-gradient(180.00deg, rgb(40, 36, 48), rgb(26, 26, 26) 100%);
		padding: 10rpx;
		width: 344rpx;
		border-radius: 0 0 10rpx 10rpx;
		margin-bottom: 20rpx;
	}

	.r-video {
		width: 344rpx;
		border-radius: 10rpx 10rpx 0 0;
		position: absolute;
		z-index: 2;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.frame {
		position: relative;
		width: 344rpx;
		height: 612rpx;
		border-radius: 10rpx 10rpx 0 0;
		margin-right: 10rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: block;
		/* 避免底部空白 */
	}

	.m-frame {
		width: 710rpx;
		border-radius: 20rpx;
		margin: 20rpx;
	}

	.grid-style {
		display: grid;
		grid-template-columns: 340rpx 340rpx;
		grid-template-rows: auto auto;
		grid-template-areas:
			"item1 item2"
			"item1 item3";
		gap: 20rpx;

		.img-2 {
			width: 100%;
			height: 154rpx;
			margin-bottom: 0;
		}

		.big-img-2 {
			height: 328rpx;
			/* 两个普通图片高度加上间隔 */
			object-fit: cover;
		}
	}

	.a-top {
		width: 710rpx;
		margin: 0 20rpx;
		margin-top: 40rpx;
		padding: 0 6rpx;
	}

	.line-img {
		width: 27rpx;
		height: 32rpx;
		margin-right: 6rpx;
	}

	.a-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180deg,
				rgb(109, 221, 245),
				rgb(66, 72, 244) 100%);
		margin-right: 14rpx;
	}

	.img-25 {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}

	.a-frame {
		position: relative;
		width: 340rpx;
		height: 154rpx;
		background-color: #2E3540;
		border-radius: 10rpx;
		margin-bottom: 18rpx;
		flex-direction: column;
		justify-content: space-around;

		.bgc {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			height: 100%;
			width: 100%;
			border-radius: 20rpx;
		}

		.text-view {
			position: absolute;
			top: 50%;
			left: 24rpx;
			transform: translateY(-50%);
			font-weight: 800;

			color: #FFFFFF;
			line-height: 40rpx;

			.text {
				font-size: 32rpx;
			}
		}
	}

	.img-308 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 14rpx;
	}

	.i-but-3 {
		position: relative;
		z-index: 9;
		width: 336rpx;
		border-radius: 100rpx;
		background: linear-gradient(83.72deg, rgb(163, 255, 64) 0.206%, rgb(81, 255, 165) 61.051%, rgb(0, 255, 229) 99.84%);
		padding: 26rpx 0;
		color: #000;
		margin: -250rpx 30rpx 160rpx;
	}

	.i-but-2 {
		position: relative;
		z-index: 9;
		width: 580rpx;
		border-radius: 100rpx;
		background: linear-gradient(83.72deg, rgb(163, 255, 64) 0.206%, rgb(81, 255, 165) 61.051%, rgb(0, 255, 229) 99.84%);
		padding: 26rpx 0;
		color: #000;
		margin: -60rpx 84rpx 50rpx;
	}

	.img-qr-code {
		width: 500rpx;
		height: 500rpx;
		margin: 50rpx;
	}

	.kefu-2 {
		right: -50rpx;
		opacity: 0.5;
		transition: all 1s linear;
	}

	.kefu-1 {
		right: 10rpx;
		transition: all 1s linear;
	}

	.kefu {
		width: 100rpx;
		height: 100rpx;
		position: fixed;
		z-index: 99;
		// right: -50rpx;
		bottom: 340rpx;
	}

	.i-line {
		width: 8rpx;
		height: 28rpx;
		background-color: #9268FF;
		border-radius: 4rpx;
		margin-right: 12rpx;
	}

	.img-205 {
		width: 30rpx;
		height: 30rpx;
		margin-right: 12rpx;
	}

	.i-but {
		width: 650rpx;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(125, 108, 255) 0%, rgb(209, 92, 254) 100%);
		padding: 26rpx 0;
		color: #FFF;
		margin: -60rpx 50rpx 40rpx;
		position: relative;
		z-index: 1;
	}

	.img-new {
		width: 700rpx;
		height: 650rpx;
	}

	.img-4 {
		width: 125rpx;
		height: 125rpx;
	}

	.img-3 {
		width: 344rpx;
	}

	.img-2 {
		width: 340rpx;
		// height: 166rpx;
		margin-bottom: 20rpx;
	}

	.swiper-5 {
		width: 750rpx;
		height: 730rpx;
	}

	.pic-img-5 {
		width: 750rpx;
		height: 730rpx;
	}

	.pic-img-2 {
		width: 710rpx;
		height: 340rpx;
		// margin: 0 20rpx 44rpx;
		// margin-bottom: 44rpx;
		border-radius: 10rpx;
	}

	.pic-img {
		width: 750rpx;
		height: 950rpx;
		// margin: 0 20rpx 24rpx;
		// border-radius: 10rpx;
	}

	.swiper-2 {
		width: 710rpx;
		height: 340rpx;
		margin: 0 20rpx 44rpx;
	}

	.swiper-1 {
		width: 750rpx;
		height: 950rpx;
		// margin: 0 20rpx 20rpx;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		font-size: 32rpx;
		// font-weight: bold;
		color: #FFFFFF;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}
</style>