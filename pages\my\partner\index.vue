<template>
	<view>
		
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'41.png'+')'}">
			<view class="display-a">
				<!-- <image class="p-avatar" :src="partnerUser.avatar"></image> -->
				<image class="p-avatar" :src="imgUrl + '159.png'"></image>
				<view class="color_FFFFFF">
					<view class="font-size_32rpx margin-bottom_20rpx">{{partnerUser.name}}</view>
					<view class="font-size_26rpx">{{partnerUser.telphone}}</view>
				</view>
			</view>
		</view>
		
		<view class="p-frame">
			<view class="display-a-js margin_20rpx">
				<view class="p-icon">
					<view style="width: 300rpx;height: 300rpx;" class="margin-bottom_20rpx">
						<qiun-data-charts type="arcbar" :opts="opts" :chartData="chartData" />
					</view>
					<view class="display-a-js">
						<view class="text-align_center">
							<view class="color_B6B6B6">今日激活数</view>
							<view class="font-size_32rpx color_FFFFFF">{{partnerUser.cardUsedT}}</view>
						</view>
						<view class="text-align_center">
							<view class="color_B6B6B6">昨日激活数</view>
							<view class="font-size_32rpx color_FFFFFF">{{partnerUser.cardUsedY}}</view>
						</view>
					</view>
				</view>
				<view>
					<view class="p-count display-a margin-bottom_20rpx">
						<image class="img-50" :src="imgUrl + '50.png'"></image>
						<view >
							<view class="color_B6B6B6">累计卡密数</view>
							<view class="font-weight_bold font-size_38rpx color_FFFFFF">{{partnerUser.cardAll}}</view>
						</view>
					</view>
					<view class="p-count display-a margin-bottom_20rpx">
						<image class="img-50" :src="imgUrl + '51.png'"></image>
						<view >
							<view class="color_B6B6B6">已激活</view>
							<view class="font-weight_bold font-size_38rpx color_FFFFFF">{{partnerUser.cardUsed}}</view>
						</view>
					</view>
					<view class="p-count display-a">
						<image class="img-50" :src="imgUrl + '52.png'"></image>
						<view >
							<view class="color_B6B6B6">未激活</view>
							<view class="font-weight_bold font-size_38rpx color_FFFFFF">{{partnerUser.cardUnused}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="display-a" style="padding: 0 20rpx 30rpx;">
			<view class="color_FFFFFF font-size_32rpx font-weight_bold">购卡记录</view>
			<view class="see-all" @click="getAll()">查看全部</view>
			<image class="img-21" @click="getAll()" :src="imgUrl + '21.png'"></image>
		</view>
		
		<block v-if="list.length > 0">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a padding-bottom_20rpx p-bo margin-bottom_20rpx">
						<view class="color_ABABAB">订单号: {{item.log_no}}</view>
						<view class="o-copy" @click="getCopy(item.log_no)">复制</view>
					</view>
					<view class="display margin-bottom_40rpx">
						<image class="img-pic" :src="item.goods_pic"></image>
						<view style="width: 470rpx;">
							<view class="o-name">{{item.goods_name}}</view>
							<view class="color_cdcdcd display-a margin-bottom_40rpx">
								<view class="font-size_26rpx">已激活: {{item.already}}/{{item.quantity}}</view>
								<view class="font-size_26rpx margin-left-auto">数量: x{{item.count}}</view>
							</view>
							<view class="o-money">-<span class="font-size_26rpx">￥</span>{{Number(item.money)}}</view>
						</view>
					</view>
					<view class="display-a">
						<view class="o-but o-but-del" @click="delOrder(item.id,item.goods_name)">删除卡密</view>
						<view class="o-but o-but-see" @click="getSee(item.id,item.goods_name)">查看卡密</view>
					</view>
				</view>
			</block>
		</block>
		<mescroll-empty v-else></mescroll-empty>
		
		<sunui-tabbar :fixed="true" :current="tabIndex" :types="2" tintColor="#23E9FF" backgroundColor="#323232"></sunui-tabbar>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				tabIndex: 1,
				
				partnerUser: {},
				
				imgUrl: this.$imgUrl,
				
				chartData: {},
				//您可以通过修改 config-ucharts.js 文件中下标为 ['arcbar'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: undefined,
					title: {
						name: "0%",
						fontSize: 32,
						color: "#FFF"
					},
					subtitle: {
						name: "已激活占比",
						fontSize: 14,
						color: "#FFF"
					},
					extra: {
						arcbar: {
							type: "circle",
							width: 12,
							backgroundColor: "#2C2C2C",
							startAngle: 1.5,
							endAngle: 0.25,
							gap: 2,
							linearType: 'custom',
							customColor: ['#049FFF','#29F7FF'],
						}
					}
				},
				
				list: [],
				
			}
		},
		
		onLoad() {
			
		},
		
		onShow() {
			this.partnerInfo();
			this.getPartnerCardLogList();
		},
		
		methods: {
			
			//查看订单
			getAll() {
				uni.navigateTo({
					url: '/pages/my/partner/order'
				})
			},
			
			//查看
			getSee(id,name) {
				uni.navigateTo({
					url: '/pages/my/partner/orderDetail?logId='+id+'&logName='+name
				})
			},
			
			/*  删除  */
			delOrder(id,name) {
			
				uni.showModal({
					title: '提示',
					content: '确认删除' + name + '该卡密?',
					success: async (res) => {
						if (res.confirm) {
							const result = await this.$http.post({
								url: this.$api.partnerCardLogDel,
								data: {
									partner_id: uni.getStorageSync("partnerId"),
									log_id: id
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				});
			},
			
			//复制
			getCopy(text) {
				if (text) {
					uni.setClipboardData({
						data: text,
						success: () => {
							// 复制成功的回调
							this.$sun.toast('复制成功');
							// uni.showToast({
							// 	title: '复制成功',
							// 	icon: 'success',
							// 	duration: 4000
							// });
						},
						fail: (err) => {
							console.log("复制失败原因===>",err);
							// 复制失败的回调
							uni.showToast({
								title: '复制失败：'+err,
								icon: 'none'
							});
						}
					});
				}
			},
			
			//购买卡密记录
			async getPartnerCardLogList() {
				const result = await this.$http.post({
					url: this.$api.partnerCardLogList,
					data: {
						partner_id: uni.getStorageSync('partnerId'),
						log_no: '',
						is_status: '',
						page: 1,
						psize: 5
					}
				});
				if (result.errno == 0) {
					this.list = result.data.list;
				}
			},
			
			getServerData() {
				
				let calculation = 0;
				
				
				if (Number(this.partnerUser.cardUsed) > 0) {
					calculation = Number(this.partnerUser.cardUsed)/Number(this.partnerUser.cardAll)
				}
				
				// calculation = Math.floor(calculation);
				
				this.opts.title.name = Math.floor(Number(calculation)*100)+'%';
				
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
					let res = {
						series: [{
							name: "已激活占比",
							color: "#91CB74",
							data: Number(Number(calculation).toFixed(2))
						}]
					};
					this.chartData = JSON.parse(JSON.stringify(res));
				}, 500);
			},
			
			//用户信息
			async partnerInfo() {
				const result = await this.$http.post({
					url: this.$api.partnerIndex,
					data: {
						partner_id: uni.getStorageSync('partnerId')
					}
				});
				if (result.errno == 0) {
					this.partnerUser = result.data;
					this.getServerData();
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.o-but-see {
		border: 1px solid rgb(126, 233, 255);
		color: #7EE9FF;
		margin-left: 40rpx;
	}
	
	.o-but-del {
		border: 1px solid rgb(171, 171, 171);
		color: #ABABAB;
		margin-left: auto;
	}
	
	.o-but {
		width: 170rpx;
		text-align: center;
		padding: 10rpx 0;
		border-radius: 100rpx;
	}
	
	.o-money {
		color: #FF0000;
		font-size: 36rpx;
		font-weight: bold;
	}
	
	.color_cdcdcd {
		color: #CDCDCD;
	}
	
	.o-name {
		font-weight: bold;
		color: #FFF;
		margin-bottom: 20rpx;
		font-size: 32rpx;
	}
	
	.o-copy {
		width: 74rpx;
		text-align: center;
		color: #686767;
		padding: 4rpx 0;
		font-size: 24rpx;
		margin-left: 18rpx;
		background-color: #C4C4C4;
		border-radius: 10rpx;
	}
	
	.color_ABABAB {
		color: #ABABAB;
	}
	
	.img-pic {
		width: 180rpx;
		height: 180rpx;
		margin-right: 20rpx;
	}
	
	.p-bo {
		border-bottom: 1px solid rgb(79, 78, 78);
	}
	
	.list-public {
		background-color: #373737;
	}
	
	.see-all {
		color: #A1A1A1;
		margin-left: auto;
		font-size: 26rpx;
	}
	
	.img-21 {
		width: 20rpx;
		height: 20rpx;
		margin-left: 8rpx;
	}
	
	.color_B6B6B6 {
		color: #B6B6B6;
		font-size: 26rpx;
		margin-bottom: 12rpx;
	}
	
	.img-50 {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}
	
	.p-count {
		width: 340rpx;
		background-color: #323232;
		border-radius: 10rpx;
		padding: 20rpx;
	}
	
	.p-icon {
		width: 340rpx;
		height: 448rpx;
		background-color: #323232;
		border-radius: 10rpx;
		padding: 20rpx;
	}
	
	.p-frame {
		width: 750rpx;
		background-color: #1D1D1D;
		margin-top: -20rpx;
		border-radius: 20rpx 20rpx 0 0;
		padding-top: 20rpx;
		margin-bottom: 40rpx;
	}
	
	.p-avatar {
		width: 110rpx;
		height: 110rpx;
		border-radius: 100rpx;
		margin-right: 20rpx;
	}
	
	.bg {
		width: 750rpx;
		height: 260rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 60rpx 20rpx 0;
	}
	
	page {
		border: none;
		background: #1D1D1D;
	}
	
</style>
