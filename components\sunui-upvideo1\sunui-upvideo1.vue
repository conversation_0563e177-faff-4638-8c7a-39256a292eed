<template>
	<view class="sunui-add-list">
		
		<view
			class="sunui-add-list-items"
			:style="{
				height: size[0],
				width: size[1]
			}"
			v-for="(item, index) in imgLists"
			:key="index"
		>
			<video
				:style="{
					height: size[0],
					width: size[1]
				}"
				class="sunui-add-list-img"
				:id="'myVideo_' + index"
				:src="item.url"
				@play="videoPlay"
				@error="videoError"
				controls
				:enable-play-gesture="true"
				objectFit="contain"
			></video>

			<view class="sunui-add-list-remove sunui-icons icon-close" @tap.stop="removeImg" :id="'sunui-items-img-' + index">
				<icon type="clear" :color="closeBtnColor"></icon>
			</view>
			<view class="upload-progress" v-if="updatting && item.progress < 100" ><progress :percent="item.progress" :stroke-width="progressSize" :activeColor="progressColor" :backgroundColor="progressBGColor" /></view>
			<view class="sunui-add-list-reup" @tap.stop="retry" :data-index="index" v-if="item.error">
				<text class="sunui-add-list-reup-icon sunui-icons icon-retry"></text>
				<text class="sunui-add-list-reup-text">失败重试</text>
			</view>
		</view>
		<view
			class="sunui-add-list-items sunui-add-list-btn"
			:style="{
				height: size[0],
				width: size[1]
			}"
			@tap="addImg"
			v-if="imgLists.length < maxFileNumber"
		>
			<view class="sunui-add-list-btn-icon">+</view>
			<view class="sunui-add-list-btn-text">{{ btnName }}</view>
		</view>
	</view>
</template>
<script>
export default {
	props: {
		maxFileNumber: {
			type: Number,
			default: 9
		},
		btnName: {
			type: String,
			default: '添加视频'
		},
		// 图片大小
		size: {
			type: Array,
			default: () => ['220rpx', '220rpx']
		},
		// 上传前钩子
		beforeUpload: {
			type: Function
		},
		items: {
			type: Array,
			default: function() {
				return [];
			}
		},
		closeBtnColor: {
			type: String,
			default: '#666666'
		},
		alyUrl: {
			type: String,
			default: ''
		},
		progressSize: {
			type: Number,
			default: 1
		},
		progressColor: {
			type: String,
			default: '#27BD81'
		},
		progressBGColor: {
			type: String,
			default: '#F8F8F8'
		},
		fileName: { type: String, default: 'img' },
		formData: {
			type: Object,
			default: ()=> {
				return {};
			}
		},
		imgMode: { type: String, default: 'widthFix' },
		header: {
			type: Object,
			default: function() {
				return {};
			}
		}
	},
	data() {
		return {
			imgLists: [],
			videoContextList: [],
			updatting: false
		};
	},
	watch: {
		imgLists(newVal, oldVal) {
			if (!this.updatting) {
				this.$emit('change', newVal);
			}
		}
	},
	methods: {
		videoPlay(e) {
			let id = e.target.id;
			let index = id.replace(/[^0-9]/g, '');
			for (let i = 0; i < this.imgLists.length; i++) {
				if (id == this.videoContextList[i].id) {
					this.videoContextList[i].play();
					// this.videoContextList[i].requestFullScreen();
				} else {
					this.videoContextList[i].pause();
				}
			}
		},
		videoError(e) {
			
			console.log("视频上传失败==",e);
			
			uni.showModal({
				content: '网络错误，请稍后重试！',
				showCancel: false
			});
		},
		clearAllImgs() {
			this.imgLists = [];
		},
		addImg() {
			var num = this.maxFileNumber - this.imgLists.length;
			if (num < 1) {
				return false;
			}
			// uni.showLoading({title:""});
			uni.chooseVideo({
				count: num,
				sizeType: ['compressed'],
				compressed: false,
				sourceType: ['album'],
				success: async res => {
					let file = res.tempFilePath;
					if (this.beforeUpload) {
						const valid = await this.beforeUpload(res);
						if (valid === false) {
							return false;
						}
					}
					this.imgLists.push({ url: file, progress: 0, error: false });
					this.videoContextList.push(uni.createVideoContext('myVideo_' + this.imgLists.length, this));
					//uni.hideLoading();
				},
				complete: function() {
					//uni.hideLoading();
				},
				fail: function() {
					//uni.hideLoading();
				}
			});
		},
		removeImg(e) {
			var index = e.currentTarget.id.replace('sunui-items-img-', '');
			var removeImg = this.imgLists.splice(index, 1);
			var removeContent = this.videoContextList.splice(index, 1);
			this.$emit('removeImg', removeImg[0]);
		},
		showImgs(e) {
			var currentImg = e.currentTarget.dataset.imgurl;
			var imgs = [];
			for (let i = 0; i < this.imgLists.length; i++) {
				imgs.push(this.imgLists[i].url);
			}
			uni.previewImage({
				urls: imgs,
				current: currentImg
			});
		},
		upload(index) {
			if (this.updatting) {
				return;
			}
			this.updatting = true;
			if (!index) {
				index = 0;
			}
			// uni.showLoading({ title: '视频上传中', });
			uni.showLoading({
				title: '视频上传中',
				mask: true
			});
			this.uploadBase(index);
		},
		retry(e) {
			var index = e.currentTarget.dataset.index;
			this.upload(index);
		},
		uploadBase(index) {
			// 全部上传完成
			if (index > this.imgLists.length - 1) {
				uni.hideLoading();
				this.updatting = false;
				this.$emit('uploaded', this.imgLists);
				return;
			}
			// 验证后端
			// if (this.alyUrl == '') {
			// 	uni.showToast({ title: '请设置上传服务器地址', icon: 'none' });
			// 	return;
			// }
			// 检查是否是默认值
			if (this.imgLists[index].progress >= 1) {
				this.uploadBase(index + 1);
				return;
			}
			this.imgLists[index].error = false;
			
			console.log("--------imgLists",this.imgLists);
			
			this.formData.key = new Date().getTime() + Math.floor(Math.random() * 150)+'.mp4';
			
			// 创建上传对象
			const task = uni.uploadFile({
				url: this.alyUrl,
				filePath: this.imgLists[index].url,
				name: 'file' || this.fileName,
				formData: this.formData,
				header: this.header,
				success: uploadRes => {
					
					// console.log("uploadRes+++++++",uploadRes);
					
					// uploadRes = JSON.parse(uploadRes.data);
					if (uploadRes.statusCode != 200) {
						uni.showToast({ title: '上传失败 : ' + uploadRes.data, icon: 'none' });
						this.error(index);
					} else {
						//上传图片成功
						uni.showToast({ title: '上传成功' });
						this.updatting = false;
						this.imgLists[index].progress = 100;
						this.imgLists[index].url = this.alyUrl+'/'+this.formData.key;
						this.imgLists[index].result = uploadRes;
						this.uploadBase(index + 1);
					}
				},
				fail: e => {
					uni.showToast({ title: '上传失败，请点击重试', icon: 'none' });
					this.error(index);
				}
			});
			task.onProgressUpdate(res => {
				
				// console.log("------进度条",res);
				
				if (res.progress > 0) {
					this.imgLists[index].progress = res.progress;
					this.imgLists.splice(index, 1, this.imgLists[index]);
				}
			});
		},
		// 上传错误
		error(index) {
			this.updatting = false;
			setTimeout(() => {
				this.imgLists[index].progress = 0;
				this.imgLists[index].error = true;
				this.$emit('uploaderror');
			}, 500);
		},
		// 设置默认值
		setItems(items) {
			this.imgLists = [];
			for (let i = 0; i < items.length; i++) {
				this.imgLists.push({ url: items[i], progress: 100 });
			}
		}
	}
};
</script>
<style scoped>
.sunui-add-list {
	display: flex;
	flex-wrap: wrap;
}
.sunui-add-list-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.sunui-add-list-btn-text {
	font-size: 26rpx;
	line-height: 36rpx;
	text-align: center;
	color: #999999;
	width: 100%;
}
.sunui-add-list-btn-icon {
	font-size: 80rpx;
	height: 80rpx;
	line-height: 80rpx;
	margin-bottom: 20rpx;
	color: #999999;
}
.sunui-add-list-items {
	width: 220rpx;
	height: 220rpx;
	overflow: hidden;
	margin-bottom: 10rpx;
	margin-right: 10rpx;
	background: #323232;
	font-size: 0;
	position: relative;
	border-radius: 10rpx;
}
.sunui-add-list-image {
	width: 220rpx;
}
.sunui-add-list-remove {
	position: absolute;
	z-index: 15;
	right: 10rpx;
	top: 0;
	color: #888888;
}
.upload-progress {
	position: absolute;
	z-index: 15;
	left: 0;
	bottom: 10rpx;
	width: 220rpx;
	padding: 0 20rpx;
}
.sunui-add-list-reup {
	position: absolute;
	z-index: 3;
	left: 0;
	top: 0rpx;
	width: 220rpx;
	height: 220rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.3);
	flex-direction: column;
}
.sunui-add-list-reup-icon {
	text-align: center;
	width: 100%;
	color: #ffffff;
	display: block;
	font-size: 80rpx;
	line-height: 100rpx;
}
.sunui-add-list-reup-text {
	text-align: center;
	width: 100%;
	color: #ffffff;
	display: block;
	font-size: 20rpx;
	line-height: 30rpx;
}
.sunui-add-list-img {
	width: 220rpx;
}
</style>
