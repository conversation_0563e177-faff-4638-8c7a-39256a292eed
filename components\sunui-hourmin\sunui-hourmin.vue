<template>
	<sunui-popup ref="pop">
		<template v-slot:content>
			<view class="picker-date-view">
				<view class="sunui-flex-space-between">
					<view class="sunui-tag" style="color: #666;" hover-class="sunui-hover" @click="closePicker"></view>
					<view class="sunui-tag" style="color: #4A8AF4;text-align: right;" hover-class="sunui-hover" @click="closePicker">确认</view>
				</view>
				<picker-view indicator-style="height: 50px;" class="picker-date" :value="value" @change="dateChange">
					<picker-view-column>
						<view v-for="(item, index) in startHour" :key="index" style="line-height: 50px">{{ item }}点</view>
					</picker-view-column>
					<picker-view-column><view style="line-height: 50px;text-align: center;">:</view></picker-view-column>
					<picker-view-column>
						<view v-for="(item, index) in startMin" :key="index" style="line-height: 50px">{{ item }}分</view>
					</picker-view-column>
					<picker-view-column><view style="line-height: 50px;text-align: center;">~</view></picker-view-column>
					<picker-view-column>
						<view v-for="(item, index) in endHour" :key="index" style="line-height: 50px">{{ item }}点</view>
					</picker-view-column>
					<picker-view-column><view style="line-height: 50px;text-align: center;">:</view></picker-view-column>
					<picker-view-column>
						<view v-for="(item, index) in endMin" :key="index" style="line-height: 50px">{{ item }}分</view>
					</picker-view-column>
				</picker-view>
			</view>
		</template>
	</sunui-popup>
</template>

<script>
export default {
	name: 'sunui-hourmin',
	data() {
		return {
			value: [0, 0, 0, 0, 0, 0, 0],
			startHour: [],
			startMin: [],
			endHour: [],
			endMin: [],
			startHourStr: '',
			startMinStr: '',
			endHourStr: '',
			endMinStr: '',
			show: false
		};
	},
	created() {
		this.getResetTime();
	},
	methods: {
		openPicker(){
			this.$refs.pop.show({
				style: 'background-color:#fff;width:100%;height:auto;overflow-x:auto;',
				anim: 'bottom',
				position: 'bottom'
			});
		},
		moveHandle() {},
		closePicker() {
			this.$refs.pop.close();
			this.$emit('change', {
				startHour: this.startHourStr,
				startMin: this.startMinStr,
				endHour: this.endHourStr,
				endMin: this.endMinStr,
				startHourMin: `${this.startHourStr}:${this.startMinStr}`,
				endHourMin: `${this.endHourStr}:${this.endMinStr}`
			});
		},
		toDigit(any) {
			return any.toString().replace(/^(\d)$/, `0$1`);
		},
		getResetTime() {
			for (let i = 1; i <= 23; i++) {
				i = this.toDigit(i);
				this.startHour.push(i);
			}
			for (let i = 0; i <= 59; i++) {
				i = this.toDigit(i);
				this.startMin.push(i);
			}
			for (let i = 1; i <= 24; i++) {
				i = this.toDigit(i);
				this.endHour.push(i);
			}
			for (let i = 0; i <= 59; i++) {
				i = this.toDigit(i);
				this.endMin.push(i);
			}
		},
		dateChange(e) {
			const val = e.detail.value;
			this.value = val;
			if (this.value[0] > this.value[4]) {
				this.value[4] = this.value[0] + 1;
			}
			this.startHourStr = this.startHour[val[0]];
			this.startMinStr = this.startMin[val[2]];
			this.endHourStr = this.endHour[val[4]];
			this.endMinStr = this.endMin[val[6]];
		}
	}
};
</script>

<style lang="scss">
.picker-date {
	width: 100%;
	height: 100%;
	text-align: center;
}
// .sunui-hour-mask {
// 	background-color: rgba(0, 0, 0, 0.5);
// 	position: absolute;
// 	height: 100%;
// 	width: 100%;
// 	z-index: 9999;
// 	top: 0;
// 	right: 0;
// 	bottom: 0;
// 	left: 0;
// }
.picker-date-view {
	border-top: 1rpx solid #eee;
	// position: fixed;
	// bottom: 0;
	height: 300px;
	width: 100%;
	// z-index: 9999;
	background-color: #fff;

	.sunui-tag {
		width: 200rpx;
		text-align: left;
		padding: 20rpx;
		padding-top: 0;
		height: 50rpx;
		line-height: 50rpx;
		font-size: 32rpx;
	}
}
</style>
