<script>
	export default {
		onLaunch: async function() {

			const updateManager = uni.getUpdateManager();
			updateManager.onCheckForUpdate((res) => {
				// 检测新版本后的回调
				if (res && res.hasUpdate) {
					// 如果有新版本提醒并进行强制升级
					uni.showModal({
						content: '新版本已经准备好，是否重启应用？',
						showCancel: false,
						confirmText: '确定',
						success: (res) => {
							if (res.confirm) {
								updateManager.onUpdateReady((res) => {
									// 新版本下载完成的回调,强制当前小程序应用上新版本并重启
									updateManager.applyUpdate()
								})

								updateManager.onUpdateFailed((res) => {
									// 新版本下载失败的回调
									uni.showModal({
										content: '下载失败，请您删除当前小程序，重新搜索打开',
										showCancel: false,
										confirmText: '知道了',
									})
								})
							}
						},
					})
				}
			})


			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
			uni.removeStorageSync("isOpen")
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url('common/css/sun.css');
	@import url('common/css/public.css');
</style>