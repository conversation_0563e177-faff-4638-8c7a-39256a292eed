<template>
	<view class="content">
		<block v-if="detail">
			<view class="padding_30rpx">
				<rich-parser :html="detail" domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load
					ref="article" selectable show-with-animation use-anchor>
					<!-- 加载中... -->
				</rich-parser>

			</view>
		</block>
		<block v-else>
			<mescroll-empty></mescroll-empty>
		</block>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				detail: '',
			}
		},

		onLoad() {
			this.brokerageSet();
		},

		methods: {

			async brokerageSet() {
				const result = await this.$http.post({
					url: this.$api.brokerageSet,
				});
				if (result.errno == 0) {
					this.detail = result.data.explain;
				}
			},
		}
	}
</script>

<style lang="scss">
	
	
</style>