<template>
	<view v-if="configs.show" class="sunui-pop" :class="configs.isCloseCls" @touchmove.stop.prevent="moveHandle">
		<view class="sunui-pop-panel" :style="{ zIndex: configs.zIndex + 10 }">
			<view
				v-if="configs.shade"
				class="sunui-pop-mask"
				:style="{ backgroundColor: configs.rgba ? configs.rgba : '', zIndex: configs.zIndex }"
				@touchstart="shadeTaped"
			></view>
			<view class="sunui-pop-main">
				<view class="sunui-pop-child" :class="['anim-' + configs.anim, configs.skin ? 'sunuipop-' + configs.skin : '', configs.position]" :style="configs.style">
					<view v-if="configs.title" :style="configs.titleStyle" class="sunui-pop-tit">
						<view v-if="configs.leftClose && !configs.bottomClose" @tap="close()" style="position: absolute;top: 8rpx;color: #000;font-weight: bold;font-size: 40rpx;">
							×
						</view>
						{{ configs.title }}
						<view
							v-if="configs.rightClose && !configs.bottomClose"
							@tap="close()"
							style="position: absolute;top: 8rpx;right: 40rpx;color: #000;font-weight: bold;font-size: 40rpx;"
						>
							×
						</view>
					</view>
					<view v-if="configs.content" class="sunui-pop-cnt" :style="configs.contentStyle">
						<view v-if="configs.icon && configs.skin == 'toast'" class="sunui-pop-toast" :class="configs.icon"></view>
						<!-- <rich-text :nodes="configs.content"></rich-text> -->
						<slot name="content"></slot>
					</view>
					<view v-if="configs.btns" class="sunui-pop-btns">
						<text v-for="(item, index) in configs.btns" :key="index" class="btn" :style="item.style" @tap="btnTaped(item)">{{ item.text }}</text>
					</view>
				</view>
				<view v-if="configs.bottomClose" class="sunuipop-xclose" @tap="close"></view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			config: {
				// 遮罩层
				zIndex: 99999,
				// 弹窗背景颜色
				rgba: `rgba(0,0,0,.6)`,
				// 是否显示
				show: false,
				// 顶部关闭 - 左
				leftClose: false,
				// 顶部关闭 - 右
				rightClose: false,
				// 是否显示底部关闭
				bottomClose: false,
				// 标题
				title: ``,
				// 标题样式
				titleStyle: ``,
				// 富文本内容
				content: `<div>&nbsp;</div>`,
				// 内容样式
				contentStyle: ``,
				// 弹出样式
				style: null,
				// 展示格式(ios,android)
				skin: '',
				// 是否显示图标
				icon: ``,
				// 是否显示阴影
				shade: true,
				// 是否点击阴影关闭弹窗
				shadeClose: true,
				// 时间
				time: 0,
				// 结束后的回调
				end: null,
				// 动画
				anim: 'scaleIn',
				// 位置
				position: ``,
				// 按钮
				btns: null
			},
			configs: {},
			// 计时器
			timer: null
		};
	},
	name: 'sunui-popup',
	methods: {
		moveHandle() {},
		show(args) {
			this.configs = Object.assign({}, this.config, args, {
				show: true
			});
			if (this.configs.time) {
				this.timer = setTimeout(() => {
					this.close();
				}, this.configs.time * 1000);
			}
			this.$emit('open');
		},
		close() {
			this.$set(this.configs, 'isCloseCls', 'sunui-pop-close');
			setTimeout(() => {
				this.$set(this.configs, 'show', false);
				this.timer && clearTimeout(this.timer);
				delete this.timer;
				typeof this.configs.end === 'function' && this.configs.end.call(this);
				delete this.configs.end;
			}, 200);
			this.$emit('close');
		},
		btnTaped(item) {
			typeof item.onTap === 'function' && item.onTap();
		},
		shadeTaped() {
			if (!this.configs.shadeClose) return;
			this.close();
		}
	}
};
</script>

<style scoped>
.sunui-pop-panel {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	width: 100%;
	position: fixed;
	left: 0;
	top: 0;
	pointer-events: none;
}

.sunui-pop-mask {
	height: 100%;
	width: 100%;
	position: fixed;
	left: 0;
	top: 0;
	pointer-events: auto;
	touch-action: none;
	animation: anim-mask 0.5s;
}

@keyframes anim-mask {
	0% {
		opacity: 0;
	}
}

.sunui-pop-main {
	position: relative;
	z-index: 202009;
	pointer-events: auto;
}

.sunui-pop-child {
	font-size: 14px;
}

.sunui-pop-tit {
	color: #353535;
	font-size: 17px;
	text-align: center;
	padding: 8px 20px;
	position: relative;
	border-radius: inherit;
}

.sunui-pop-cnt {
	/* padding: 15px; */
	line-height: 1.3;
	word-wrap: break-word;
	word-break: break-all;
}

.sunui-pop-btns {
	display: flex;
	align-items: center;
	position: relative;
	border-radius: inherit;
}

.sunui-pop-btns:after {
	content: '';
	background: #dbdbdb;
	height: 1px;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	transform: scaleY(0.5);
	transform-origin: 0 0;
}

.sunui-pop-btns .btn {
	box-sizing: border-box;
	color: #353535;
	flex: 1;
	font-size: 14px;
	text-align: center;
	line-height: 50px;
	position: relative;
	border-radius: inherit;
}

.sunui-pop-btns .btn:active {
	background: #ebebeb;
}

.sunui-pop-btns .btn:after {
	content: '';
	background: #dbdbdb;
	width: 1px;
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	transform: scaleX(0.5);
	transform-origin: 0 0;
}

.sunui-pop-btns .btn:first-child:after {
	display: none;
}

.sunuipop-xclose {
	background-image: url('./skin/error.png');
	background-size: cover;
	margin-left: -12px;
	height: 25px;
	width: 25px;
	position: absolute;
	left: 50%;
	bottom: -40px;
	z-index: 1001;
}

.sunui-pop-child.top,
.sunui-pop-child.right,
.sunui-pop-child.bottom,
.sunui-pop-child.left {
	border-radius: 0;
	max-width: 100%;
	width: 100%;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
}

.sunui-pop-child.top {
	top: 0;
	bottom: auto;
}

.sunui-pop-child.right {
	top: 0;
	left: auto;
	width: 80%;
}

.sunui-pop-child.left {
	top: 0;
	right: auto;
	width: 80%;
}

.sunui-pop-close .sunui-pop-mask {
	animation: anim-fadeOut 0.3s;
}

.sunui-pop-close .sunui-pop-child {
	animation: anim-fadeOut 0.3s;
}

.sunui-pop-close .anim-scaleIn {
	animation: anim-scaleOut 0.3s;
}

.sunui-pop-close .anim-top {
	animation: anim-outTop 0.3s;
}

.sunui-pop-close .anim-right {
	animation: anim-outRight 0.3s;
}

.sunui-pop-close .anim-bottom {
	animation: anim-outBottom 0.3s;
}

.sunui-pop-close .anim-left {
	animation: anim-outLeft 0.3s;
}

.sunuipop-toast {
	background: rgba(17, 17, 17, 0.7);
	border-radius: 5px;
	color: #fff;
	text-align: center;
	width: 125px;
}

.sunuipop-toast .sunui-pop-cnt {
	padding: 20px;
}

.sunui-pop-toast {
	margin: 0 auto;
	margin-bottom: 10px;
	height: 25px;
	width: 25px;
	background-size: cover;
	background-position: center;
}

.sunui-pop-toast.success {
	background-image: url('./skin/success.png');
}

.sunui-pop-toast.info {
	background-image: url('./skin/info.png');
}

.sunui-pop-toast.error {
	background-image: url('./skin/error.png');
}

.sunui-pop-toast.loading {
	background-image: url('./skin/loading.png');
	animation: anim-loading 1s steps(12, end) infinite;
}

.sunui-pop-footer {
	background: none;
	border-radius: 12px;
	margin: 0 auto;
	max-width: 100%;
	width: 95%;
	position: fixed;
	bottom: 10px;
	left: 0;
	right: 0;
}

.sunui-pop-footer .sunui-pop-cnt {
	background: rgba(255, 255, 255, 0.85);
	padding: 20px;
	text-align: center;
}

.sunui-pop-footer .sunui-pop-btns {
	flex-direction: column;
}

.sunui-pop-footer .sunui-pop-btns:after {
	display: none;
}

.sunui-pop-footer .sunui-pop-btns .btn {
	background: rgba(255, 255, 255, 0.85);
	width: 100%;
}

.sunui-pop-footer .sunui-pop-btns .btn:active {
	background: #dbdbdb;
}

.sunui-pop-footer .sunui-pop-btns .btn:after {
	display: none;
}

.sunui-pop-footer .sunui-pop-btns .btn:before {
	content: '';
	background: #cbcbcb;
	height: 1px;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	transform: scaleY(0.5);
	transform-origin: 0 0;
}

.sunui-pop-footer .sunui-pop-btns .btn:last-child:before {
	display: none;
}

.sunui-pop-footer .sunui-pop-btns .btn:last-child {
	border-radius: 12px;
	margin-top: 10px;
}

.sunui-pop-footer .sunui-pop-btns .btn:nth-last-child(2) {
	border-radius: 0 0 12px 12px;
}

.sunui-pop-footer.center {
	max-width: 600upx;
	width: auto;
	position: relative;
	bottom: 0;
}

.sunui-pop-footer.center .sunui-pop-btns .btn:last-child:before {
	display: inherit;
}

.sunui-pop-footer.center .sunui-pop-btns .btn:last-child {
	border-radius: 0;
	margin-top: 0;
}

.sunui-pop-footer.center .sunui-pop-btns .btn:nth-last-child(2) {
	border-radius: 0;
}
.sunuipop-android {
	padding-top: 25px;
	width: 600upx;
	position: relative;
}

.sunuipop-android .sunui-pop-tit {
	font-size: 18px;
	padding: 0 25px 10px;
	text-align: left;
}

.sunuipop-android .sunui-pop-cnt {
	color: #757575;
	padding: 0 25px 30px;
}

.sunuipop-android .sunui-pop-cnt:first-child {
	color: #353535;
}

.sunuipop-android .sunui-pop-btns {
	flex-direction: row;
	justify-content: flex-end;
	padding: 0 15px 15px;
}

.sunuipop-android .sunui-pop-btns:after {
	display: none;
}

.sunuipop-android .sunui-pop-btns .btn {
	flex: none;
	padding: 0 15px;
	line-height: 30px;
}

.sunuipop-android .sunui-pop-btns .btn:after {
	display: none;
}

.sunuipop-sheet {
	width: 600upx;
	position: relative;
	background-color: #fff;
}

.sunuipop-sheet .sunui-pop-tit {
	font-size: 18px;
	padding: 15px 25px 10px;
	text-align: left;
	font-weight: bold;
}

.sunuipop-sheet .sunui-pop-cnt {
	padding: 0 25px 15px;
}

.sunuipop-sheet .sunui-pop-cnt:first-child {
	padding-top: 15px;
}

.sunuipop-sheet .sunui-pop-btns {
	flex-direction: column;
}

.sunuipop-sheet .sunui-pop-btns:after {
	display: none;
}

.sunuipop-sheet .sunui-pop-btns .btn {
	text-align: left;
	padding: 0 25px;
	width: 100%;
}

.sunuipop-sheet .sunui-pop-btns .btn:after {
	display: none;
}

.sunuipop-sheet .sunui-pop-btns .btn:before {
	content: '';
	background: #dbdbdb;
	height: 1px;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	transform: scaleY(0.5);
	transform-origin: 0 0;
}

.sunuipop-sheet .sunui-pop-btns .btn:first-child:before {
	display: none;
}

.anim-fadeIn {
	animation: anim-fadeIn 0.3s;
}

.anim-scaleIn {
	animation: anim-scaleIn 0.3s;
}

.anim-shake {
	animation: anim-shake 0.3s;
}

.anim-top {
	animation: anim-top 0.3s;
}

.anim-right {
	animation: anim-right 0.3s;
}

.anim-bottom {
	animation: anim-bottom 0.3s;
}

.anim-left {
	animation: anim-left 0.3s;
}

.anim-fadeOut {
	animation: anim-fadeOut 0.3s;
}

.anim-scaleOut {
	animation: anim-scaleOut 0.3s;
}

.anim-outTop {
	animation: anim-outTop 0.3s;
}

.anim-outRight {
	animation: anim-outRight 0.3s;
}

.anim-outBottom {
	animation: anim-outBottom 0.3s;
}

.anim-outLeft {
	animation: anim-outLeft 0.3s;
}

@keyframes anim-fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

@keyframes anim-scaleIn {
	from {
		opacity: 0;
		transform: scale(0.9);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes anim-shake {
	0%,
	100% {
		transform: translateX(0);
	}

	10%,
	30%,
	50%,
	70%,
	90% {
		transform: translateX(-10px);
	}

	20%,
	40%,
	60%,
	80% {
		transform: translateX(10px);
	}
}

@keyframes anim-top {
	from {
		opacity: 0;
		transform: translateY(-100%);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

@keyframes anim-right {
	from {
		opacity: 0;
		transform: translateX(100%);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

@keyframes anim-bottom {
	from {
		opacity: 0;
		transform: translateY(100%);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

@keyframes anim-left {
	from {
		opacity: 0;
		transform: translateX(-100%);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

/* 动画(离开) */
@keyframes anim-fadeOut {
	100% {
		opacity: 0;
	}
}

@keyframes anim-scaleOut {
	100% {
		opacity: 0;
		transform: scale(0.9);
	}
}

@keyframes anim-outTop {
	100% {
		opacity: 0;
		transform: translateY(-100%);
	}
}

@keyframes anim-outRight {
	100% {
		opacity: 0;
		transform: translateX(100%);
	}
}

@keyframes anim-outBottom {
	100% {
		opacity: 0;
		transform: translateY(100%);
	}
}

@keyframes anim-outLeft {
	100% {
		opacity: 0;
		transform: translateX(-100%);
	}
}

@keyframes anim-loading {
	0% {
		transform: rotate3d(0, 0, 1, 0deg);
	}

	100% {
		transform: rotate3d(0, 0, 1, 360deg);
	}
}
</style>
