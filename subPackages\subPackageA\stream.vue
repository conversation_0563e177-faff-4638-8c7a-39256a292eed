<template>
	<view>
		<view class="display-a padding_30rpx color_FFFFFF">
			<view class="d-line"></view>
			<view class="font-size_32rpx">类型</view>
			<view class="font-size_26rpx margin-left_20rpx">
				({{title}})
			</view>
		</view>

		<view class="d-text">
			<!-- <rich-text class="rich-text" :nodes="formattedContent"></rich-text> -->
			<towxml :nodes="formattedContent" />
		</view>

		<view style="height: 140rpx;"></view>

		<view class="buts" v-if="isEnd">
			<view class="but" @click="startStream()">重新生成</view>
			<view class="but" @click="getCopy()">复制文案</view>
		</view>

	</view>
</template>

<script>
	import {
		decodedString,
		markdownToText
	} from './utils/decodedString.js'
	const towxml = require('./wxcomponents/towxml/index');
	export default {
		data() {
			return {
				streamData: '', // 存储流式接收到的数据
				requestTask: null, // 存储请求任务对象，用于需要时中止请求
				titleObj: {
					'2': '同城团购',
					'6': '口播文案',
					'7': '朋友圈营销',
					'8': '小红书笔记',
					'19': '账号装修'
				},
				// 标题
				title: '',
				// 类型
				type: '',
				// 数据
				content: '',
				typeIndex: '',

				// 是否生成完毕
				isEnd: false
			}
		},
		computed: {
			formattedContent() {
				// 处理换行符，确保在小程序中正确显示
				if (!this.streamData) return '';

				// 将普通换行符转换为HTML换行
				let formatted = this.streamData
				// 	.replace(/\n/g, '<br>')
				// 	.replace(/\r\n/g, '<br>')
				// 	.replace(/\r/g, '<br>');

				// 确保小程序富文本能够正确解析
				return towxml(formatted, 'markdown', {
					theme: 'dark'
				});
			}
		},
		methods: {


			startStream() {
				this.isEnd = false
				this.streamData = ''
				uni.showLoading({
					title: '正在生成'
				})
				let url = null
				if (this.type === '2') {
					url = this.$api.getGroupBuy
				} else if (this.type === '6') {
					url = this.$api.getOralBroadcasting
				} else if (this.type === '7') {
					url = this.$api.getFriend
				} else if (this.type === '8') {
					url = this.$api.getReadBook
				} else if (this.type === '19') {
					url = this.$api.getAccount
				}

				let data = {
					uid: uni.getStorageSync('uid') // 请求参数
				}
				if (this.content) {
					data.content = this.content
				}
				// 使用流式请求
				this.requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						uni.hideLoading()
						// 处理接收到的数据块
						try {

							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 确保换行符被保留（不进行额外处理，由computed属性处理格式化）
								this.streamData += text;
							} else if (typeof data === 'string') {
								// 如果没有结束标记，直接追加
								this.streamData += data;
							} else {
								console.warn("收到未知类型的数据块:", data);
							}
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						setTimeout(() => {
							if (this.typeIndex === '2') {
								this.returnNav(this.streamData);
							}
						}, 2000)
						this.isEnd = true
						console.log('文案生成完成');
					},
					onError: (err) => {
						uni.hideLoading()
						console.error("流式请求错误:", err);
					}
				});
			},

			//返回上一页
			returnNav(text) {
				var pages = getCurrentPages();
				var prevPage = pages[pages.length - 3]; //上一个页面
				prevPage.$vm.otherFun2(text); //重点$vm
				uni.navigateBack({
					delta: 2
				});
			},
			//复制
			getCopy() {
				if (this.streamData) {
					// 将Markdown转换为纯文本后复制
					const plainText = markdownToText(this.streamData);

					uni.setClipboardData({
						data: plainText, // 使用转换后的纯文本
						success: () => {
							// 复制成功的回调
							this.$sun.toast('复制成功');
						},
						fail: (err) => {
							console.log("复制失败原因===>", err);
							// 复制失败的回调
							this.$sun.toast('复制失败');
						}
					});
				}
			},

			// 中止请求
			stopStream() {
				if (this.requestTask) {
					this.requestTask.cancel();
					this.requestTask = null;
					console.log('已中止请求');
				}
			},

			// 查询起号信息数据
			async getAccountInfo() {
				const result = await this.$http.get({
					url: this.$api.getAccountInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});

				if (result.errno == 0) {
					if (result.data) {
						this.streamData = result.data.content
						this.isEnd = true
					} else {
						this.startStream()
					}
				} else {
					this.$sun.toast(result.message, 'none');
					uni.navigateBack()
				}
			}
		},
		// 组件销毁时中止可能正在进行的请求
		onUnload() {
			this.stopStream();
		},
		onLoad(options) {
			if (options.type) {
				this.type = options.type
				this.title = this.titleObj[options.type]
				if (!['19'].includes(options.type)) {
					this.content = uni.getStorageSync('stream-content')
				}
			}
			if (options.typeIndex) {
				this.typeIndex = options.typeIndex;
			}

			if (options.type === '19') {
				this.getAccountInfo()
			} else {
				this.startStream()
			}
		}
	}
</script>

<style lang="scss">
	.buts {
		display: flex;
		align-items: center;
		justify-content: space-around;
		position: fixed;
		bottom: 40rpx;
		width: 100%;
	}

	.but {
		width: 300rpx;
		text-align: center;
		border-radius: 100rpx;
		box-shadow: 2px 3px 14px 0px rgba(30, 156, 214, 0.81), inset 0px 0px 11px 0px rgba(204, 235, 255, 0.3);
		background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
		color: #FFF;
		font-size: 32rpx;
		font-weight: 600;
		padding: 22rpx 0;
	}

	.d-text {
		width: 710rpx;
		background-color: #111D37;
		border-radius: 10rpx;
		margin: 0 20rpx;
		color: #FFF;
		padding-bottom: 20rpx;

		.rich-text {
			width: 100%;
			word-break: break-word;
			white-space: pre-wrap;
			word-wrap: break-word;

			/deep/ view {
				white-space: pre-wrap;
				word-break: break-word;
				word-wrap: break-word;
			}

			/deep/ p {
				white-space: pre-wrap;
				word-break: break-word;
				word-wrap: break-word;
				margin-bottom: 10rpx;
			}
		}
	}

	.d-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245), rgb(66, 72, 244) 100%);
		margin-right: 20rpx;
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
</style>