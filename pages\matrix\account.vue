<template>
	<view>
		
		<view class="display-a acc-top">
			<block v-for="(item,index) in tabs" :key="index">
				<view class="tabs" @click="getLabel(item.id)">
					<view class="margin-bottom_20rpx" :style="tabId == item.id ? 'color:#69F8AA;' : ''">{{item.name}}</view>
					<view class="tabs-line" :style="tabId == item.id ? 'background: #69F8AA;' : ''"></view>
				</view>
			</block>
		</view>
		
		<view class="display-a search-x">
			<icon type="search" size="16" style="margin-left: 240rpx;margin-right: 20rpx;"></icon>
			<input type="text" placeholder="请输入用户名称" v-model="name" placeholder-class="placeholder"
				@input="inputChange" @confirm="confirmChange" />
		</view>
	
		<!-- <view class="display-a padding-bottom_30rpx">
			<view class="display-a-jc" style="width: 374rpx;">
				<picker mode="selector" @change="bindPickerChange2" :range="statusList" :range-key="'name'">
					<view class="display-a">
						<input type="text" class="input-t" disabled placeholder="授权状态" v-model="statusName"
							placeholder-class="color_949DA6" />
						<image class="matrix-7" src="../../static/matrix/7.png"></image>
					</view>
				</picker>
			</view>
			<view class="display-a-jc" style="width: 374rpx;" @click="openPicker()">
				<view style="width: 160rpx;" v-if="startTime">
					<view class="font-size_26rpx">{{startTime+' '+endTime}}</view>
				</view>
				<view v-else class="input-t color_949DA6">日期筛选</view>
				<image class="matrix-7" src="../../static/matrix/7.png"></image>
			</view>
		</view> -->
		
		<view class="display-a padding_20rpx_0" @click="batch()">
			<image class="matrix-9" :src="imgUrl+'389.png'"></image>
			<view class="color_999999">批量操作</view>
		</view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public">
					<view class="display-a list-data">
						<image class="head" :src="item.avatar"></image>
						<view style="width: 440rpx;">
							<view class="font-size_32rpx margin-bottom_10rpx">{{item.account_name}}</view>
							<view class="font-size_26rpx" :class="item.groupName ? 'color_00FFCA' : 'color_999999'">所在分组:
								{{item.account_group_name ?item.account_group_name :'--'}}
							</view>
						</view>
						<block v-if="item.status == 2" class="empower" style="background: #F00;">已失效</block>
						<view v-else class="empower" :style="isEmpower(item) ? '' : 'background: #F00;'">
							{{isEmpower(item) ? '授权中' : '已失效'}}</view>
					</view>
					<view class="display-a">
						<view>
							<!-- <view class="color_999999 font-size_26rpx margin-bottom_10rpx">授权时间: {{item.create_time}}</view> -->
							<view class="color_999999 font-size_26rpx">到期时间: {{item.expires_in}}</view>
						</view>
						<image @click="getBatch(item)" class="matrix-10" :src="imgUrl+'390.png'"></image>
					</view>
				</view>
			</block>
		</mescroll-body>
		
		<sunui-popup ref="pop2">
			<template v-slot:content>
				<view class="text-align_center">
					<view v-if="tabId != 3" class="padding_40rpx_0 color_1E6CEB p-bo" @click="getAllTask()">作品</view>
					<picker mode="selector" @change="bindPickerChange3" :range="groupList"
						:range-key="'name'">
						<view class="padding_40rpx_0 color_1E6CEB p-bo">换组</view>
					</picker>
					<view class="padding_40rpx_0 color_FF0000 p-bo" @click="getDel()">删除</view>
					<view class="padding_40rpx_0" @click="closeBatch()">取消</view>
				</view>
			</template>
		</sunui-popup>

		<term-picker :visable.sync="pickerVisable" :defaultDate="defaultDate" @confirm="confirm"></term-picker>


		<sunui-tabbar2 v-if="type !== 1" :fixed="true" :current="tabIndex" tintColor="#00FFCA"
			backgroundColor="#1B1B1B"></sunui-tabbar2>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: null,
				tabIndex: 1,

				imgUrl: this.$imgUrl,

				tabs: [{
						id: '1',
						name: 'D音'
					},
					{
						id: '2',
						name: 'K手'
					},
					{
						id: '3',
						name: '视频号'
					},
					{
						id: '4',
						name: '小红薯'
					},
					// {id:'5',name:'B站'}
				],
				tabId: '1', //1抖音 2快手 3视频号 4小红书

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],
				listObj: {},

				windowHeight: '',

				name: '',

				setObj: {}, //商家端查询代理基础设置

				statusList: [{
						id: '',
						name: '全部'
					},
					{
						id: '1',
						name: '已过期'
					},
					{
						id: '0',
						name: '授权中'
					}
				],
				statusName: '',
				statusId: '',
				
				/* 筛选时间 start */
				pickerVisable: false,
				defaultDate: [],
				startTime: null,
				endTime: null,
				
				groupList: [],
				
			}
		},
		
		computed: {
		
			// 是否已授权
			isEmpower() {
				return function(row) { //使用函数返回
					let isEmpower = false;
					// true,已授权 ,为false，则为已失效
					// row.type  1D音2K手
					
					isEmpower = new Date(this.msToDate(new Date()).hasTime.replace(/-/g, "\/")) < new Date(this.date(row.expires_in).replace(/-/g, "\/"))
					
					console.log("---->",new Date(this.msToDate(new Date()).hasTime.replace(/-/g, "\/")));
					
					return isEmpower;
				}
			},
		},

		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)
			}
			//获取系统信息
			uni.getSystemInfo({
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 738;
				}
			})
			this.getGroup();
		},
		
		onShow() {
			let pagearr = getCurrentPages(); //获取应用页面栈
			let currentPage = pagearr[pagearr.length - 1]; //获取当前页面信息
			if (currentPage.options.tabId) {
				this.tabId = currentPage.options.tabId;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}else {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}
			
		},
		
		methods: {
			
			//查看作品
			getAllTask() {
				this.closeBatch();
				uni.navigateTo({
					url: '/pages/matrix/allTask?taskId='+this.listObj.id+'&type=1'+'&selLabel='+this.tabId
				})
			},
			
			// 日期筛选
			confirm(e) {
				console.log('点击确定按钮，返回当前选择的值', e); // ['2021-06-01', '2021-07-01']
				this.startTime = e[0];
				this.endTime = e[1];
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			//日期筛选
			openPicker() {
				this.pickerVisable = true;
			},
			
			//批量分组
			batch() {
				
				let searchObj = {
					selLabel: this.tabId,
					name: this.name
				}
				
				uni.navigateTo({
					url: '/pages/matrix/batchAccount?obj='+JSON.stringify(searchObj)
				})
				
			},
			
			/*  删除  */
			getDel() {
				
				uni.showModal({
					title: '提示',
					content: '确认删除该账户?',
					success: async (res) => {
						if (res.confirm) {
							this.closeBatch();
							const result = await this.$http.post({
								url: this.$api.delAccount,
								data: {
									id: [this.listObj.id],
									uid: uni.getStorageSync("uid"),
								}
							});
							if (result.errno == 0) {
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			//账号状态
			bindPickerChange3(e) {
				this.closeBatch();
				let group_name = this.groupList[e.detail.value].name;
				let group_id = this.groupList[e.detail.value].id;
				this.updataGroup(group_name,group_id);
			},
			
			//换分组
			async updataGroup(group_name,group_id) {
				const result = await this.$http.post({
					url: this.$api.accountExchangeGroup,
					data: {
						account_group_id: group_id,
						// groupName: group_name,
						id: [this.listObj.id],
						uid: uni.getStorageSync("uid"),
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
					}, 2000);
				}else {
					this.$sun.toast(result.message, 'none');
				}
			},
			
			//分组
			async getGroup() {
				const result = await this.$http.post({
					url: this.$api.accountGroupList,
					data: {
						type: this.tabId,
						uid: uni.getStorageSync("uid"),
						page: 1,
						psize: 1000
					}
				});
				if (result.errno == 0) {
					this.groupList = result.data.list;
				}
			},
			
			//更多
			getBatch(obj) {
				this.listObj = obj;
				this.$refs.pop2.show({
					style: 'background-color:#fff;width:100%;height:auto;border-radius: 20px 20px 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: true,
				});
			},
			closeBatch() {
				this.$refs.pop2.close();
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.accountList,
					data: {
						type: this.tabId,
						uid: uni.getStorageSync("uid"),
						account_name: this.name,
						// startTime: this.startTime ? this.startTime+ ' 00:00:00' : null,
						// endTime: this.endTime ? this.endTime+ ' 23:59:59' : null,
						page: scroll.num,
						psize: 12
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			//账号状态
			bindPickerChange2(e) {
				this.statusName = this.statusList[e.detail.value].name;
				this.statusId = this.statusList[e.detail.value].id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			// 输入事件
			inputChange(e) {
				this.name = e.detail.value;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			
			},
			confirmChange(e) {
				this.name = e.detail.value;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			getLabel(type) {
				this.tabId = type;
				this.getGroup();
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			// date:创建日期 time：授权过期时间（需加）
			date(addTime) {
				// 日期或中国标准时间转毫秒数：
				// let result = new Date(date).getTime();
				 
				let allDate = null;
				allDate = new Date(addTime).getTime(); // 按秒计算，所以需要*1000（原始毫秒）
				// 预计到期时间计算
				let endtime = this.msToDate(allDate).hasTime
				// 去除秒数截取最后：的前面数字
				let index = endtime.lastIndexOf(":")
				endtime = endtime.substring(0, index);
				return endtime
			},
			// 毫秒数或中国标准时间转日期
			msToDate(msec) {
				let datetime = new Date(msec);
				let year = datetime.getFullYear();
				let month = datetime.getMonth();
				let date = datetime.getDate();
				let hour = datetime.getHours();
				let minute = datetime.getMinutes();
				let second = datetime.getSeconds();
				let result1 = year +
					'-' +
					((month + 1) >= 10 ? (month + 1) : '0' + (month + 1)) +
					'-' +
					((date + 1) < 10 ? '0' + date : date) +
					' ' +
					((hour + 1) < 10 ? '0' + hour : hour) +
					':' +
					((minute + 1) < 10 ? '0' + minute : minute) +
					':' +
					((second + 1) < 10 ? '0' + second : second);
			
				let result2 = year +
					'-' +
					((month + 1) >= 10 ? (month + 1) : '0' + (month + 1)) +
					'-' +
					((date + 1) < 10 ? '0' + date : date);
			
				let result = {
					hasTime: result1,
					withoutTime: result2
				};
			
				return result;
			},
			
		}
	}
</script>

<style lang="scss">
	
	.list-data {
		margin-bottom: 20rpx;
		padding-bottom: 20rpx;
		border-bottom: 1px solid #292929;
	}
	
	.color_1E6CEB {
		color: #1E6CEB;
	}
	
	.matrix-10 {
		width: 64rpx;
		height: 64rpx;
		margin-left: auto;
		margin-right: 14rpx;
	}
	
	.empower {
		position: absolute;
		z-index: 2;
		// background-color: #00D060;
		width: 100rpx;
		text-align: center;
		border-radius: 0px 10rpx 0px 10rpx;
		background: rgb(0, 134, 255);
		padding: 8rpx 0;
		font-size: 24rpx;
		color: #FFFFFF;
		right: 0;
		top: 0;
	}
	
	.head {
		width: 90rpx;
		height: 90rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	.matrix-9 {
		width: 40rpx;
		height: 40rpx;
		margin: 0 12rpx 0 40rpx;
	}
	
	.input-t {
		width: 120rpx;
		text-align: center;
	}
	
	.color_949DA6 {
		color: #949DA6;
	}
	
	.matrix-7 {
		width: 30rpx;
		height: 30rpx;
		margin-left: 6rpx;
	}
	
	.search-x {
		width: 710rpx;
		margin: 0 20rpx 10rpx;
		background-color: #323232;
		border-radius: 100rpx;
		padding: 20rpx;
		color: #FFF;
	}
	
	.tabs-line {
		width: 60rpx;
		background-color: #232323;
		height: 6rpx;
		border-radius: 100rpx;
		margin-left: 63rpx;
		// margin-left: 95rpx;
	}
	
	.tabs {
		width: 186rpx;
		// width: 250rpx;
		text-align: center;
		color: #E8E8E8;
	}
	
	.list-public {
		background-color: #0F0F0F;
		padding: 20rpx;
		color: #FFF;
		position: relative;
		z-index: 1;
	}
	
	.acc-top {
		padding: 24rpx 0;
		border-bottom: 1px solid rgb(73, 73, 73);
		margin-bottom: 20rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}

</style>
