<template>
	<view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<view style="height: 30rpx;"></view>
			<block v-for="(item,index) in list" :key="index">
				<view class="m-frame">
					<view class="padding_20rpx p-bo">
						<view class="display-a margin-bottom_20rpx">
							<image class="img-40" :src="imgUrl + '40.png'"></image>
							<view class="display-a " style="width: 564rpx;">
								<view class="m-name">{{item.name}}</view>
								<view class="m-money" v-if="item.type == 1"><span class="font-size_28rpx">￥</span>{{item.money}}</view>
								<view class="m-money" v-if="item.type == 2">卡密激活</view>
							</view>
						</view>
						<view class="display-a color_757575 margin-bottom_20rpx">
							<view class="m-count" v-if="item.day > 0">AI文案:{{item.ai_copywriting_times}}次</view>
							<view class="m-count text-align_center" v-if="item.day > 0">视频合成:{{item.second_infinite == 1 ? '无限' : item.second?(item.second/60).toFixed(0):0}}分</view>
							<view class="m-count margin-left-auto text-align_right">获得: {{item.day > 0 ? item.day+'(天)' : item.point+'(点)'}}</view>
						</view>
						<view class="display-a-js color_757575 margin-bottom_20rpx" v-if="item.day > 0">
							<view style="width: 300rpx;" class="m-count text-align_left">高保真声音:{{item.voice_twin_count}}次</view>
							<view style="width: 300rpx;" class="m-count text-align_right">高保真合成:{{item.xunfei_fidelity_words_number}}字</view>
						</view>
						<view class="display-a-js color_757575" v-if="item.day > 0">
							<view style="width: 300rpx;" class="m-count text-align_left">专业版声音:{{item.xunfei_sound_clone_words_number}}次</view>
							<view style="width: 300rpx;" class="m-count text-align_right">专业版合成:{{item.voice_twin_count}}字</view>
						</view>
					</view>
					<view class="m-date">购买时间: {{item.create_time}}</view>
				</view>
			</block>
		</mescroll-body>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
			}
		},
		
		onLoad() {
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2;
				},
			})
		},
		
		onShow() {
			this.$nextTick(() => {
				this.mescroll.resetUpScroll();
			});
		},
		
		methods: {
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.memberLogList,
					data: {
						uid: uni.getStorageSync("uid"),
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.m-date {
		padding: 20rpx;
		color: #757575;
		font-size: 26rpx;
	}
	
	.m-count {
		width: 236rpx;
		font-size: 26rpx;
	}
	
	.m-money {
		font-weight: bold;
		color: #4095FF;
		font-size: 34rpx;
		margin-left: auto;
	}
	
	.m-name {
		font-weight: 600;
		font-size: 32rpx;
		color: #FFF;
	}
	
	.img-40 {
		width: 80rpx;
		height: 80rpx;
		margin-right: 26rpx;
	}
	
	.p-bo {
		border-bottom: 1px solid rgb(88, 88, 88);;
	}
	
	.m-frame {
		width: 710rpx;
		margin: 0 20rpx 30rpx;
		background-color: #1C1C1C;
		border-radius: 10rpx;
	}
	
	page {
		border-top: 1px solid rgb(56, 56, 56);;
		background: #000000;
	}
	
</style>
