<template>
	<view>
		<view style="height: 20rpx;"></view>
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public" @click="getSel(item.id)">
					<view class="display-a margin-bottom_10rpx">
						<image class="matrix-15" :src="isSel.indexOf(item.id) != -1 ? imgUrl+'384.png' : imgUrl+'383.png'"></image>
						<image class="head" :src="item.avatar"></image>
						<view style="width: 384rpx;">
							<view class="font-size_30rpx margin-bottom_10rpx">{{item.account_name}}</view>
							<view class="color_999999 font-size_24rpx">到期时间: {{item.expires_in}}</view>
						</view>
						<block v-if="item.status == 2" class="empower" style="background: #F00;">已失效</block>
						<view v-else class="empower" :style="isEmpower(item) ? '' : 'background: #F00;'">
							{{isEmpower(item) ? '授权中' : '已失效'}}</view>
					</view>
					<view class="display-a">
						<view style="width: 156rpx;"></view>
						<view :class="item.account_group_name ? 'color_00FFCA' : 'color_999999'">所在分组:
							{{item.account_group_name?item.account_group_name:'--'}}
						</view>
					</view>
				</view>
			</block>
			
			<view style="height: 210rpx;"></view>
			
		</mescroll-body>
		
		<view class="list-bott">
			<view class="display-a margin-bottom_30rpx">
				<image class="matrix-15" @click="getAllSel()" :src="allSel ? imgUrl+'384.png' : imgUrl+'383.png'"></image>
				<view class="color_00FFCA" @click="getAllSel()">全选({{count+'/'+total}})</view>
				<view class="color_FF0000 margin-left-auto" @click="getDel()">删除</view>
			</view>
			<block v-if="isSel.length > 0">
				<picker mode="selector" @change="bindPickerChange3" :range="groupList" :range-key="'name'">
					<view class="bott-but">去选择分组</view>
				</picker>
			</block>
			<block v-else>
				<view class="bott-but" @click="getTips()">去选择分组</view>
			</block>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				obj: {
					name: '',
					selLabel: '', ////1抖音 2快手 3视频号 4小红书  // 废弃 1抖音h5发布 3矩阵首页待用户发布查询 4矩阵首页快手发布 5.B站
				},
				
				isSel: [],
				allSel: false,
				
				groupList: [],
				
				total: 0, //总条数
				count: 0, //当前页条数
			}
		},
		
		computed: {
			// 是否已授权
			isEmpower() {
				return function(row) { //使用函数返回
					let isEmpower = false;
					// true,已授权 ,为false，则为已失效
					// row.type  1D音2K手
					isEmpower = new Date(this.msToDate(new Date()).hasTime.replace(/-/g, "\/")) < new Date(this
						.date(row.expires_in).replace(/-/g, "\/"))
					
					return isEmpower;
				}
			},
		
		},
		
		onLoad(options) {
			//获取系统信息
			uni.getSystemInfo({
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 240;
				}
			})
			this.obj = JSON.parse(options.obj);
			if (this.obj.selLabel) {
				this.getGroup();
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}
			
		},
		
		onShow() {
			
		},
		
		methods: {
			
			getTips() {
				if (this.isSel.length == 0) {
					this.$sun.toast("请先选择账户", 'none');
					return;
				}
			},
			
			/*  删除  */
			getDel() {
				
				if (this.isSel.length == 0) {
					this.$sun.toast("请先选择账户", 'none');
					return;
				}
				
				uni.showModal({
					title: '提示',
					content: '确认删除该账户?',
					success: async (res) => {
						if (res.confirm) {
							this.closeBatch();
							const result = await this.$http.post({
								url: this.$api.delAccount,
								data: {
									id: this.isSel,
									uid: uni.getStorageSync("uid"),
								}
							});
							if (result.errno == 0) {
								this.isSel = [];
								this.$sun.toast(result.message);
								setTimeout(() => {
									this.$nextTick(() => {
										this.mescroll.resetUpScroll();
									});
								}, 2000);
							} else {
								this.$sun.toast(result.message, 'none');
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			
			//全选
			getAllSel() {
				
				this.allSel = !this.allSel;
				
				this.isSel = [];
				
				if (this.allSel) {
					for(let i = 0 ; i < this.list.length ; i++) {
						this.isSel.push(this.list[i].id);
					}
				}
				
			},
			
			//选中
			getSel(id) {
				let isBoo = this.isSel.indexOf(id);
				if (isBoo == -1) {
					this.isSel.push(id);
					if (Number(this.total == this.isSel.length)) {
						this.allSel = true;
					}
				}else {
					this.isSel.splice(isBoo,1);
					this.allSel = false;
				}
			},
			
			//账号状态
			bindPickerChange3(e) {
				let group_name = this.groupList[e.detail.value].name;
				let group_id = this.groupList[e.detail.value].id;
				this.updataGroup(group_id);
			},
			
			//换分组
			async updataGroup(group_id) {
				const result = await this.$http.post({
					url: this.$api.accountExchangeGroup,
					data: {
						account_group_id: group_id,
						// groupName: group_name,
						id: this.isSel,
						uid: uni.getStorageSync("uid"),
					}
				});
				if (result.errno == 0) {
					this.isSel = [];
					this.$sun.toast(result.message);
					setTimeout(() => {
						this.$nextTick(() => {
							this.mescroll.resetUpScroll();
						});
					}, 2000);
				}else {
					this.$sun.toast(result.message, 'none');
				}
			},
			
			//分组
			async getGroup() {
				const result = await this.$http.post({
					url: this.$api.accountGroupList,
					data: {
						type: this.obj.selLabel,
						uid: uni.getStorageSync("uid"),
						page: 1,
						psize: 1000
					}
				});
				if (result.errno == 0) {
					this.groupList = result.data.list;
				}
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.accountList,
					data: {
						type: this.obj.selLabel,
						uid: uni.getStorageSync("uid"),
						account_name: this.name,
						page: scroll.num,
						limit: 10
					}
				});
				if (result.errno == 0) {
					this.total = result.data.totalPage;
					let numberCount = scroll.num * 10;
					if (Number(numberCount) > Number(this.total)) {
						numberCount = this.total;
					}
					this.count = numberCount;
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			// date:创建日期 time：授权过期时间（需加）
			date(addTime) {
				// 日期或中国标准时间转毫秒数：
				// let result = new Date(date).getTime();
				let allDate = null;
				
				allDate = new Date(addTime).getTime(); // 按秒计算，所以需要*1000（原始毫秒）
				
				// 预计到期时间计算
				let endtime = this.msToDate(allDate).hasTime
				// 去除秒数截取最后：的前面数字
				let index = endtime.lastIndexOf(":")
				endtime = endtime.substring(0, index);
				return endtime
			},
			// 毫秒数或中国标准时间转日期
			msToDate(msec) {
				let datetime = new Date(msec);
				let year = datetime.getFullYear();
				let month = datetime.getMonth();
				let date = datetime.getDate();
				let hour = datetime.getHours();
				let minute = datetime.getMinutes();
				let second = datetime.getSeconds();
				let result1 = year +
					'-' +
					((month + 1) >= 10 ? (month + 1) : '0' + (month + 1)) +
					'-' +
					((date + 1) < 10 ? '0' + date : date) +
					' ' +
					((hour + 1) < 10 ? '0' + hour : hour) +
					':' +
					((minute + 1) < 10 ? '0' + minute : minute) +
					':' +
					((second + 1) < 10 ? '0' + second : second);
			
				let result2 = year +
					'-' +
					((month + 1) >= 10 ? (month + 1) : '0' + (month + 1)) +
					'-' +
					((date + 1) < 10 ? '0' + date : date);
			
				let result = {
					hasTime: result1,
					withoutTime: result2
				};
			
				return result;
			},
			
		}
	}
</script>

<style lang="scss">
	
	.list-public {
		background-color: #1B1C1F;
		color: #FFF;
	}
	
	.bott-but {
		width: 690rpx;
		padding: 24rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118),rgb(0, 236, 255) 97.869%);
		border-radius: 100rpx;
		color: #000;
		font-size: 32rpx;
		text-align: center;
	}
	
	.list-bott {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		// background-color: #FFFFFF;
		padding: 30rpx 30rpx 40rpx;
		z-index: 9;
	}
	
	.matrix-15 {
		width: 36rpx;
		height: 36rpx;
		margin-right: 20rpx;
	}
	
	.color_1E6CEB {
		color: #1E6CEB;
	}
	
	.empower {
		background: rgb(0, 134, 255);
		width: 96rpx;
		text-align: center;
		border-radius: 10rpx;
		padding: 8rpx;
		font-size: 24rpx;
		color: #FFFFFF;
		margin-left: auto;
	}
	
	.head {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
	
</style>

