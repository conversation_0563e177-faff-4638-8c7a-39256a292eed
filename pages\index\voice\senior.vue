<template>
	<view>
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'172.jpg'+')'}">
			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_32rpx" @click="navig()">
					高保真音色克隆
				</view>
			</view>
		</view>
		<view style="margin: 20rpx 20rpx 20rpx;">

			<view class="font-weight_bold font-size_30rpx margin-bottom_20rpx color_FFFFFF">克隆标题<span
					class="color_FF0000 margin-left_10rpx">*</span></view>
			<input type="text" v-model="name" class="c-input" placeholder="请输入声音克隆标题" placeholder-class="placeholder" />

			<!-- <view class="display-a margin-bottom_40rpx">
				<view class="font-weight_bold font-size_30rpx color_FFFFFF">选择性别<span
						class="color_FF0000 margin-left_10rpx">*</span></view>
				<view class="margin-left-auto">
					<radio-group @change="radioChange" class="display-a">
						<label class="display-a" style="margin: 0 10rpx 0 20rpx;" v-for="(item, index) in sexArr"
							:key="index">
							<view>
								<radio style="transform:scale(0.7)" color="#A693FF" :value="item.name" :checked="gender == item.name" />
							</view>
							<view :class="item.name == gender ? 'color_A693FF' : 'color_FFFFFF'">{{item.name}}</view>
						</label>
					</radio-group>
				</view>
			</view> -->

			<block v-if="audioUrl">
				<view class="display-a" style="padding: 70rpx 0 70rpx;">
					<view class="display-ac-jc" style="width: 354rpx;" @click="getStep()">
						<image class="img-173" :src="imgUrl+'173.png'"></image>
						<view class="color_FFFFFF">重新上传</view>
					</view>
					<view class="display-ac-jc" style="width: 356rpx;" @click="playAudio()">
						<image class="img-173" :src="isPlay == 1 ? imgUrl+'175.png' : imgUrl+'174.png'"></image>
						<view class="color_FFFFFF">{{isPlay == 1 ? '暂停' : '播放'}}</view>
					</view>
				</view>
			</block>
			<block v-else>
				<view class="v-frame display-ac-jc" @click="chooseFile()">
					<image class="img-164" :src="imgUrl + '164.png'"></image>
					<view class="color_08BB71 font-size_32rpx margin-bottom_20rpx">从微信聊天记录里上传</view>
					<view class="color_B7B7B7 font-size_26rpx">支持mp3、wav和m4a格式音频并建议时长30秒以上</view>
				</view>
			</block>

			<view class="display-a-js color_FFFFFF margin-bottom_20rpx">
				<view class="display-a">
					<view class="clone-line"></view>
					<view class="font-size_36rpx font-weight_bold">声音录制要求</view>
				</view>
				<view class="display-a" @click="getRecord()">
					<image class="img-63" :src="imgUrl+'63.png'"></image>
					<view class="font-size_26rpx">克隆记录</view>
					<image class="img-58" :src="imgUrl+'58.png'"></image>
				</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">音频文件上传支持：wav、mp3、m4a格式</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">上传的音频要求30秒以上,容量10M以内</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">录制过程确保环境安静没有明显的环境噪音</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">录制过程中不要长时间不说话要保证语速平稳</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">录制过程中不要声音语调时高时低保持音量均衡</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">录制过程中只能有一个人的声音避免其它人声</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">声音不得进行混响特效处理，更不得带背景音乐和音效</view>
			</view>
			<view class="display-a margin-bottom_20rpx">
				<view class="c-tips"></view>
				<view class="font-size_26rpx color_FFFFFF">要保证为原音录制不得对音频进行切分与合并以及剪辑</view>
			</view>

		</view>
		<view style="height: 130rpx;"></view>
		<view class="next-but" @click="openProtocol()">
			开始克隆
			<span class="margin-left_10rpx">
				{{user.voice_twin_count ? '1次/1次' : tallySetObj.voice_high_deduct+'点/1次'}}
			</span>
		</view>

		<!-- <block v-if="customerConfig.customer_type == 'on_line'">
			<button open-type="contact">
				<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl+'kefu.gif'" ></image>
			</button>
		</block>
		<block v-else>
			<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl+'kefu.gif'" ></image>
		</block> -->

		<sunui-popup ref="pop">
			<template v-slot:content>
				<view style="overflow:auto;padding:10rpx 30rpx 20rpx;">
					<scroll-view :scroll-y="true" style="height: 700rpx;">
						<rich-parser :html="cloneSet.protocol "
							domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load ref="article" selectable
							show-with-animation use-anchor>
							<!-- 加载中... -->
						</rich-parser>
					</scroll-view>

					<view class="display-a-js margin-top_20rpx">
						<view class="c-close" @click="close()">关闭</view>
						<view class="c-agree" @click="but()">同意并克隆</view>
					</view>

				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop5">
			<template v-slot:content>
				<image class="img-qr-code" :src="customerConfig.customer_qr_code"></image>
			</template>
		</sunui-popup>

	</view>
</template>

<script>
	const voiceInnerAudioContext = uni.createInnerAudioContext();
	export default {
		data() {
			return {

				imgUrl: this.$imgUrl,

				heightSystemss: '',
				statusBarHeightss: '',

				name: '',

				sexArr: [{
						name: '男'
					},
					{
						name: '女'
					}
				],
				gender: '男',
				vid: '', //记录ID
				audioUrl: '',

				isPlay: '2', //1播放 2暂停

				isWhether: true, //判断重复点击

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				cloneSet: {},

				user: {},

				isKefu: true, //true隐藏 false展开

				customerConfig: uni.getStorageSync('customerConfig'), //客服配置

			}
		},

		onLoad(options) {
			if (options.vid) {
				this.vid = options.vid;
			}
			if (options.name) {
				this.name = options.name;
			}
			if (options.sex) {
				this.gender = options.sex;
			}
			this.getSystemInfo();
			this.getCloneSet();
		},

		onShow() {
			this.userInfo();
		},

		onUnload() {

			if (voiceInnerAudioContext && (!voiceInnerAudioContext.paused)) {
				voiceInnerAudioContext.stop();
			}

		},

		methods: {

			//用户信息
			async userInfo() {
				const result = await this.$http.post({
					url: this.$api.userInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {
					this.user = result.data;
				}
			},

			getKefu() {
				if (this.isKefu) {
					this.isKefu = false;
				} else {
					if (this.customerConfig.customer_type == 'on_line') {
						return;
					} else if (this.customerConfig.customer_type == 'phone') {
						if (this.customerConfig.customer_phone) {
							this.$sun.phone(this.customerConfig.customer_phone);
						} else {
							this.$sun.toast("暂无联系方式", 'error');
						}
					} else if (this.customerConfig.customer_type == 'qr_code') {
						this.$refs.pop5.show({
							style: 'background-color:#000;width:600rpx;border-radius:10rpx;',
							bottomClose: true,
							shadeClose: false,
						});
					}
				}
			},

			openProtocol() {

				if (uni.getStorageSync('uid')) {

					if (!this.audioUrl) {
						this.$sun.toast("请先上传音频", 'error');
						return;
					}

					if (!this.name) {
						this.$sun.toast("请输入标题", 'error');
						return;
					}

					this.$refs.pop.show({
						title: this.cloneSet.protocol_name,
						style: 'background-color:#fff;width:700rpx;border-radius:10rpx;',
						// bottomClose: true,
						shadeClose: false,
					});
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								// this.navig();
							}
						}
					})
				}

			},

			close() {
				this.$refs.pop.close();
			},

			//克隆设置
			async getCloneSet() {
				const result = await this.$http.post({
					url: this.$api.cloneSet
				});
				if (result.errno == 0) {
					this.cloneSet = result.data;
				}
			},

			//播放音频
			playAudio() {

				voiceInnerAudioContext.src = this.audioUrl;

				if (this.isPlay == 2) {
					this.isPlay = 1;
					voiceInnerAudioContext.play();
					voiceInnerAudioContext.onPlay(() => {
						// console.log('开始播放');
					});
					voiceInnerAudioContext.onEnded(() => {
						this.isPlay = 2;
						voiceInnerAudioContext.destroy();
						// this.$sun.toast("音频播放完成");
						// console.log('音频播放结束：');
					});
					voiceInnerAudioContext.onError((err) => {
						voiceInnerAudioContext.destroy();
						// console.log('播放音频出错：', err);
					});
				} else {
					this.isPlay = 2;
					voiceInnerAudioContext.pause();
					voiceInnerAudioContext.onPause(() => {
						// console.log('暂停播放');
					});
				}

				// console.log("---->",this.isPlay);

			},

			getStep() {
				this.audioUrl = '';
			},

			//音频上传
			chooseFile() {
				uni.chooseMessageFile({
					count: 1,
					type: 'file',
					extension: ['.mp3', '.m4a', '.wav', 'wav', 'mp3', 'm4a', '.MP3', '.M4A', '.WAV', 'WAV', 'MP3',
						'M4A'
					],
					success: (res) => {
						// console.log("从聊天记录中获取文件", res);
						const {
							errMsg,
							tempFiles
						} = res;
						if (errMsg == "chooseMessageFile:ok" && tempFiles.length) {
							const {
								name,
								size,
								path
							} = tempFiles[0];
							// console.log("临时文件", {
							// 	size,
							// 	path
							// });
							if ((name.slice(-4) != ".mp3") && (name.slice(-4) != ".MP3") && (name.slice(-4) !=
									".m4a") && (name.slice(-4) != ".M4A") && (name.slice(-4) !=
									".wav") && (name.slice(-4) != ".WAV")) {
								return uni.showToast({
									icon: "none",
									title: "请上传mp3,m4a,wav格式音频文件！",
									duration: 2000,
								});
							}
							if (size / 1024 / 1024 > 10) {
								return uni.showToast({
									icon: "none",
									title: "请上传10M内的音频文件！",
								});
							}

							// console.log("从聊天记录中获取文件", name,path);
							// formData.value.audioName = name
							this.upload(path);
						}
					},
				});
			},
			upload(path) {
				uni.showLoading()
				// 调用uni.uploadFile将文件上传至服务器
				uni.uploadFile({
					url: this.$api.upload, // 设置上传接口地址
					filePath: path, // 需要上传的文件路径
					name: 'file', // 后台接收文件时对应的字段名称
					fileType: 'audio', // 指定文件类型
					header: {
						// "Content-Type": "multipart/form-data",
					},
					success: (response) => {
						// TODO: 处理上传成功后的操作
						const data = JSON.parse(response.data)
						// console.log('文件上传成功',data);
						// formData.value.accompanyInfo = data.data.url
						if (data.errno != 0) {
							uni.showToast({
								title: '文件上传失败',
								icon: 'none'
							});
						} else {
							this.audioUrl = data.data;
							// let index = this.audioUrl.lastIndexOf('/'); // 获取最后一个/的位置
							// let lastSegment = this.audioUrl.substring(index + 1); // 截取最后一个/后的值
							// this.audioName = lastSegment;
							this.$sun.toast("文件上传成功");
							setTimeout(() => {
								this.isStep = 4;
							}, 1500);
						}
						uni.hideLoading()
						return
					},
					fail(error) {
						uni.hideLoading()
						// console.log('文件上传失败');
						// console.log(error);

						// TODO: 处理上传失败后的操作
					}
				});
			},

			//克隆训练
			async but() {

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				this.close();

				const result = await this.$http.post({
					url: this.vid ? this.$api.ttsContinue : this.$api.ttsUpload,
					data: {
						uid: uni.getStorageSync('uid'),
						name: this.vid ? '' : this.name,
						voice_urls: [this.audioUrl],
						sex: this.vid ? '' : this.gender,
						voice_id: this.vid ? this.vid : '',
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/assets/digital-assets?tabsId=3&tabsNextId=2'
						})
						this.isWhether = true;
					}, 2000);
				} else {
					if (result.message == 'voice name already exists') {
						this.$sun.toast("声音名称已存在", 'none');
					} else {
						if (result.errno == -2) {
							uni.showModal({
								content: result.message,
								cancelText: "取消",
								confirmText: "去开通",
								success: (res) => {
									if (res.confirm) {
										uni.navigateTo({
											url: '/pages/my/member'
										})
									} else if (res.cancel) {

									}
								}
							})
						} else {
							this.$sun.toast(result.message, 'none');
						}
					}
					this.isWhether = true;
				}

			},

			//克隆记录
			getRecord() {

				if (uni.getStorageSync('uid')) {
					// uni.navigateTo({
					// 	url: '/pages/index/voice/record?type=2'
					// })

					uni.navigateTo({
						url: '/pages/assets/digital-assets?tabsId=3&tabsNextId=2'
					})

				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								// this.navig();
							}
						}
					})
				}
			},

			//选择性别
			radioChange(e) {
				this.gender = e.detail.value;
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	.img-qr-code {
		width: 500rpx;
		height: 500rpx;
		margin: 50rpx;
	}

	.kefu-2 {
		right: -50rpx;
		opacity: 0.5;
		transition: all 1s linear;
	}

	.kefu-1 {
		right: 10rpx;
		transition: all 1s linear;
	}

	.kefu {
		width: 100rpx;
		height: 100rpx;
		position: fixed;
		z-index: 99;
		// right: -50rpx;
		bottom: 240rpx;
	}

	.c-agree {
		width: 350rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(188, 120, 248), rgb(135, 80, 242) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 20rpx 0;
	}

	.c-close {
		width: 270rpx;
		text-align: center;
		border: 1px solid rgb(203, 202, 202);
		border-radius: 10rpx;
		background: rgb(255, 255, 255);
		font-size: 32rpx;
		color: #929292;
		padding: 20rpx 0;
	}

	.img-173 {
		width: 96rpx;
		height: 96rpx;
		margin-bottom: 12rpx;
	}

	.clone-line {
		width: 8rpx;
		height: 28rpx;
		background: linear-gradient(180.00deg, rgb(187, 119, 248), rgb(135, 80, 242) 100%);
		border-radius: 2rpx;
		margin-right: 10rpx;
	}

	.img-164 {
		width: 34rpx;
		height: 34rpx;
		margin-bottom: 14rpx;
	}

	.v-frame {
		width: 710rpx;
		border: 1px dashed rgb(106, 106, 106);
		border-radius: 10rpx;
		margin-bottom: 30rpx;
		padding: 60rpx 0;
	}

	.next-but {
		width: 710rpx;
		position: fixed;
		bottom: 40rpx;
		text-align: center;
		padding: 26rpx 0;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(102, 171, 255), rgb(135, 80, 242) 100%);
		margin: 0 20rpx;
		color: #FFF;
		font-size: 30rpx;
		z-index: 9;
	}

	.c-tips {
		background: linear-gradient(180.00deg, rgb(161, 91, 246), rgb(131, 32, 253) 100%);
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		margin-right: 12rpx;
	}

	.img-58 {
		width: 26rpx;
		height: 26rpx;
		margin-left: 6rpx;
		margin-top: 2rpx;
	}

	.img-63 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
	}

	.color_A693FF {
		color: #A693FF;
	}

	.c-input {
		width: 670rpx;
		// background-color: #434343;
		background-color: #2E313A;
		border-radius: 10rpx;
		padding: 20rpx;
		// margin: 20rpx 0;
		margin-bottom: 40rpx;
		color: #FFF;
	}

	.bg {
		width: 750rpx;
		height: 510rpx;
		background-repeat: no-repeat;
		background-size: cover;
	}

	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		// font-size: 30rpx;
		// font-weight: bold;
		display: flex;
		align-items: center;
	}

	page {
		border-top: none;
		background-color: #191919;
	}
</style>