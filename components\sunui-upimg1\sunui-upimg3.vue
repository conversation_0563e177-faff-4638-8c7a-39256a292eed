<template>
	<view class="sunui-add-list">
		<view :class="itemsColor" v-for="(item, index) in imgLists" :key="index">
			<image :src="item.url" mode="aspectFit" :data-imgurl="item.url" @tap="showImgs" :class="itemsImgColor"></image>
			<view class="sunui-add-list-remove sunui-icons icon-close" :style="{ color: closeBtnColor }" @tap.stop="removeImg" :id="'sunui-items-img-' + index"><icon type="clear" :color="closeBtnColor"></icon></view>
			<!-- <view class="upload-progress"><progress :percent="item.progress" :stroke-width="progressSize" :activeColor="progressColor" :backgroundColor="progressBGColor" /></view> -->
			<view class="sunui-add-list-reup" @tap.stop="retry" :data-index="index" v-if="item.error">
				<text class="sunui-add-list-reup-icon sunui-icons icon-retry"></text>
				<text class="sunui-add-list-reup-text">失败重试</text>
			</view>
		</view>
		<view :class="itemsColor" class="sunui-add-list-btn" @tap="addImg" v-if="imgLists.length < maxFileNumber">
			<image :class="bgImgColor" :src="bgImg" ></image>
			<view class="sunui-add-list-btn-text">{{ btnName }}</view>
		</view>
	</view>
</template>
<script>
export default {
	props: {
		// 上传前钩子
		beforeUpload: {
			type: Function
		},
		maxFileNumber: {
			type: Number,
			default: 9
		},
		btnName: {
			type: String,
			default: ''
		},
		items: {
			type: Array,
			default: function() {
				return [];
			}
		},
		closeBtnColor: {
			type: String,
			default: '#666666'
		},
		uploadServerUrl: {
			type: String,
			default: ''
		},
		progressSize: {
			type: Number,
			default: 1
		},
		progressColor: {
			type: String,
			default: '#27BD81'
		},
		progressBGColor: {
			type: String,
			default: '#F8F8F8'
		},
		fileName: { type: String, default: 'img' },
		formData: {
			type: Object,
			default: function() {
				return {};
			}
		},
		itemsImgColor: {
			type: String,
			default: 'sunui-add-list-items-img'
		},
		itemsColor: {
			type: String,
			default: 'sunui-add-list-items'
		},
		bgImg: {
			type: String,
			default: ''
		},
		bgImgColor: {
			type: String,
			default: 'sunui-add-list-btn-icon'
		},
		imgMode: { type: String, default: 'widthFix' },
		header: {
			type: Object,
			default: function() {
				return {};
			}
		}
	},
	data() {
		return {
			imgLists: [],
			updatting: false
		};
	},
	watch: {
		imgLists(newVal, oldVal) {
			if (!this.updatting) {
				this.$emit('change', newVal);
			}
		}
	},
	methods: {
		clearAllImgs() {
			this.imgLists = [];
		},
		addImg() {
			var num = this.maxFileNumber - this.imgLists.length;
			if (num < 1) {
				return false;
			}
			//uni.showLoading({title:""});
			uni.chooseImage({
				count: num,
				sizeType: ['original'],
				sourceType: ['album'],
				success: res => {
					for (let i = 0; i < res.tempFilePaths.length; i++) {
						this.getImage(res.tempFilePaths[i]);
					}
					//uni.hideLoading();
				},
				complete: function() {
					//uni.hideLoading();
				},
				fail: function() {
					//uni.hideLoading();
				}
			});
		},
		
		//获取图片信息
		getImage(url) {
			uni.getImageInfo({
				src: url,
				success: async (image) => {
					
					if (this.beforeUpload) {
						const valid = await this.beforeUpload(image, 2);
						if (valid === false) {
							return false;
						}
					}
					
					this.imgLists.push({ url: url, progress: 0, error: false });
					
					// if ((image.width == 1080 || image.width == 1920) && (image.height == 1080 || image.height == 1920)) {
						
					// }else {
					// 	uni.showModal({
					// 		content: "请上传1080*1920或1920*1080的图片",
					// 		showCancel: false,
					// 		success: () => {
								
					// 		}
					// 	})
					// }
				},
				fail: err => {
					console.log(err);
				}
			});
		},
		
		removeImg(e) {
			var index = e.currentTarget.id.replace('sunui-items-img-', '');
			var removeImg = this.imgLists.splice(index, 1);
			this.$emit('removeImg', removeImg[0]);
		},
		showImgs(e) {
			var currentImg = e.currentTarget.dataset.imgurl;
			var imgs = [];
			for (let i = 0; i < this.imgLists.length; i++) {
				imgs.push(this.imgLists[i].url);
			}
			uni.previewImage({
				urls: imgs,
				current: currentImg
			});
		},
		upload(index) {
			if (this.updatting) {
				return;
			}
			this.updatting = true;
			if (!index) {
				index = 0;
			}
			uni.showLoading({ title: '图片上传中' });
			this.uploadBase(index);
		},
		retry(e) {
			var index = e.currentTarget.dataset.index;
			this.upload(index);
		},
		uploadBase(index) {
			// 全部上传完成
			if (index > this.imgLists.length - 1) {
				uni.hideLoading();
				this.updatting = false;
				this.$emit('uploaded', this.imgLists);
				return;
			}
			// 验证后端
			if (this.uploadServerUrl == '') {
				uni.showToast({ title: '请设置上传服务器地址', icon: 'none' });
				return;
			}
			// 检查是否是默认值
			if (this.imgLists[index].progress >= 1) {
				this.uploadBase(index + 1);
				return;
			}
			this.imgLists[index].error = false;
			// 创建上传对象
			const task = uni.uploadFile({
				url: this.uploadServerUrl,
				filePath: this.imgLists[index].url,
				name: 'file' || this.fileName,
				formData: this.formData,
				header: this.header,
				success: uploadRes => {
					uploadRes = JSON.parse(uploadRes.data);

					console.log('uploadRes', uploadRes);

					if (uploadRes.errno != 0) {
						uni.showToast({ title: '上传失败 : ' + uploadRes.data, icon: 'none' });
						this.error(index);
					} else {
						//上传图片成功
						this.imgLists[index].progress = 100;
						this.imgLists[index].url = uploadRes.data;
						this.imgLists[index].result = uploadRes;
						this.uploadBase(index + 1);
					}
				},
				fail: e => {
					uni.showToast({ title: '上传失败，请重试', icon: 'none' });
					this.error(index);
				}
			});
			task.onProgressUpdate(res => {
				if (res.progress > 0) {
					this.imgLists[index].progress = res.progress;
					this.imgLists.splice(index, 1, this.imgLists[index]);
				}
			});
		},
		// 上传错误
		error(index) {
			this.updatting = false;
			setTimeout(() => {
				this.imgLists[index].progress = 0;
				this.imgLists[index].error = true;
				this.$emit('uploaderror');
			}, 500);
		},
		// 设置默认值
		setItems(items) {
			this.imgLists = [];
			for (let i = 0; i < items.length; i++) {
				this.imgLists.push({ url: items[i], progress: 100 });
			}
		}
	}
};
</script>
<style scoped>
.sunui-add-list {
	display: flex;
	flex-wrap: wrap;
}
.sunui-add-list-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.sunui-add-list-btn-text {
	font-size: 26rpx;
	line-height: 36rpx;
	text-align: center;
	color: #999999;
	width: 100%;
}
/* .sunui-add-list-btn-icon {
	font-size: 80rpx;
	height: 80rpx;
	line-height: 80rpx;
	margin-bottom: 20rpx;
	color: #999999;
} */
.sunui-add-list-btn-icon {
	width: 68rpx;
	height: 68rpx;
}

.sunui-add-list-items {
	width: 686rpx;
	height: 400rpx;
	overflow: hidden;
	border: 1px dashed rgb(87, 86, 86);
	position: relative;
	border-radius: 10rpx;
}

.sunui-add-list-btn-icon2 {
	width: 66rpx;
	height: 66rpx;
}

.sunui-add-list-items-img2 {
	width: 196rpx;
	height: 196rpx;
	border-radius: 10rpx;
	position: relative;
}

.sunui-add-list-items2 {
	width: 200rpx;
	height: 200rpx;
	border: 1px dashed rgb(87, 86, 86);
	border-radius: 10rpx;
	/* margin-right: 20rpx;
	margin-bottom: 20rpx; */
	position: relative;
}

.sunui-add-list-image {
	width: 222rpx;
}
.sunui-add-list-remove {
	width: 50rpx;
	height: 50rpx;
	line-height: 50rpx;
	text-align: center;
	font-size: 40rpx;
	position: absolute;
	z-index: 5;
	right: 10rpx;
	top: 10rpx;
	color: #888888;
}
.upload-progress {
	position: absolute;
	z-index: 2;
	left: 0;
	bottom: 10rpx;
	width: 180rpx;
	padding: 0 21rpx;
}
.sunui-add-list-reup {
	position: absolute;
	z-index: 3;
	left: 0;
	top: 0rpx;
	width: 222rpx;
	height: 222rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.3);
	flex-direction: column;
}
.sunui-add-list-reup-icon {
	text-align: center;
	width: 100%;
	color: #ffffff;
	display: block;
	font-size: 80rpx;
	line-height: 100rpx;
}
.sunui-add-list-reup-text {
	text-align: center;
	width: 100%;
	color: #ffffff;
	display: block;
	font-size: 20rpx;
	line-height: 30rpx;
}
.sunui-add-list-img {
	width: 222rpx;
}
</style>
