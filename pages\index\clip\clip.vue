<template>
	<view>

		<view class="bg" :style="{'background-image': 'url('+imgUrl+'405.png'+')'}">

			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_30rpx" @click="navig()">
					视频创作
				</view>
			</view>

			<view class="display color_FFFFFF">
				<view class="display-mt" @click="getBatch(6)">
					<image class="img-408" :src="imgUrl+'408.png'"></image>
					<view>数字人</view>
				</view>
				<view class="display-ac-jc" style="margin-left: 100rpx;" v-if="param.decode_img">
					<image class="decode-img" mode="widthFix" :src="param.decode_img"></image>
				</view>
				<view class="display-ac-jc c-frame" @click="getBatch(6)" v-else>
					<image class="img-407" :src="imgUrl+'407.png'"></image>
					<view>添加数字人</view>
				</view>
			</view>

		</view>

		<view class="c-frame-txt">
			<input class="padding_20rpx color_FFFFFF" style="width: 670rpx;" v-model="voideTitle" type="text"
				placeholder="请输入视频标题" placeholder-class="placeholder" />
		</view>

		<view class="list-public">
			<view class="display-a">
				<view @click="getDriveType(1)" :class="driveType == 1 ? 'c-type-sel' : 'c-type-not-sel'">
					<span :class="driveType == 1 ? 'c-type-font' : ''">AI文本驱动</span>
				</view>
				<view @click="getDriveType(2)" :class="driveType == 2 ? 'c-type-sel' : 'c-type-not-sel'">
					<span :class="driveType == 2 ? 'c-type-font' : ''">语音驱动</span>
				</view>
			</view>
			<view class="display-a padding_20rpx" style="border-bottom: 1px solid rgb(17, 19, 23);"
				v-if="driveType == 1">
				<view class="">
					<view style="position: relative;" @click="getVoiceType(3)"
						v-if="cloneSet.xunfei_sound_clone_swich == 1">
						<view class="recommend">推荐</view>
						<view class="c-font" :class="voiceType == 3 ? 'c-font-1' : 'c-font-2'">专业版</view>
						<view class="c-line-bott" :class="voiceType == 3 ? 'c-line-bott-1' : 'c-line-bott-2'"></view>
					</view>
				</view>
				<view class="">
					<view style="position: relative;" @click="getVoiceType(2)" v-if="cloneSet.voice_high_open == 1">
						<view class="recommend">推荐</view>
						<view class="c-font" :class="voiceType == 2 ? 'c-font-1' : 'c-font-2'">高保真</view>
						<view class="c-line-bott" :class="voiceType == 2 ? 'c-line-bott-1' : 'c-line-bott-2'"></view>
					</view>
				</view>
				<view class="">
					<view @click="getVoiceType(1)" class="c-font" :class="voiceType == 1 ? 'c-font-1' : 'c-font-2'">入门版
					</view>
					<view class="c-line-bott" :class="voiceType == 1 ? 'c-line-bott-1' : 'c-line-bott-2'"></view>
				</view>


			</view>
			<view class="drive-type" v-if="driveType == 2">
				<view style="background-color: #323232;height: 50rpx;"></view>
				<block v-if="isPop == 2">
					<sound-recording colorType="2" bgColor="#323232" backgroundColor="#323232" inactiveColor="#323232"
						@confirm="getConfirm"></sound-recording>
				</block>
				<view class="display-a" style="padding: 20rpx 30rpx;" v-if="audioUrl">
					<image class="img-93" :src="imgUrl + '93.png'"></image>
					<view class="v-name">{{audioName}}</view>
					<view class="display-a-jc v-play" @click="playAudio(audioUrl)">
						<image class="img-94" :src="isPlay == 1 ? imgUrl + '95.png' : imgUrl + '94.png'"></image>
						<view>{{isPlay == 1 ? '暂停' : '播放'}}</view>
					</view>
					<image @click="delAudio()" class="img-96" :src="imgUrl + '96.png'"></image>
				</view>
				<view class="v-upload" @click="chooseFile()">
					<view class="v-add margin-bottom_20rpx"><span class="font-weight_bold margin-right_16rpx">+</span>
						{{audioUrl ? '点击重新上传音频文件' : '点击上传音频文件'}}
					</view>
					<view class="color_B7B7B7">支持mp3,m4a,wav音频文件,请上传3分钟内的音频文件</view>
				</view>
			</view>

			<block v-if="driveType == 1">

				<block v-if="voiceType == 1">
					<view v-if="soundObj.id">
						<view class="sel-voice">
							<view class="display-a">
								<block v-if="soundObj.url">
									<view class="display-a-jc r-play" @click="playAudio(soundObj.url)">
										<image class="img-95" :key="updateKey"
											:src="isPlay == 1 ? imgUrl+'95.png' : imgUrl+'94.png'"></image>
									</view>
								</block>
								<block v-else>
									<view class="display-a-jc r-play" @click="getSoundRefresh()">
										<view class="circle-loading">
											<view class="dot">
												<view class="first-dot"></view>
											</view>
											<view class="dot"></view>
											<view class="dot"></view>
											<view class="dot"></view>
										</view>
									</view>
								</block>
								<block v-if="soundObj.url">
									<view>
										<view class="color_FFFFFF font-size_32rpx font-weight_bold">{{soundObj.name}}
										</view>
									</view>
									<view class="display-a-jc voice-listening" @click="playAudio(soundObj.url)">
										<image class="img-247" :src="imgUrl+'247.png'"></image>
										<view class="font-size_26rpx">试听</view>
									</view>
								</block>
								<block v-else>
									<view style="width: 400rpx;">
										<view class="color_FFFFFF font-size_32rpx font-weight_bold margin-bottom_20rpx">
											{{soundObj.name}}
										</view>
										<progress :percent="percent" border-radius="10" stroke-width="5"
											activeColor="#4FFF76" backgroundColor="#FFF" />
									</view>
									<view class="margin-left-auto text-align_center">
										<view class="font-size_30rpx color_FFFFFF margin-bottom_10rpx">{{percent+'%'}}
										</view>
										<view class="color_4FFF76 font-size_24rpx">合成进度</view>
									</view>
								</block>
							</view>
							<view class="display-ac-jc margin_20rpx padding_30rpx_0" @click="getAudio(2)">
								<image class="img-239" :src="imgUrl+'348.png'"></image>
								<view class="c-type-font font-size_30rpx">重新合成音频</view>
							</view>
						</view>

					</view>

					<view class="display-ac-jc c-info-2" @click="getAudio(1)" v-else>
						<image class="img-239" :src="imgUrl+'348.png'"></image>
						<view class="c-type-font font-size_30rpx">点击合成音频</view>
					</view>
				</block>

				<block v-if="voiceType == 3 || voiceType == 2">
					<view class="c-frame-txt" style="margin: 0;padding-bottom: 20rpx;">
						<block v-if="soundObj.id">
							<view class="sel-voice" style="margin: 20rpx 20rpx 0;">
								<view class="display-a">

									<view class="display-a-jc r-play" @click="playAudio(soundObj.url)">
										<image class="img-95" :key="updateKey"
											:src="isPlay == 1 ? imgUrl+'95.png' : imgUrl+'94.png'"></image>
									</view>

									<view>
										<view class="color_FFFFFF font-size_32rpx font-weight_bold">{{soundObj.name}}
										</view>
									</view>
									<view class="display-a-jc voice-listening" @click="delAudio2()">
										<image class="img-247" :src="imgUrl+'244.png'"></image>
										<view class="font-size_26rpx">删除</view>
									</view>
								</view>
								<view class="display-ac-jc c-info-3" @click="getAudio(2)">
									<image class="img-239" :src="imgUrl+'348.png'"></image>
									<view class="c-type-font font-size_30rpx">重新选择音频</view>
								</view>
							</view>

						</block>
						<block v-else>
							<view class="display-a c-info">
								<image class="img-83" :src="imgUrl+'406.png'"></image>
								<block v-if="voiceType == 2">
									<view>
										<view class="font-size_32rpx">{{voicesObj.name}}</view>
									</view>
								</block>
								<block v-if="voiceType == 3">
									<view>
										<view class="display-a color_c7c7c7">
											<view class="font-size_32rpx color_FFFFFF margin-right_20rpx">
												{{voicesObj.name}}
											</view>
											<view class="font-size_26rpx">{{voicesObj.sex == 1 ? '男' : '女'}}/</view>
											<view class="font-size_26rpx">
												{{voicesObj.age_group == 1 ? '儿童' : voicesObj.age_group == 2 ? '青年' : voicesObj.age_group == 3 ? '中年' : '中老年'}}/
											</view>
											<view class="font-size_26rpx">
												{{voicesObj.language == 1 ? '英文' : voicesObj.language == 2 ? '日语' : voicesObj.language == 3 ? '韩语' : voicesObj.language == 4 ? '俄语' : '中文'}}
											</view>
										</view>
									</view>
								</block>
								<view class="display-a margin-left-auto" @click="getBatch(3)">
									<image class="img-85" :src="imgUrl+'352.png'"></image>
									<view class="color_FFFFFF font-size_26rpx">音色</view>
								</view>
							</view>
							<view class="frame-top">
								<textarea v-model="msgText" maxlength="520" :cursor-spacing="50"
									:placeholder="voiceType == 2 ? '高保真默认加了<speak></speak>标签,所以默认有15个字' : '我是虚拟数字人,请输入您的配音文案'"
									placeholder-class="placeholder"></textarea>
								<view class="display-a margin-bottom_20rpx">
									<view class="msg-tips" v-if="voiceType == 2">{{msgText.length+15}}/500字</view>
									<view class="msg-tips" v-if="voiceType == 3">{{msgText.length}}/500字</view>
								</view>
								<view class="display-a" style="color: #EBEAEA;">
									<view class="display-a margin-right_40rpx" @click="openAICreation()">
										<image class="img-409" :src="imgUrl+'409.png'"></image>
										<view class="font-size_26rpx">AI文案</view>
									</view>
									<view class="display-a margin-right_40rpx" @click="startStream()">
										<image class="img-409" :src="imgUrl+'410.png'"></image>
										<view class="font-size_26rpx">一键仿写</view>
									</view>
									<!-- <view class="display-a">
										<image class="img-409" :src="imgUrl+'411.png'"></image>
										<view class="font-size_26rpx">AI翻译</view>
									</view> -->
									<view class="clear-clip" @click="getClear()">清空</view>
								</view>
							</view>
						</block>
					</view>
				</block>
				<block v-if="voiceType == 2">
					<view class="display-a margin-bottom_10rpx color_FFFFFF">
						<view class="width_176rpx-center padding-bottom_20rpx" @click="insertPause()">
							<image class="img-353" :src="imgUrl+'353.png'"></image>
							<view class="font-size_26rpx">插入停顿</view>
						</view>
						<view class="width_176rpx-center padding-bottom_20rpx" @click="characters()">
							<image class="img-353" :src="imgUrl+'354.png'"></image>
							<view class="font-size_26rpx">多音字</view>
						</view>
						<picker mode="selector" :range="sayAsList" :range-key="'name'" @change="getChange">
							<view class="width_176rpx-center padding-bottom_20rpx">
								<image class="img-353" :src="imgUrl+'355.png'"></image>
								<view class="font-size_26rpx">数字读法</view>
							</view>
						</picker>
						<view class="width_176rpx-center padding-bottom_20rpx" @click="getOpenSSML()">
							<image class="img-353" :src="imgUrl+'412.png'"></image>
							<view class="font-size_26rpx">使用说明</view>
						</view>
					</view>
				</block>

			</block>

		</view>

		<view style="height: 160rpx;"></view>

		<view class="pos-bott">
			<block v-if="driveType == 1 && (voiceType == 2 || voiceType == 3)">
				<view class="display-a-js">
					<view class="listening" @click="getAudio(2)">
						<image class="img-196" :src="imgUrl+'348.png'"></image>
						<view class="font-size_26rpx color_FFFFFF">历史音频</view>
					</view>
					<view class="listening" @click="getVideoSendTts(2,'')">
						<image class="img-196" :src="imgUrl+'349.png'"></image>
						<view class="font-size_26rpx color_FFFFFF">{{isPlay == 1 ? '暂停' : '试听'}}</view>
					</view>
					<view class="c-but2" @click="getBatch(1)">
						生成视频
						<!-- <span class="margin-left_10rpx" v-if="isMember != 1">
							<block v-if="param.isSel == 3">{{tallySetObj.composite_deduct}}点/分</block>
							<block v-if="param.isSel == 2">{{tallySetObj.woni_video_deduct}}点/秒</block>
							<block v-if="param.isSel == 1">{{tallySetObj.video_deduct}}点/秒</block>
							<block v-if="param.isSel == 4">{{tallySetObj.composite_deduct_four}}点/分</block>
						</span> -->
					</view>
				</view>
			</block>
			<block v-else>
				<view class="c-but" @click="getBatch(1)">
					生成视频
					<!-- <span class="margin-left_10rpx" v-if="isMember != 1">
						<block v-if="param.isSel == 3">{{tallySetObj.composite_deduct}}点/分</block>
						<block v-if="param.isSel == 2">{{tallySetObj.woni_video_deduct}}点/秒</block>
						<block v-if="param.isSel == 1">{{tallySetObj.video_deduct}}点/秒</block>
						<block v-if="param.isSel == 4">{{tallySetObj.composite_deduct_four}}点/分</block>
					</span> -->
				</view>
			</block>
		</view>

		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="color_FFFFFF padding_30rpx_0">
					<view @click="closeBatch()" class="get-close">x</view>
					<block v-if="isType == 1">
						<view class="p-title" style="font-size: 40rpx;font-weight: bold;">
							{{param.isSel == 3 ? '极速视频合成协议' : '高级视频合成协议'}}
						</view>
						<view class="padding_0_30rpx">
							<scroll-view :scroll-y="true" style="height: 700rpx;">
								<rich-parser
									:html="param.isSel == 3 ? cloneSet.composite_notice : cloneSet.video_notice "
									domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load ref="article"
									selectable show-with-animation use-anchor>

								</rich-parser>
							</scroll-view>
							<view class="c-but" style="width: 610rpx;margin: 20rpx 40rpx;" @click="getCheck()">
								我已阅读并同意视频合成协议</view>
						</view>
					</block>
					<block v-if="isType == 3">
						<view class="p-title p-bo">{{voiceType == 2 ? '高保真音色' : '专业版音色'}}</view>
						<scroll-view :scroll-y="true" style="height: 540rpx;">
							<block v-if="voicesList.length > 0">
								<view class="display-fw-a padding_20rpx">
									<block v-for="(item,index) in voicesList" :key="index">
										<view class="width_176rpx-center margin-bottom_30rpx"
											@click="selVoiceTabs(item)">
											<image class="img-238" :class="item.id == voicesObj.id ? 'img-238-1' : ''"
												:src="imgUrl+'238.png'"></image>
											<view class="font-overflow"
												:class="item.id == voicesObj.id ? 'color_FFFFFF' : 'color_A1A1A1'">
												{{item.name}}
											</view>
										</view>
									</block>
								</view>
							</block>
							<block v-else>
								<view class="display-ac-jc">
									<image class="nodata" :src="imgUrl+'tabbar/nodata4.png'"></image>
									<view class="nodata-tips">您还没有高保真声音，快去复刻一个吧~</view>
									<view class="nodata-but" @click="getSenior()">立即复刻声音</view>
								</view>
							</block>
						</scroll-view>
						<view v-if="voicesList.length > 0" class="p-but" @click="closeBatch()">确定</view>
					</block>
					<block v-if="isType == 6">
						<view class="p-title p-bo">请选择AI数字人</view>
						<view class="display-a line-top" v-if="setLineList.length > 1">
							<block v-for="(item,index) in setLineList" :key="index">
								<view class="display-ac-jc width_186rpx-center" @click="getIsSel(item.id)">
									<view :class="param.isSel == item.id ? 'line-top-name-sel' : 'color_A0A0A1'">
										{{item.name}}
									</view>
									<view class="line-top-bot"
										:class="param.isSel == item.id ? 'line-top-bot-sel' : ''"></view>
								</view>
							</block>
						</view>
						<scroll-view :scroll-y="true" style="height: 540rpx;">
							<block v-if="avatarList.length > 0">
								<view class="display-fw-a">
									<block v-for="(item,index) in avatarList" :key="index">
										<view class="c-img" @click="confirmImage(item)" :id="'id'+item.id">
											<image class="img-213"
												:src="item.id == param.id ? imgUrl+'213.png' : imgUrl+'212.png'">
											</image>
											<image class="decode-img" mode="widthFix" :src="item.video_cover"></image>
										</view>
									</block>
								</view>
							</block>
							<block v-else>
								<view>
									<image @click="getClone()" class="img-211" :src="imgUrl+'211.png'"></image>
								</view>
							</block>
						</scroll-view>
						<view v-if="avatarList.length > 0" class="p-but" @click="closeBatch()">确定</view>
					</block>
					<block v-if="isType == 4">
						<view class="p-title p-bo">调整语速</view>
						<view class="padding_20rpx text-align_center">
							<view class="color_A1A1A1 font-size_26rpx" style="padding: 60rpx 0 14rpx;">当前语速</view>
							<view class="sp-ra">{{speechRate}}</view>
							<slider class="custom-slider" :value="speechRate" @change="sliderChange"
								activeColor="#92F595" backgroundColor="#8D8D8D" block-color="#92F595" block-size="12" />
						</view>
						<view class="p-but" @click="closeBatch()">确定</view>
					</block>
					<block v-if="isType == 5">
						<view class="p-title p-bo">调整音量</view>
						<view class="padding_20rpx text-align_center">
							<view class="color_A1A1A1 font-size_26rpx" style="padding: 60rpx 0 14rpx;">当前音量</view>
							<view class="sp-ra">{{pitchRate}}</view>
							<slider class="custom-slider" :value="pitchRate" @change="sliderChange2"
								activeColor="#92F595" backgroundColor="#8D8D8D" block-color="#92F595" block-size="12" />
						</view>
						<view class="p-but" @click="closeBatch()">确定</view>
					</block>
				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop4">
			<template v-slot:content>
				<view class="text-align_center color_FFFFFF padding_30rpx_0">使用说明</view>
				<scroll-view :scroll-y="true" style="height: 840rpx;">
					<view class="padding_0_10rpx color_FFFFFF">
						<view class="font-size_30rpx color_FF0000 font-weight_bold margin-bottom_10rpx">插入停顿</view>
						<view class="padding_10rpx">
							<view class="margin-bottom_10rpx">1.具体停顿时长,秒的绝对值,上限10s,可以精确到小数点后1位</view>
							<view class="margin-bottom_10rpx">2.标签中间不能添加文本</view>
							<view class="margin-bottom_10rpx">3.当{{td1}}时,默认按照句号节奏停顿,如不需停顿,请删去该标签</view>
							<view class="margin-bottom_10rpx">4.示例: {{td2}}</view>
						</view>
						<view class="font-size_30rpx color_FF0000 font-weight_bold margin-bottom_10rpx">中文多音字</view>
						<view class="padding_10rpx">
							<view class="margin-bottom_10rpx">1.为标签内的文本指定发音,只能加在中文上</view>
							<view class="margin-bottom_10rpx">2.多个拼音用空格分割</view>
							<view class="margin-bottom_10rpx">3.1~4 分别顺序对应汉语四个声调,5对应轻声。比如,词语“爸爸”,拼音应写为“ba4 ba5”
								标签指定的拼音个数,必须与标签内中文字数相等</view>
							<view class="margin-bottom_10rpx">4.标签内不能有标点符号、空格等非汉字内容</view>
							<view class="margin-bottom_10rpx">5.拼音的ü需要写成v</view>
							<view class="margin-bottom_10rpx">6.如果词语连读,导致发音控制不明显,增加停顿 break
								标签,增大字间停顿。但多音字phoneme标签内不能嵌套停顿break标签,需要每个字单独使用多音字phoneme标签</view>
							<view class="margin-bottom_10rpx">7.示例: {{dyz}}</view>
						</view>
						<view class="h_20rpx"></view>
					</view>
				</scroll-view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop9">
			<template v-slot:content>

				<view class="pop-bg" style="padding: 34rpx 20rpx;">
					<image @click="closeAICreation()" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title2">AI文案类型</view>
					<view class="display-fw-js">
						<view class="a-frame display-a" @click="getAdd(18)">
							<image class="img-25" :src="imgUrl + '324.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">DeepSeek
								</view>
								<view class="color_D5D5D5 font-size_24rpx">DeepSeek文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(1)">
							<image class="img-25" :src="imgUrl + '309.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">企业宣传
								</view>
								<view class="color_D5D5D5 font-size_24rpx">企业宣传</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(2)">
							<image class="img-25" :src="imgUrl + '310.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">同城团购
								</view>
								<view class="color_D5D5D5 font-size_24rpx">团购产品文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(3)">
							<image class="img-25" :src="imgUrl + '311.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">电商带货
								</view>
								<view class="color_D5D5D5 font-size_24rpx">带货营销文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(4)">
							<image class="img-25" :src="imgUrl + '312.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">知识科普
								</view>
								<view class="color_D5D5D5 font-size_24rpx">知识获取很方便</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(5)">
							<image class="img-25" :src="imgUrl + '313.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">情感专家
								</view>
								<view class="color_D5D5D5 font-size_24rpx">情感问题一点通</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(6)">
							<image class="img-25" :src="imgUrl + '314.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">口播文案
								</view>
								<view class="color_D5D5D5 font-size_24rpx">播音必备神器</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(7)">
							<image class="img-25" :src="imgUrl + '315.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">朋友圈营销
								</view>
								<view class="color_D5D5D5 font-size_24rpx">轻松搞定营销文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(8)">
							<image class="img-25" :src="imgUrl + '316.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">小红书笔记
								</view>
								<view class="color_D5D5D5 font-size_24rpx">轻松生成笔记</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(9)">
							<image class="img-25" :src="imgUrl + '318.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">智能代写
								</view>
								<view class="color_D5D5D5 font-size_24rpx">一键智能代写</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(10)">
							<image class="img-25" :src="imgUrl + '317.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">文案仿写
								</view>
								<view class="color_D5D5D5 font-size_24rpx">模仿生成营销文案</view>
							</view>
						</view>
						<view class="a-frame display-a" @click="getAdd(16)">
							<image class="img-25" :src="imgUrl + '325.png'"></image>
							<view>
								<view class="color_FFFFFF font-size_30rpx font-weight_bold margin-bottom_10rpx">AI提取文案
								</view>
								<view class="color_D5D5D5 font-size_24rpx">一键提取文案</view>
							</view>
						</view>
					</view>
				</view>

			</template>
		</sunui-popup>

	</view>
</template>

<script>
	import {
		decodedString
	} from '../../../subPackages/subPackageA/utils/decodedString';
	export default {
		data() {
			return {

				imgUrl: this.$imgUrl,

				heightSystemss: '',
				statusBarHeightss: '',

				driveType: '1', //1文本驱动 2语音驱动
				voiceType: '1', //1入门 3专业 2高保真
				percentTime: '',
				percent: 0, //合成进度
				audioUrl: '', //音频路径
				audioName: '', //音频名称
				isPlay: '2', //1播放 2暂停
				//音频信息
				soundObj: {
					name: '',
					url: '', //
					id: '', //
					type: '', //1 历史音频返回 2生成音频返回
				},

				voicesList: [], //高保真列表
				voicesObj: {
					name: '',
					media_url: '', //
					id: '', //
					sex: '', //1男 2女
					language: '', //0中文1英文2日3韩4俄
					age_group: '', //'1 儿童 2 青年3 中年4，中老年',
				},

				isPop: '2', //1打开 2关闭

				voideTitle: '', //视频标题
				msgText: '', //数字人文本

				isType: '', //1使用说明  3性别音色 4语速 5音量

				avatarList: [], //我的形象
				listObj: {
					id: '',
				},

				innerAudioContext2: null,

				td1: "<break time='0s'></break>",
				td2: "今天天气<break time='2.5s'></break>很好",
				dyz: "<phoneme alphabet='py' ph='xi1 xi1'>茜茜</phoneme>公主是拍摄的历史题材",

				sayAsList: [{
						id: 1,
						name: 'score 冒号按照「比例/比分」播报',
						text: '<say-as interpret-as="score">12:15</say-as>'
					},
					{
						id: 2,
						name: 'time 冒号按照「时间」播报',
						text: '<say-as interpret-as="time">12:15</say-as>'
					},
					{
						id: 3,
						name: 'digits 数字按照「单个数字」播报',
						text: '<say-as interpret-as="digits">123 456 789</say-as>'
					},
					{
						id: 4,
						name: 'number 数字按照「整体数字」播报',
						text: '<say-as interpret-as="number">467,995</say-as>'
					},
					{
						id: 5,
						name: 'telephone 数字按照「电话」播报',
						text: '<say-as interpret-as="telephone">123 4567 4567</say-as>'
					},
					{
						id: 6,
						name: 'address 文本按照「地址」播报',
						text: '<say-as interpret-as="address">某某小区12号楼1单元6601号房</say-as>'
					},
					{
						id: 7,
						name: 'characters 文本按照「单个符号」播报',
						text: '<say-as interpret-as="characters">.</say-as>'
					},
					{
						id: 8,
						name: 'poetry 按照「古诗风格」播报',
						text: '<say-as interpret-as="poetry">窗前明月光,疑是地上霜。</say-as>'
					}
				],

				uploadUrl: this.$api.upload,

				param: {
					base_video: '', //形象视频
					decode_img: '', //形象图片
					id: '', //形象ID
					isSel: '', // 1 2 3 4号线路
					current_status: '',
					new_current_status: '',
					composite_current_status: '',
					four_current_status: '',

				},

				web_people_width: '', //标准形象 宽
				web_people_height: '', //标准形象 高

				driveType: '1', //1文本驱动 2语音驱动
				voiceType: '3', //1入门 3专业 2高保真

				speechRate: '50', //语速 0-100
				pitchRate: '50', // 音量 0-100

				answerType: '', // 1文案创作

				templateObj: {}, //消息模板

				cloneSet: uni.getStorageSync("cloneSet"), //克隆开关设置
				isMember: uni.getStorageSync("isMember"), //1开启会员
				setLineList: uni.getStorageSync('indexWay'), //已开启的线路
				system: uni.getStorageSync('system'),
				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				isWhether: true, //判断重复点击
				requestTask: null, //流式请求任务

			}
		},

		onLoad(options) {
			this.getTemplate();
			if (options.title) {
				this.voideTitle = options.title
			}
			if (options.content) {
				this.msgText = options.content
			}
			this.getSystemInfo();
			if (options.type) {
				this.param.isSel = options.type;
			}
			if (options.answerType == 1) {
				this.answerType = options.answerType;
				this.msgText = uni.getStorageSync('answer');
			}
			if (options.param) {
				this.param = JSON.parse(decodeURIComponent(options.param));

				if (this.param.isSel == 3 || this.param.isSel == 2) {
					this.getImage(this.param.decode_img);
				}

			}

			if (!this.param.isSel) {
				this.param.isSel = this.setLineList[0].id;
			}
		},

		onShow() {
			if (uni.getStorageSync('uid')) {
				this.uid = uni.getStorageSync('uid');
				this.getAvatarList();
				this.getVoice2();
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
							// uni.navigateTo({
							// 	url: '/pages/auth/auth?type=1'
							// })
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},

		onUnload() {
			this.stopStream()
			if (this.innerAudioContext2 && (!this.innerAudioContext2.paused)) {
				this.innerAudioContext2.stop();
			}

			this.stopProgress();


		},


		methods: {

			//模板设置
			async getTemplate() {
				const result = await this.$http.post({
					url: this.$api.template,
				});
				if (result.errno == 0) {
					this.templateObj = result.data;
				}
			},

			//确认形象
			confirmImage(obj) {
				this.listObj = obj;

				this.param = {
					decode_img: this.listObj.video_cover,
					id: this.listObj.id,
					isSel: this.param.isSel,
					current_status: this.listObj.current_status,
					new_current_status: this.listObj.new_current_status,
					composite_current_status: this.listObj.composite_current_status,
					four_current_status: this.listObj.four_current_status
				};

				if (this.param.isSel == 3 || this.param.isSel == 2) {
					this.getImage(this.param.decode_img);
				}

				this.isPop = 2;

			},

			getIsSel(type) {
				this.listObj = {
					id: '',
				};
				this.web_people_width = '';
				this.web_people_height = '';
				this.param = {
					decode_img: '',
					id: '',
					isSel: type,
				};
				this.getAvatarList();
			},

			//生成视频
			getCheck() {

				uni.getSetting({
					withSubscriptions: true,
					success: (res) => {
						console.log(res.subscriptionsSetting);
						if (res.subscriptionsSetting.mainSwitch == false) {
							this.getCheckSave();
						} else {
							// 获取下发权限
							uni.requestSubscribeMessage({
								tmplIds: [this.templateObj.generate_success_template, this.templateObj
									.generate_fail_template
								], //此处写在后台获取的模板ID，可以写多个模板ID，看自己的需求
								success: (data) => {
									if (data[this.templateObj.generate_success_template] ==
										'accept') { //accept--用户同意 reject--用户拒绝 ban--微信后台封禁,可不管
										this.getCheckSave();
									} else {
										uni.showModal({
											title: '温馨提示',
											content: '您已拒绝授权，将无法在微信中收到通知！',
											showCancel: false,
											success: res => {
												if (res.confirm) {
													// 这里可以写自己的逻辑
													this.getCheckSave();
													console.log('拒绝授权', data);
												}
											}
										})
									}
								},
								fail: (err) => {
									this.getCheckSave();
									console.log('失败', err);
								},
								complete: (result) => {

									console.log('完成', result);
								}
							});
						}
					}
				});

			},

			//
			//生成视频
			getCheckSave() {

				this.closeBatch();

				let getUrl = '';

				//线路一
				if (this.param.isSel == 1) {
					getUrl = this.$api.videoGenerate;
				}

				// //线路二
				// if (this.param.isSel == 2) {
				// 	getUrl = this.$api.generateTwo;
				// }

				//线路四
				if (this.param.isSel == 4) {
					getUrl = this.$api.generateFour;
				}


				if (this.driveType == 1) {

					//高保真/专业版
					if (this.voiceType == 2 || this.voiceType == 3) {

						if (this.soundObj.url) {
							if (this.param.isSel == 3 || this.param.isSel == 2) {
								this.getSpeedSoundAdd(this.soundObj.url);
							} else {
								this.getGenerate(this.soundObj.url, getUrl);
							}
						} else {
							this.getVideoSendTts(1, getUrl);
						}
					}

					//入门版
					if (this.voiceType == 1) {

						if (this.param.isSel == 3 || this.param.isSel == 2) {
							this.getSpeedSoundAdd(this.soundObj.url);
						} else {
							this.getGenerate('', getUrl);
						}

					}
				}


				//语言驱动
				if (this.driveType == 2) {

					if (!this.audioUrl) {
						this.$sun.toast("请点击录音或上传音频文件", 'none');
						return;
					}

					if (!this.isWhether) {
						return;
					}
					this.isWhether = false;

					if (this.param.isSel == 3 || this.param.isSel == 2) {
						this.getSpeedSoundAdd(this.audioUrl);
					} else {
						this.getGenerate(this.audioUrl, getUrl);
					}
				}

			},

			//极速视频添加声音 线路三/线路二
			async getSpeedSoundAdd(aUrl) {

				const result = await this.$http.post({
					url: this.$api.speedSoundAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: this.voideTitle, //视频标题	必传
						url: aUrl, // 音频链接 从/video/sendTts接口返回	必传
					}
				});
				if (result.errno == 0) {
					this.getSpeedGenerate(result.data);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//极速视频生成 线路三/线路二
			async getSpeedGenerate(id) {

				const result = await this.$http.post({
					url: this.param.isSel == 3 ? this.$api.speedGenerate : this.$api.generateTwo,
					data: {
						uid: uni.getStorageSync('uid'),
						name: this.voideTitle, //视频标题	必传
						image_id: this.param.id, //形象id		必传
						sound_id: id,
						width: this.web_people_width,
						height: this.web_people_height,
						combination: '',
						caption_josn: '',
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages/assets/digital-assets?tabsId=2&type=1&tabsNextId=' + this
								.param.isSel
						})
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
			},

			//生成高级视频 线路一/线路四
			async getGenerate(aUrl, getUrl) {

				const result = await this.$http.post({
					url: getUrl,
					data: {
						uid: uni.getStorageSync('uid'),
						name: this.voideTitle, //视频标题	必传
						avatar_id: this.param.id, //形象id		必传
						sound_id: (this.driveType == 1 && this.voiceType == 1) ? this.soundObj.id : '',
						audio_src: aUrl, // 音频链接 从/video/sendTts接口返回	必传
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages/assets/digital-assets?tabsId=2&type=1&tabsNextId=' + this
								.param.isSel
						})
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
			},


			//预览试听
			async getVideoSendTts(type, getUrl2) {

				if (this.msgText.length > 500) {
					this.$sun.toast("请把文案限制在500字以内", 'none');
					return;
				}

				if (type == 2 && this.soundObj.url && this.isPlay == 2) {
					this.playAudio(this.soundObj.url);
					return;
				}

				if (this.isPlay == 1 && type == 2) {
					if (this.innerAudioContext2 && (!this.innerAudioContext2.paused)) {
						this.innerAudioContext2.stop();
					}
					uni.hideLoading();
					this.isPlay = 2;
					this.isWhether = true;
					return;
				}

				if (!this.isWhether) {
					console.log("-----进来了11");
					return;
				}
				this.isWhether = false;

				let getUrl = '';
				let getDate = '';

				if (this.voiceType == 2) { // voiceType 1 入门 2高保真 3专业
					getUrl = this.$api.ttsSsml;
					getDate = {
						uid: uni.getStorageSync('uid'),
						voice_id: this.voicesObj.id,
						text: this.msgText ? '<speak>' + this.msgText + '</speak>' :
							'<speak>这是你的高保真声音克隆效果，你觉得效果怎么样?</speak>'
					}
				}

				if (this.voiceType == 3) { // voiceType 1 入门 2高保真 3专业
					getUrl = this.$api.voiceClone;
					getDate = {
						uid: uni.getStorageSync('uid'),
						voice_id: this.voicesObj.id,
						msg: this.msgText,
						speechRate: this.speechRate,
						pitchRate: this.pitchRate
					}
				}

				if (!this.msgText) {
					const result = await this.$http.post({
						url: getUrl,
						data: getDate
					});
					if (result.errno == 0) {

						if (result.data.url) {

							if (type == 1) {

								if (this.param.isSel == 3 || this.param.isSel == 2) {
									this.getSpeedSoundAdd(result.data.url);
								} else {
									this.getGenerate(result.data.url, getUrl2);
								}

							}

							if (type == 2) {
								this.playAudio(result.data.url);
							}

						} else {
							this.$sun.toast("音频生成失败,请联系平台处理!", 'none');
							this.isWhether = true;
						}

					} else {
						this.$sun.toast(result.message, 'none');
						this.isWhether = true;
					}

				} else {
					const result = await this.$http.post({
						url: getUrl,
						data: getDate
					});
					if (result.errno == 0) {

						if (result.data.url) {

							if (type == 1) {
								if (this.param.isSel == 3 || this.param.isSel == 2) {
									this.getSpeedSoundAdd(result.data.url);
								} else {
									this.getGenerate(result.data.url, getUrl2);
								}

							}

							if (type == 2) {
								this.playAudio(result.data.url);
							}

						} else {
							this.$sun.toast("音频生成失败,请联系平台处理!", 'none');
							this.isWhether = true;
						}

					} else {
						this.$sun.toast(result.message, 'none');
						this.isWhether = true;
					}

				}
			},

			//选中音色
			selVoiceTabs(obj) {
				this.voicesObj = {
					name: obj.name,
					id: obj.id,
					media_url: obj.media_url,
					sex: this.voiceType == 3 ? obj.sex : '',
					language: this.voiceType == 3 ? obj.language : '',
					age_group: this.voiceType == 3 ? obj.age_group : ''
				};
			},

			//高保真声音克隆
			getSenior() {

				if (this.driveType == 1) {
					if (this.voiceType == 3) {
						uni.navigateTo({
							url: '/pages/index/voice/highFidelity'
						})
					}
					if (this.voiceType == 2) {
						uni.navigateTo({
							url: '/pages/index/voice/senior'
						})
					}
				}
			},

			//前往克隆页
			getClone() {
				let compressed = 1;
				if (this.param.isSel == 4) {
					compressed = 2;
				}
				uni.navigateTo({
					url: '/pages/index/clone/clone?compressed=' + compressed
				})
			},

			//
			getBatch(type) {
				this.isPop = 1;
				this.isType = type;

				if (type == 1) {

					if (!this.isWhether) {
						this.$sun.toast("正在生成中，请稍后再试", 'none');
						return;
					}

					if (!this.param.id) {
						this.$sun.toast("请选择形象", 'none');
						this.isPop = 2;
						return;
					}

					if (!this.voideTitle) {
						this.$sun.toast("请输入视频标题", 'none');
						this.isPop = 2;
						return;
					}

					if (this.driveType == 2 && (!this.audioUrl)) {
						this.$sun.toast("请点击录音或上传音频文件", 'none');
						this.isPop = 2;
						return;
					}

					if (this.driveType == 1) {
						if (this.voiceType > 1) {

							if ((!this.msgText) && (!this.soundObj.url)) {
								this.$sun.toast("请先输入文案", 'none');
								this.isPop = 2;
								return;
							}

							if (this.msgText.length > 500) {
								this.$sun.toast("请把文案限制在500字以内", 'none');
								return;
							}
						}

						if (this.voiceType == 1 && (!this.soundObj.url)) {
							this.$sun.toast("音频合成中,请耐心等待!", 'none');
							this.isPop = 2;
							return;
						}
					}

				}

				this.$refs.pop3.show({
					style: 'background-color:#101010;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeBatch() {
				this.isPop = 2;
				this.$refs.pop3.close();
			},

			//高保真SSML说明
			getOpenSSML() {
				this.$refs.pop4.show({
					style: 'width:710rpx;background-color: #171717;border-radius: 5px;',
					anim: 'center',
					shadeClose: false, //使用户不能点击其它关闭页面
					bottomClose: true
				});
			},

			//插入停顿
			insertPause() {
				this.msgText = this.msgText + '<break time="1.5s"></break>';
			},

			//多音字
			characters() {
				this.msgText = this.msgText + '<phoneme alphabet="py" ph="xi1 xi1">茜茜</phoneme>';
			},

			//数字读法
			getChange(e) {
				// console.log("---->",e.detail.value);
				this.msgText = this.msgText + this.sayAsList[e.detail.value].text;
			},

			//AI文案
			getAdd(type) {
				if (type == 16) {
					uni.navigateTo({
						url: '/pages/index/AICreation/extract?typeIndex=3'
					})
				} else {
					uni.navigateTo({
						url: '/pages/index/AICreation/release?type=' + type + '&typeIndex=3'
					})
				}
				this.closeAICreation();
			},

			// 中止请求
			stopStream() {
				if (this.requestTask) {
					this.requestTask.cancel();
					this.requestTask = null;
					console.log('已中止请求');
				}
			},
			// 一键仿写流式请求
			startStream() {
				if (!this.msgText) {
					this.$sun.toast("请先输入文案", 'error');
					return;
				}
				if (!this.isWhether) {
					this.$sun.toast("正在生成中，请稍后再试", 'none');
					return;
				}
				this.isWhether = false

				uni.showLoading({
					title: '正在生成'
				})
				let url = this.$api.rewriteText

				let data = {
					uid: uni.getStorageSync('uid'), // 请求参数
					content: this.msgText
				}
				this.msgText = ''
				// 使用流式请求
				this.requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						uni.hideLoading()
						// 处理接收到的数据块
						try {

							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 确保换行符被保留（不进行额外处理，由computed属性处理格式化）
								this.msgText += text;
							} else if (typeof data === 'string') {
								// 如果没有结束标记，直接追加
								this.msgText += data;
							} else {
								console.warn("收到未知类型的数据块:", data);
							}
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						uni.hideLoading()
						uni.setStorageSync("answer", this.msgText);
						this.isWhether = true
						console.log('文案生成完成');
					},
					onError: (err) => {
						uni.hideLoading()
						console.error("流式请求错误:", err);
					}
				});
			},

			//查询点数是否足够
			async getAccountInfo() {

				if (!this.msgText) {
					this.$sun.toast("请先输入文案", 'error');
					return;
				}

				// uni.showModal({
				// 	content: this.tallySetObj.ai_create_deduct + '点/1次,一键仿写会覆盖当前文案,是否确认?',
				// 	cancelText: "取消",
				// 	confirmText: "确认",
				// 	success: async (res) => {
				// 		if (res.confirm) {
				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.accountInfo,
					data: {
						type: 1,
						uid: uni.getStorageSync('uid'),
					}
				});
				if (result.errno == 0) {
					this.getCopyImitation();
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}
				// 		} else if (res.cancel) {

				// 		}
				// 	}
				// })

			},

			//一键仿写
			async getCopyImitation() {
				const result = await this.$http.post({
					url: 'https://vapi.weijuyunke.com/api/doBao/copyImitation',
					data: {
						content: this.msgText,
						countType: 3,
						ipStatus: 2,
						domainName: 'vapi.weijuyunke.com',
						requestIp: '**************'
					},
					mask: true,
					title: '请求中...'
				});
				if (result.code == 200) {
					// this.$sun.toast("操作成功");
					// setTimeout(() => {
					// 	this.msgText = result.data;
					// 	this.isWhether = true;
					// }, 1000);
					this.getAICreation(result.data);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//生成AI文案
			async getAICreation(answer) {

				const result = await this.$http.post({
					url: this.$api.aiCreateAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: "一键仿写",
						type: 17,
						question: this.msgText, //拼接的文本
						answer: answer, // 接口返回的文本
						words: answer.length
					}
				});
				if (result.errno == 0) {
					this.$sun.toast("操作成功");
					setTimeout(() => {
						this.msgText = answer;
						this.isWhether = true;
					}, 1000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//AI文案
			openAICreation() {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中，请稍后再试", 'none');
					return;
				}
				this.$refs.pop9.show({
					style: 'background-color:#222127;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeAICreation() {
				this.$refs.pop9.close();
			},

			//清空文本
			getClear() {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中，请稍后再试", 'none');
					return;
				}
				this.msgText = '';
			},

			//历史音频
			async getVideoSoundList() {
				const result = await this.$http.post({
					url: this.$api.videoSoundList,
					data: {
						uid: uni.getStorageSync('uid'),
						page: 1,
						psize: 10
					}
				});
				if (result.errno == 0) {

					this.soundObj = {
						name: result.data.list[0].name,
						url: result.data.list[0].url, //
						id: result.data.list[0].id, //
					};

					this.$forceUpdate();
				}
			},

			//高保真克隆记录
			async getVoice2() {
				const result = await this.$http.post({
					url: this.$api.voiceTrainList,
					data: {
						uid: uni.getStorageSync('uid'),
						current_status: 'completed',
						train_mode: this.voiceType, //1-入门 2-高保真 3专业
						buy_expire: 2, //资产市场购买 2未过期
						page: 1,
						psize: 200
					}
				});
				if (result.errno == 0) {

					this.voicesList = result.data.list;

					if (this.voicesList.length > 0) {
						this.voicesObj = {
							name: this.voicesList[0].name,
							id: this.voicesList[0].id,
							media_url: this.voicesList[0].media_url,
							sex: this.voiceType == 3 ? this.voicesList[0].sex : '',
							language: this.voiceType == 3 ? this.voicesList[0].language : '',
							age_group: this.voiceType == 3 ? this.voicesList[0].age_group : ''
						};
					}

				}
			},

			//我的形象
			async getAvatarList() {

				const result = await this.$http.post({
					url: this.$api.avatarList,
					data: {
						uid: uni.getStorageSync('uid'),
						buy_expire: 2, //资产市场购买 2未过期
						current_status: this.param.isSel == 1 ? 'completed' : '',
						new_current_status: this.param.isSel == 2 ? 'completed' : '',
						composite_current_status: this.param.isSel == 3 ? 'completed' : '',
						four_current_status: this.param.isSel == 4 ? 'completed' : '',
						definition: this.param.isSel == 4 ? 2 : 1,
						page: 1,
						psize: 200
					}
				});
				if (result.errno == 0) {
					this.avatarList = result.data.list;
				}
			},

			startProgress() {
				this.percentTime = setInterval(() => {
					if (this.percent < 100) {
						if (this.percent == 90) {
							this.getSoundRefresh();
						} else {
							this.percent = Math.min(this.percent + 10, 100);
						}
					} else {
						this.stopProgress();
					}
				}, 4000); // 每秒执行一次
			},
			stopProgress() {
				if (this.percentTime) {
					clearInterval(this.percentTime);
					this.percentTime = null;
				}
			},

			//获取图片信息
			getImage(url) {
				uni.getImageInfo({
					src: url,
					success: (image) => {

						uni.showLoading({
							mask: true,
							title: '加载中...'
						})

						this.web_people_width = image.width;
						this.web_people_height = image.height;

						console.log("图片信息---->", this.web_people_width, this.web_people_height);

						uni.hideLoading();

					},
					fail: err => {
						uni.hideLoading();
						this.$sun.toast(err, 'none');
						console.log(err);
					}
				});
			},

			//上一页文案仿写的
			otherFun2(text) {
				this.msgText = text;
			},

			//上一页音频信息
			otherFun(obj) {
				console.log("音频信息-----", obj);

				this.percent = 0;

				if (obj.type == 1) {
					this.soundObj = obj;
				}
				if (obj.type == 2) {
					this.getVideoSoundList();
					this.startProgress();
				}
			},

			//删除音频
			delAudio2() {
				uni.showModal({
					title: '提示',
					content: '确认删除 ' + this.soundObj.name + ' 音频吗?',
					success: async (res) => {
						if (res.confirm) {
							this.soundObj = {
								name: '',
								url: '', //
								id: '', //
								type: '', //1 历史音频返回 2生成音频返回
							}
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				});
			},

			//选择音频
			getAudio(type) {
				if (type == 1) {
					uni.navigateTo({
						url: '/pages/index/clip/audio?answerType=' + this.answerType + '&content=' + this.msgText
					})
				}
				if (type == 2) {
					uni.navigateTo({
						url: '/pages/index/clip/audio?tabsId=2&id=' + this.soundObj.id + '&voiceType=' + this
							.voiceType + '&answerType=' + this.answerType
					})
				}
			},

			//音频刷新
			async getSoundRefresh() {

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.soundRefresh,
					data: {
						uid: uni.getStorageSync('uid'),
						sound_id: this.soundObj.id,
					}
				});
				if (result.errno == 0) {

					this.isWhether = true;
					if (result.data) {
						// this.$sun.toast("音频合成成功");
						this.getVideoSoundList();
						this.percent = 100;
					}
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},

			//删除音频
			delAudio() {
				uni.showModal({
					title: '提示',
					content: '确认删除 ' + this.audioName + ' 吗?',
					success: async (res) => {
						if (res.confirm) {
							this.audioUrl = ''; //音频路径
							this.audioName = ''; //音频名称
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				});
			},

			//播放音频
			playAudio(audioUrl) {
				uni.showLoading({
					title: '正在试听...',
					// mask: true
				})
				this.innerAudioContext2 = null;
				this.innerAudioContext2 = uni.createInnerAudioContext();

				this.innerAudioContext2.src = audioUrl;
				setTimeout(() => {
					if (this.isPlay == 2) {
						this.isPlay = 1;
						this.innerAudioContext2.play();
						this.innerAudioContext2.onPlay(() => {
							// console.log('开始播放');
						});
						this.innerAudioContext2.onEnded(() => {
							this.isPlay = 2;
							uni.hideLoading();
						});
						this.innerAudioContext2.onError((err) => {
							this.innerAudioContext2.destroy();
							this.innerAudioContext2 = null;
							uni.hideLoading();
							// console.log('播放音频出错：', err);
						});
					} else {
						this.isPlay = 2;
						uni.hideLoading();
						this.innerAudioContext2.pause();
						this.innerAudioContext2.onPause(() => {
							// console.log('暂停播放');
						});
					}
					this.isWhether = true;
				}, 500);

			},

			//录音上传
			getConfirm(e) {
				// console.log("录音文件==>", e);
				this.upload(e);
			},

			//音频上传
			chooseFile() {
				uni.chooseMessageFile({
					count: 1,
					type: 'file',
					extension: ['.mp3', '.m4a', '.wav', 'wav', 'mp3', 'm4a', '.MP3', '.M4A', '.WAV', 'WAV', 'MP3',
						'M4A'
					],
					success: (res) => {
						// console.log("从聊天记录中获取文件", res);
						const {
							errMsg,
							tempFiles
						} = res;
						if (errMsg == "chooseMessageFile:ok" && tempFiles.length) {
							const {
								name,
								size,
								path
							} = tempFiles[0];
							// console.log("临时文件", {
							// 	size,
							// 	path
							// });
							if ((name.slice(-4) != ".mp3") && (name.slice(-4) != ".MP3") && (name.slice(-4) !=
									".m4a") && (name.slice(-4) != ".M4A") && (name.slice(-4) !=
									".wav") && (name.slice(-4) != ".WAV")) {
								return uni.showToast({
									icon: "none",
									title: "请上传mp3,m4a,wav格式音频文件！",
									duration: 2000,
								});
							}
							if (size / 1024 / 1024 > 10) {
								return uni.showToast({
									icon: "none",
									title: "音频文件过大，请重新选择！",
								});
							}
							// console.log("从聊天记录中获取文件", name,path);
							// formData.value.audioName = name
							this.upload(path);
						}
					},
				});
			},
			upload(path) {
				uni.showLoading()
				// 调用uni.uploadFile将文件上传至服务器
				uni.uploadFile({
					url: this.$api.upload, // 设置上传接口地址
					filePath: path, // 需要上传的文件路径
					name: 'file', // 后台接收文件时对应的字段名称
					fileType: 'audio', // 指定文件类型
					header: {
						// "Content-Type": "multipart/form-data",
					},
					success: (response) => {
						// TODO: 处理上传成功后的操作
						const data = JSON.parse(response.data)
						// console.log('文件上传成功',data);
						// formData.value.accompanyInfo = data.data.url
						if (data.errno != 0) {
							uni.showToast({
								title: '文件上传失败',
								icon: 'none'
							});
						} else {
							this.audioUrl = data.data;
							let index = this.audioUrl.lastIndexOf('/'); // 获取最后一个/的位置
							let lastSegment = this.audioUrl.substring(index + 1); // 截取最后一个/后的值
							this.audioName = lastSegment;
							this.$sun.toast("文件上传成功");
							// console.log('上传成功', this.audioUrl, this.audioName);
						}
						uni.hideLoading()
						return
					},
					fail(error) {
						uni.hideLoading()
					}
				});
			},

			//音色类型
			getVoiceType(voiceType) {
				this.voiceType = voiceType;
				this.audioUrl = ''; //音频路径
				this.audioName = ''; //音频名称
				this.soundObj = {
					name: '',
					url: '', //
					id: '', //
					type: '', //1 历史音频返回 2生成音频返回
				};
				if (this.voiceType > 1) {
					this.voicesObj = {
						name: '',
						media_url: '', //
						id: '', //
						sex: '',
						language: '',
						age_group: '',
					};
					this.voicesList = [];
					this.getVoice2();
				}
			},

			//驱动类型
			getDriveType(driveType) {
				this.driveType = driveType;
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	.decode-img {
		width: 230rpx;
		height: 409rpx;
		border-radius: 10rpx;
		// position: absolute;
		// z-index: 2;
		// top: 50%;
		// left: 50%;
		// transform: translate(-50%, -50%);
	}

	.img-213 {
		position: absolute;
		z-index: 9;
		bottom: 10rpx;
		right: 20rpx;
		width: 50rpx;
		height: 50rpx;
		background-color: #FFF;
		border-radius: 50%;
	}

	.img-211 {
		width: 230rpx;
		height: 409rpx;
		margin-left: 15rpx;
	}

	.c-img {
		position: relative;
		width: 230rpx;
		height: 409rpx;
		border-radius: 10rpx;
		margin-left: 15rpx;
		margin-bottom: 15rpx;
		overflow: hidden;
		/* 超出容器部分隐藏 */
		display: inline-table;

	}

	.line-top-bot-sel {
		background: rgb(113, 223, 242);
	}

	.line-top-bot {
		width: 60rpx;
		height: 8rpx;
		border-radius: 10rpx;
		margin-top: 16rpx;
	}

	.line-top-name-sel {
		background: linear-gradient(180.00deg, rgb(156, 252, 124), rgb(113, 223, 242));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-size: 32rpx;
		font-weight: 600;
		letter-spacing: 0%;
	}

	.line-top {
		width: 750rpx;
		padding: 30rpx 0 22rpx;
	}

	.c-but2 {
		width: 410rpx;
		padding: 26rpx 0;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118), rgb(0, 236, 255) 97.869%);
		font-size: 32rpx;
		color: #000;
		font-weight: bold;
		text-align: center;
	}

	.c-but {
		width: 690rpx;
		padding: 26rpx 0;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118), rgb(0, 236, 255) 97.869%);
		margin: 0 10rpx;
		font-size: 32rpx;
		color: #000;
		font-weight: bold;
		text-align: center;
	}

	.img-196 {
		width: 60rpx;
		height: 60rpx;
	}

	.listening {
		width: 150rpx;
		text-align: center;
	}

	.pos-bott {
		position: fixed;
		bottom: 0;
		background-color: #111317;
		padding: 20rpx 20rpx 30rpx;
		z-index: 9;
	}

	.p-but {
		width: 710rpx;
		padding: 24rpx 0;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118), rgb(0, 236, 255) 97.869%);
		margin: 30rpx 20rpx 10rpx;
		font-size: 32rpx;
		color: #FFF;
		text-align: center;
	}

	.sp-ra {
		background: linear-gradient(180.00deg, rgb(146, 245, 149), rgb(117, 225, 232));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-size: 48rpx;
		font-weight: 700;
		letter-spacing: 0%;
		margin-bottom: 40rpx;
	}

	.custom-slider {
		width: 710rpx !important;
		height: 60rpx !important;
		margin: 0 !important;
	}

	.nodata-but {
		width: 260rpx;
		text-align: center;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(120, 164, 248), rgb(61, 52, 255) 100%);
		color: #FFF;
		font-size: 32rpx;
		padding: 24rpx 0;
		margin-top: 50rpx;
	}

	.nodata-tips {
		color: #969696;
		font-size: 26rpx;
	}

	.nodata {
		width: 300rpx;
		height: 300rpx;
		margin-bottom: 10rpx;
	}

	.img-238-1 {
		border: 1px solid #423AFE;
	}

	.img-238 {
		width: 80rpx;
		height: 80rpx;
		border-radius: 100rpx;
	}

	.p-title {
		font-size: 32rpx;
		padding-bottom: 30rpx;
		text-align: center;
	}

	.get-close {
		font-size: 32rpx;
		font-weight: 500;
		text-align: right;
		padding: 0 30rpx;
	}

	.placeholder {
		color: #848484;
	}

	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 42rpx;
		right: 30rpx;
	}

	.img-25 {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}

	.a-frame {
		width: 344rpx;
		padding: 30rpx;
		background-color: #323232;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}

	.p-title2 {
		font-weight: 600;
		letter-spacing: 4%;
		font-size: 40rpx;
		text-align: center;
		color: #FFF;
		margin-bottom: 30rpx;
	}

	.clear-clip {
		width: 90rpx;
		text-align: center;
		background-color: #2D2E33;
		border-radius: 10rpx;
		margin-left: auto;
		color: #EBEAEA;
		font-size: 24rpx;
		padding: 8rpx 0;
	}

	.img-409 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
	}

	textarea {
		width: 650rpx;
		margin-bottom: 20rpx;
		color: #FFF;
		height: 200rpx;
	}

	.img-353 {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 2rpx;
	}

	.msg-tips {
		// text-align: right;
		// padding-right: 20rpx;
		// padding-bottom: 20rpx;
		// margin-bottom: 20rpx;
		margin-left: auto;
		font-size: 24rpx;
		color: #C9CACA;
	}

	.frame-top {
		width: 690rpx;
		background-color: #111317;
		padding: 20rpx;
		margin: 0 10rpx;
	}

	.img-85 {
		width: 44rpx;
		height: 44rpx;
		margin-right: 4rpx;
	}

	.img-83 {
		width: 44rpx;
		height: 44rpx;
		margin-right: 20rpx;
	}

	.c-info-3 {
		padding: 30rpx 0;
		// border: 1px dashed rgb(67, 67, 67);
		// margin: 20rpx;
		// border-radius: 10rpx;
	}

	.c-info-2 {
		padding: 80rpx 0;
		border: 1px dashed #91F49B;
		margin: 20rpx;
		border-radius: 10rpx;
	}

	.c-info {
		padding: 24rpx 20rpx;
		// border-bottom: 1px solid rgb(64, 64, 64);
		color: #FFF;
	}

	.c-frame-txt {
		width: 710rpx;
		background-color: #1C1D20;
		border-radius: 10rpx;
		margin: 0 20rpx 30rpx;
	}

	.img-239 {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 4rpx;
	}

	.voice-listening {
		background-color: #262323;
		width: 150rpx;
		border-radius: 100rpx;
		padding: 22rpx 0;
		margin-left: auto;
		color: #FFF;
	}

	.img-247 {
		width: 30rpx;
		height: 30rpx;
		margin-right: 10rpx;
	}

	/*圆形加载*/
	.circle-loading {
		width: 44rpx;
		height: 44rpx;
		position: relative;
		margin: auto;

		.dot {
			position: absolute;
			top: 0;
			left: 0;
			width: 44rpx;
			height: 44rpx;
			animation: 1.5s loadrotate cubic-bezier(0.800, 0.005, 0.500, 1.000) infinite;

			&:after,
			.first-dot {
				content: '';
				position: absolute;
				width: 12rpx;
				height: 12rpx;
				background: #FFF;
				border-radius: 50%;
				left: 50%;
			}

			.first-dot {
				background: #fff;
				animation: 1.5s dotscale cubic-bezier(0.800, 0.005, 0.500, 1.000) infinite;

			}
		}
	}

	@for $i from 1 through 4 {
		.circle-loading {
			&>.dot:nth-child(#{$i}) {
				animation-delay: 0.15s*$i;
			}
		}
	}

	@keyframes loadrotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	@keyframes dotscale {

		0%,
		10% {
			width: 28rpx;
			height: 28rpx;
			margin-left: -2rpx;
			margin-top: -5rpx;
		}

		50% {
			width: 16rpx;
			height: 16rpx;
			margin-left: 0rpx;
			margin-top: 0rpx;
		}

		90%,
		100% {
			width: 28rpx;
			height: 28rpx;
			margin-left: -2rpx;
			margin-top: -5rpx;
		}
	}

	.img-95 {
		width: 30rpx;
		height: 30rpx;
	}

	.r-play {
		width: 70rpx;
		height: 70rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(76, 254, 124), rgb(2, 236, 252) 100%);
		margin-right: 20rpx;
	}

	.img-95 {
		width: 30rpx;
		height: 30rpx;
	}

	.sel-voice {
		width: 670rpx;
		padding: 30rpx;
		margin: 20rpx;
		border: 1px dashed #91F49B;
		border-radius: 10rpx;
	}

	.color_B7B7B7 {
		color: #B7B7B7;
		font-size: 24rpx;
	}

	.v-add {
		color: #40CAFE;
		font-size: 30rpx;
	}

	.v-upload {
		width: 710rpx;
		text-align: center;
		padding: 30rpx 0;
		background-color: #191919;
		border-radius: 0 0 10rpx 10rpx;
	}

	.v-name {
		width: 400rpx;
		color: #FFF;
		font-size: 30rpx;
	}

	.img-96 {
		width: 34rpx;
		height: 34rpx;
		margin-left: 20rpx;
	}

	.v-play {
		width: 110rpx;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		padding: 6rpx 0;
		color: #FFF;
		font-size: 24rpx;
		margin-left: auto;
	}

	.img-94 {
		width: 22rpx;
		height: 22rpx;
		margin-right: 4rpx;
	}

	.img-93 {
		width: 48rpx;
		height: 48rpx;
		margin-right: 16rpx;
	}

	.c-line-bott {
		border-radius: 100rpx;
		width: 60rpx;
		height: 8rpx;
		margin-left: 60rpx;
	}

	.c-line-bott-1 {
		background: rgb(113, 223, 242);
	}

	.c-line-bott-2 {
		background: #1C1D20;
	}

	.c-font-2 {
		color: #A0A0A1;
	}

	.c-font-1 {
		background: linear-gradient(180.00deg, rgb(156, 252, 124), rgb(113, 223, 242));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-weight: 600;
		letter-spacing: 0%;
	}

	.c-font {
		width: 180rpx;
		text-align: center;
		margin-bottom: 20rpx;
	}

	.recommend {
		position: absolute;
		width: 60rpx;
		text-align: center;
		color: #FFF;
		background-color: #FF0000;
		font-size: 22rpx;
		border-radius: 100rpx;
		padding: 4rpx 0;
		top: -10rpx;
		right: -6rpx;
	}

	.c-type-not-sel {
		width: 355rpx;
		text-align: center;
		padding: 40rpx 0 30rpx;
		color: #FFF;
		font-size: 32rpx;
	}

	.c-type-font {
		background: linear-gradient(180.00deg, rgb(77, 255, 121), rgb(5, 237, 247));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		letter-spacing: 0%;
		font-size: 32rpx;
		font-weight: 600;
	}

	.c-type-sel {
		width: 356rpx;
		text-align: center;
		padding: 40rpx 0 30rpx;
		border-radius: 10rpx 10rpx 10rpx 0px;
		background-color: #000;
	}

	.list-public {
		background-color: #1C1D20;
		padding: 0;
	}

	.img-407 {
		width: 70rpx;
		height: 70rpx;
		margin-bottom: 20rpx;
	}

	.c-frame {
		width: 230rpx;
		height: 409rpx;
		border: 1px dashed rgb(79, 255, 118);
		border-radius: 10rpx;
		margin-left: 100rpx;
	}

	.img-408 {
		width: 36rpx;
		height: 36rpx;
		margin-right: 8rpx;
	}

	.bg {
		width: 750rpx;
		height: 640rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 200rpx 30rpx 30rpx;
	}

	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		display: flex;
		align-items: center;
	}

	page {
		border: none;
		background-color: #111317;
		width: 100%;
		overflow-x: hidden !important;
	}
</style>