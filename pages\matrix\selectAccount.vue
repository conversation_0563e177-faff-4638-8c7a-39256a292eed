<template>
	<view>
		<view style="height: 20rpx;"></view>
		
		<block v-if="list.length > 0">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public" @click="getSel(item)">
					<view class="display-a margin-bottom_10rpx">
						<image class="matrix-15" :src="isSel.indexOf(item.id) != -1 ? imgUrl+'384.png' : imgUrl+'383.png'"></image>
						<image class="head" :src="item.avatar"></image>
						<view style="width: 384rpx;">
							<view class="font-size_30rpx margin-bottom_10rpx">{{item.account_name}}</view>
							<view class="color_999999 font-size_24rpx">到期时间: {{item.expires_in}}</view>
						</view>
						<block v-if="item.status == 2" class="empower" style="background: #F00;">已失效</block>
						<view v-else class="empower" :style="isEmpower(item) ? '' : 'background: #F00;'">
							{{isEmpower(item) ? '授权中' : '已失效'}}</view>
					</view>
				</view>
			</block>
		</block>
		<mescroll-empty v-else></mescroll-empty>
		
		<view style="height: 250rpx;"></view>
		
		<view class="list-bott">
			<view class="display-a margin-bottom_30rpx">
				<image class="matrix-15" @click="getAllSel()" :src="allSel ? imgUrl+'384.png' : imgUrl+'383.png'"></image>
				<view class="color_00FFCA" @click="getAllSel()">全选({{isSel.length+'/'+total}})</view>
			</view>
			<view class="bott-but" @click="getBack()">确认</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				list: [],
				
				obj: {
					groupId: '',
					groupName: '',
					selLabel: '',  //1。D音 2。K手 3。视频号 4.小红薯 5.B站    //1抖音h5发布 3矩阵首页待用户发布查询 4矩阵首页快手发布
				},
				
				isSel: [],
				allSel: false,
				groupArr: [], //选中的账户数据
				
				groupList: [],
				
				total: 0, //总条数
			}
		},
		
		computed: {
			// 是否已授权
			isEmpower() {
				return function(row) { //使用函数返回
					let isEmpower = false;
					// true,已授权 ,为false，则为已失效
					// row.type  1D音2K手
					isEmpower = new Date(this.msToDate(new Date()).hasTime.replace(/-/g, "\/")) < new Date(this
						.date(row.expires_in).replace(/-/g, "\/"))
					
					return isEmpower;
				}
			},
		
		},
		
		onLoad(options) {
			
			this.obj = JSON.parse(options.obj);
			if (this.obj.selLabel) {
				this.isSel = [];
				this.groupArr = this.obj.groupArr;
				for (let i = 0 ; i < this.groupArr.length ; i++) {
					this.isSel.push(this.groupArr[i].id);
				}
				this.getAccountPageList();
			}
			
		},
		
		onShow() {
			
		},
		
		methods: {
			
			getBack () {
				
				if (this.isSel.length == 0) {
					this.$sun.toast("请先选择账户", 'none');
					return;
				}
				
				var pages = getCurrentPages();
				var prevPage = pages[pages.length - 2]; //上一个页面
				prevPage.$vm.otherFun(JSON.stringify(this.groupArr));//重点$vm
				uni.navigateBack();
			},
			
			//全选
			getAllSel() {
				
				this.allSel = !this.allSel;
				
				this.isSel = [];
				this.groupArr = [];
				
				if (this.allSel) {
					for(let i = 0 ; i < this.list.length ; i++) {
						if (this.isEmpower(this.list[i])) {
							this.isSel.push(this.list[i].id);
							this.groupArr.push(this.list[i]);
						}
						if (this.list.length != this.groupArr.length) {
							this.$sun.toast("已失效账户无法选中",'none');
						}
					}
					
				}
				
			},
			
			//选中
			getSel(obj) {
				
				if (obj.status == 2) {
					this.$sun.toast("账户已失效!",'error');
					return;
				}
				
				if (!this.isEmpower(obj)) {
					this.$sun.toast("账户已失效!",'error');
					return;
				}
				
				
				let isBoo = this.isSel.indexOf(obj.id);
				if (isBoo == -1) {
					this.isSel.push(obj.id);
					this.groupArr.push(obj);
					if (Number(this.total == this.isSel.length)) {
						this.allSel = true;
					}
				}else {
					this.isSel.splice(isBoo,1);
					this.groupArr.splice(isBoo,1);
					this.allSel = false;
				}
			},
			
			async getAccountPageList() {
				const result = await this.$http.post({
					url: this.$api.accountList,
					data: {
						type: this.obj.selLabel,
						uid: uni.getStorageSync("uid"),
						account_group_id: this.obj.groupId,
						page: 1,
						psize: 200
					}
				});
				if (result.errno == 0) {
					this.total = result.data.list.length;
					this.list = result.data.list;
					if (Number(this.total == this.isSel.length)) {
						this.allSel = true;
					}
				}
			},
			
			// date:创建日期 time：授权过期时间（需加）
			date(addTime) {
				// 日期或中国标准时间转毫秒数：
				// let result = new Date(date).getTime();
				let allDate = null;
				
				allDate = new Date(addTime).getTime(); // 按秒计算，所以需要*1000（原始毫秒）
				
				// 预计到期时间计算
				let endtime = this.msToDate(allDate).hasTime
				// 去除秒数截取最后：的前面数字
				let index = endtime.lastIndexOf(":")
				endtime = endtime.substring(0, index);
				return endtime
			},
			// 毫秒数或中国标准时间转日期
			msToDate(msec) {
				let datetime = new Date(msec);
				let year = datetime.getFullYear();
				let month = datetime.getMonth();
				let date = datetime.getDate();
				let hour = datetime.getHours();
				let minute = datetime.getMinutes();
				let second = datetime.getSeconds();
				let result1 = year +
					'-' +
					((month + 1) >= 10 ? (month + 1) : '0' + (month + 1)) +
					'-' +
					((date + 1) < 10 ? '0' + date : date) +
					' ' +
					((hour + 1) < 10 ? '0' + hour : hour) +
					':' +
					((minute + 1) < 10 ? '0' + minute : minute) +
					':' +
					((second + 1) < 10 ? '0' + second : second);
			
				let result2 = year +
					'-' +
					((month + 1) >= 10 ? (month + 1) : '0' + (month + 1)) +
					'-' +
					((date + 1) < 10 ? '0' + date : date);
			
				let result = {
					hasTime: result1,
					withoutTime: result2
				};
			
				return result;
			},
			
		}
	}
</script>

<style lang="scss">
	
	.list-public {
		background-color: #1B1C1F;
		color: #FFF;
	}
	
	.bott-but {
		width: 690rpx;
		padding: 30rpx;
		background: linear-gradient(90.00deg, rgb(79, 255, 118),rgb(0, 236, 255) 97.869%);
		border-radius: 100rpx;
		color: #000;
		font-size: 32rpx;
		text-align: center;
	}
	
	.list-bott {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		// background-color: #FFFFFF;
		padding: 30rpx 30rpx 40rpx;
		z-index: 9;
	}
	
	.matrix-15 {
		width: 36rpx;
		height: 36rpx;
		margin-right: 20rpx;
	}
	
	.color_1E6CEB {
		color: #1E6CEB;
	}
	
	.empower {
		background: rgb(0, 134, 255);
		width: 96rpx;
		text-align: center;
		border-radius: 10rpx;
		padding: 8rpx;
		font-size: 24rpx;
		color: #FFFFFF;
		margin-left: auto;
	}
	
	.head {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
	
</style>
