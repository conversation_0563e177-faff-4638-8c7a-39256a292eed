<template>
	<view>
		<view class="r-top">

			<view class="display-a margin-bottom_20rpx">
				<view class="r-line"></view>
				<view class="color_FFFFFF font-size_30rpx">链接地址</view>
				<view class="red-dot">*</view>
			</view>
			<textarea class="margin-bottom_40rpx" v-model="desc" placeholder="支持D音分享链接"
				placeholder-class="placeholder"></textarea>


			<view style="height: 160rpx;"></view>

			<view class="r-but" @click="getAccountInfo()">
				提取文案
				<!-- <span class="margin-left_16rpx">
					{{tallySetObj.ai_video_extraction}}点/1次
				</span> -->
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

				isWhether: true, //判断重复点击

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				desc: '', //介绍

				typeIndex: '', //1导航进入 2标准文本 3高保真

			}
		},

		onLoad(options) {
			if (options.typeIndex) {
				this.typeIndex = options.typeIndex;
			}
		},

		onShow() {
			if (uni.getStorageSync('uid')) {

			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "取消",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},

		methods: {

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			//查询点数是否足够
			async getAccountInfo() {

				if (!this.desc) {
					this.$sun.toast("请输入D音分享链接", 'none');
					return;
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				const result = await this.$http.post({
					url: this.$api.accountInfo,
					data: {
						type: 2, //2视频提取 1文案仿写
						uid: uni.getStorageSync('uid'),
					}
				});
				if (result.errno == 0) {
					this.getCopyGeneration();
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//视频提取
			async getCopyGeneration() {
				const result = await this.$http.post({
					url: this.$api.platformCopywriting,
					data: {
						question: this.desc,
						uid: uni.getStorageSync('uid'),
					},
					mask: true,
					title: '请求中...'
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						if (this.typeIndex == 1) {
							uni.redirectTo({
								url: '/pages/index/AICreation/record'
							})
						} else {
							this.returnNav(result.data.answer);
						}
						// uni.redirectTo({
						// 	url: '/pages/index/AICreation/record'
						// })
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//生成AI文案
			async getAICreation(answer) {

				const result = await this.$http.post({
					url: this.$api.aiCreateAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: '视频提取',
						type: 16,
						question: this.desc, //拼接的文本
						answer: answer, // 接口返回的文本
						words: answer.length
					}
				});
				if (result.errno == 0) {
					this.$sun.toast(result.message);
					setTimeout(() => {
						if (this.typeIndex == 1) {
							uni.redirectTo({
								url: '/pages/index/AICreation/record'
							})
						} else {
							this.returnNav(answer);
						}
						// uni.redirectTo({
						// 	url: '/pages/index/AICreation/record'
						// })
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			//返回上一页
			returnNav(text) {
				var pages = getCurrentPages();
				var prevPage = pages[pages.length - 2]; //上一个页面
				prevPage.$vm.otherFun2(text); //重点$vm
				uni.navigateBack();
			},

		}
	}
</script>

<style lang="scss">
	.placeholder {
		color: #999999;
	}

	.r-but {
		position: fixed;
		bottom: 50rpx;
		z-index: 9;
		font-size: 32rpx;
		color: #FFF;
		padding: 30rpx 0;
		width: 710rpx;
		text-align: center;
		border-radius: 10rpx;
		box-shadow: 4rpx 6rpx 28rpx 0 rgba(30, 156, 214, 0.81), inset 0 0 22rpx 0 rgba(204, 235, 255, 0.3);
		background: linear-gradient(90.00deg, rgb(105, 229, 253), rgb(68, 65, 253) 100%);
	}

	textarea {
		width: 670rpx;
		height: 300rpx;
		padding: 20rpx;
		background-color: #192236;
		border-radius: 10rpx;
		color: #FFF;
	}

	.r-top {
		padding: 30rpx 20rpx;
	}

	.red-dot {
		color: #FF0000;
		margin-left: 8rpx;
	}

	.r-line {
		width: 12rpx;
		height: 32rpx;
		border-radius: 100rpx;
		background: linear-gradient(180.00deg, rgb(109, 221, 245), rgb(66, 72, 244) 100%);
		margin-right: 14rpx;
	}

	page {
		border-top: none;
		background-color: #080E1E;
		overflow-x: hidden;
	}
</style>