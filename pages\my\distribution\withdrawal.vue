<template>
	<view>
		<block v-if="user.id">
			<view class="bg" :style="{'background-image': 'url('+imgUrl+'100.png'+')'}">
				<view
					:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
					class="iconDizhssi">
					<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
					<view class="font-size_32rpx" @click="navig()">
						申请提现
					</view>
				</view>

				<view style="height: 190rpx;"></view>

				<view class="text-align_center color_FFFFFF">
					<view class="margin-bottom_20rpx">我的收益(元)</view>
					<view class="font-size_40rpx font-weight_bold">{{user.brokerage}}</view>
				</view>

			</view>

			<view class="list-public" style="padding: 30rpx 20rpx;margin: -120rpx 20rpx 20rpx;">
				<view class="margin-bottom_40rpx font-weight_bold">收益提现(元)</view>
				<view class="display-a margin-bottom_20rpx">
					<input class="margin-left_10rpx font-weight_bold font-size_32rpx" style="width: 400rpx;"
						type="digit" v-model="commission_wait" placeholder="请输入提现金额" placeholder-class="placeholder" />
					<view class="all" @click="getAll()">全部提现</view>
				</view>
				<view class="color_999999 font-size_26rpx margin-bottom_20rpx">提现手续费 <span
						class="points">{{brokerageSet.service_charge+'%'}}</span></view>
				<view class="color_999999 font-size_26rpx">单次最低提现<span
						class="points">{{brokerageSet.cash_limit}}</span>元,最高提现<span
						class="points">{{brokerageSet.cash_highest}}</span>元</view>
			</view>

			<view class="list-public display-a padding_30rpx">
				<block v-if="brokerageSet.cash_type.indexOf(1) != -1">
					<image @click="getType(1)" class="img-101" :src="imgUrl+'101.png'"></image>
					<view @click="getType(1)" class="font-size_26rpx">提现到支付宝</view>
					<image @click="getType(1)" class="img-103" :src="type == 1 ? imgUrl+'104.png' : imgUrl+'103.png'">
					</image>
					<view class="margin-left-auto"></view>
				</block>
				<block v-if="brokerageSet.cash_type.indexOf(2) != -1">
					<image @click="getType(2)" class="img-101" :src="imgUrl+'102.png'"></image>
					<view @click="getType(2)" class="font-size_26rpx">提现到微信零钱</view>
					<image @click="getType(2)" class="img-103" :src="type == 2 ? imgUrl+'104.png' : imgUrl+'103.png'">
					</image>
				</block>
			</view>

			<view class="list-public padding_30rpx" v-if="type == 1">
				<view class="display-a padding-bottom_30rpx p-bo">
					<view style="width: 160rpx;">姓名</view>
					<input type="text" class="input-x" v-model="nickname" placeholder="请填写姓名"
						placeholder-class="placeholder" />
				</view>
				<view class="display-a padding-top_30rpx">
					<view style="width: 160rpx;">支付宝账号</view>
					<input type="text" class="input-x" v-model="telphone" placeholder="请输入支付宝账号"
						placeholder-class="placeholder" />
				</view>
			</view>

			<view class="but" @click="but()">申请提现</view>

			<view style="height: 20rpx;"></view>

		</block>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				brokerageSet: {},

				user: {},

				heightSystemss: '',
				statusBarHeightss: '',

				imgUrl: this.$imgUrl,

				type: '2', //1线下 2提现到微信

				commission_wait: '', //申请金额
				nickname: '',
				telphone: '',

				price: '', //手续费
				money: '', //到账金额

				isWhether: true, //判断重复点击

				templateObj: {},
			}
		},

		onLoad() {
			this.getSystemInfo();
			this.getTemplate();
		},

		onShow() {
			this.getBrokerageSet();
		},

		methods: {

			//全部提现
			getAll() {
				this.commission_wait = this.user.brokerage;
				// this.getArithmetic();
			},

			//手续费，到账金额
			getArithmetic() {
				// commission_wait: '', //申请金额
				// price: '', //手续费
				// money: '', //到账金额

				//手续费
				let wait = Number(this.brokerageSet.service_charge) / 100;

				//点数转换金额
				let cashCharge = Number(this.commission_wait) / Number(this.brokerageSet.cash_charge);

				//提现的手续费
				let getPrice = Number(cashCharge) * Number(wait);

				this.price = this.$sun.getNumberDigit(getPrice, 2);

				//实际到账金额
				this.money = Number(this.commission_wait) - Number(this.price);

				// console.log("---->",getPrice,price);

			},

			//模板设置
			async getTemplate() {
				const result = await this.$http.post({
					url: this.$api.template,
				});
				if (result.errno == 0) {
					this.templateObj = result.data;
				}
			},

			async but() {

				if (!this.commission_wait) {
					this.$sun.toast("请输入提现金额", 'none');
					return;
				}

				if (Number(this.user.brokerage) < Number(this.commission_wait)) {
					this.$sun.toast("金额不足", 'none');
					return;
				}

				if (Number(this.brokerageSet.cash_limit) > Number(this.commission_wait)) {
					this.$sun.toast("最低提现金额为" + this.brokerageSet.cash_limit, 'none');
					return;
				}

				if (Number(this.brokerageSet.cash_highest) < Number(this.commission_wait)) {
					this.$sun.toast("最高提现金额为" + this.brokerageSet.cash_highest, 'none');
					return;
				}

				if (!this.type) {
					this.$sun.toast("请选择提现方式", 'none');
					return;
				}

				if (this.type == 1) {
					if (!this.nickname) {
						this.$sun.toast("请填入姓名", 'none');
						return;
					}

					if (!this.telphone) {
						this.$sun.toast("请输入支付宝账号", 'none');
						return;
					}
				}

				if (!this.isWhether) {
					return;
				}
				this.isWhether = false;

				if (wx.canIUse('requestMerchantTransfer')) {
					uni.getSetting({
						withSubscriptions: true,
						success: (res) => {
							console.log(res.subscriptionsSetting);
							if (res.subscriptionsSetting.mainSwitch == false) {
								this.save();
							} else {
								// 获取下发权限
								uni.requestSubscribeMessage({
									tmplIds: [this.templateObj.withdraw_apply_template, this
										.templateObj.withdraw_result_template
									], //此处写在后台获取的模板ID，可以写多个模板ID，看自己的需求
									success: async (data) => {
										if (data[this.templateObj.withdraw_apply_template] ==
											'accept'
											) { //accept--用户同意 reject--用户拒绝 ban--微信后台封禁,可不管
											this.save();
										} else {
											uni.showModal({
												title: '温馨提示',
												content: '您已拒绝授权，将无法在微信中收到通知！',
												showCancel: false,
												success: res => {
													if (res.confirm) {
														// 这里可以写自己的逻辑
														this.save();
														console.log('拒绝授权', data);
													}
												}
											})
										}
									},
									fail: (err) => {
										this.save();
										console.log('失败', err);
									},
									complete: (result) => {

										console.log('完成', result);
									}
								});
							}
						}
					});
				} else {
					wx.showModal({
						content: '你的微信版本过低，请更新至最新版本。',
						showCancel: false,
					});
				}
			},


			async save() {
				const result = await this.$http.post({
					url: this.$api.brokerageCashwait,
					data: {
						uid: uni.getStorageSync('uid'),
						commission_wait: this.commission_wait,
						cash_type: this.type,
						telphone: this.telphone,
						nickname: this.nickname
					}
				});
				if (result.errno == 0) {
					if (!result.data) {
						this.$sun.toast("申请成功,请等待管理员审核", 'none');
						setTimeout(() => {
							this.commission_wait = '';
							this.isWhether = true;
							this.getUser();
							this.navig();
						}, 2000);
					} else {
						wx.requestMerchantTransfer({
							mchId: result.data.pay_mchid,
							appId: result.data.pay_appid,
							package: result.data.package_info,
							success: (res) => {
								this.$sun.toast("提现成功");
								setTimeout(() => {
									this.commission_wait = '';
									this.isWhether = true;
									this.getUser();
									this.navig();
								}, 2000);
							},
							fail: (res) => {
								// this.$sun.toast(res, 'none');
								this.isWhether = true;
								console.log('fail:', res);
							},
						});
					}
				} else {
					this.isWhether = true;
					this.$sun.toast(result.message, 'none');
				}
			},



			getType(type) {
				this.type = type;
			},

			//分销设置
			async getBrokerageSet() {
				const result = await this.$http.post({
					url: this.$api.brokerageSet,
				});
				if (result.errno == 0) {
					this.brokerageSet = result.data;
					if (this.brokerageSet.is_open == 1) {
						if (uni.getStorageSync('uid')) {
							this.getUser();
							if (this.brokerageSet.cash_type.indexOf(1) != -1) {
								this.type = 1;
							}
							if (this.brokerageSet.cash_type == 2) {
								this.type = 2;
							}
						} else {
							uni.showModal({
								content: "请先登录",
								cancelText: "返回",
								confirmText: "去登录",
								success: (res) => {
									if (res.confirm) {
										uni.navigateTo({
											url: '/pages/auth/auth?type=1'
										})
									} else if (res.cancel) {
										this.navig();
									}
								}
							})
						}
					} else {
						uni.showModal({
							content: "暂未开启分销",
							confirmText: "确认",
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									uni.redirectTo({
										url: '/pages/my/my?index=3'
									})
								} else if (res.cancel) {

								}
							}
						})
					}
				}
			},

			//用户信息
			async getUser() {
				const result = await this.$http.post({
					url: this.$api.userInfo,
					data: {
						uid: uni.getStorageSync('uid')
					}
				});
				if (result.errno == 0) {

					this.user = result.data;

					if (this.user.is_freeze == 2) {
						this.getIsUser();
					}

				}
			},

			getIsUser() {
				uni.showModal({
					content: "您的账号已冻结,请联系管理员!",
					confirmText: "确认",
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/my/my?index=2'
							})
						} else if (res.cancel) {

						}
					}
				})
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	.p-bo {
		border-bottom: 1px solid rgb(48, 48, 48);
	}

	.list-public {
		background-color: #1A1A1A;
		color: #FFF;
	}

	.points {
		color: #FF0000;
		font-weight: bold;
		font-size: 30rpx;
		margin: 0 6rpx;
	}

	.but {
		width: 560rpx;
		border-radius: 32px;
		box-shadow: inset 0px 4px 4px 0px rgba(255, 158, 148, 0.32), 0px 4px 4px 0px rgba(254, 82, 65, 0.15);
		background: linear-gradient(100.41deg, rgb(255, 125, 98) 2.1%, rgb(254, 79, 63) 99.742%);
		margin: 120rpx 94rpx 20rpx;
		text-align: center;
		color: #FFFFFF;
		font-size: 32rpx;
		padding: 26rpx 0;
	}

	.input-x {
		width: 490rpx;
		text-align: right;
	}

	.img-103 {
		width: 30rpx;
		height: 30rpx;
		margin-left: 20rpx;
	}

	.img-101 {
		width: 46rpx;
		height: 46rpx;
		margin-right: 14rpx;
	}

	.all {
		width: 130rpx;
		color: #FFFFFF;
		text-align: center;
		padding: 12rpx 0;
		border-radius: 100rpx;
		background-color: #EF5D31;
		margin-left: auto;
		font-size: 24rpx;
	}

	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.bg {
		width: 750rpx;
		height: 464rpx;
		background-repeat: no-repeat;
		background-size: cover;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		// font-size: 30rpx;
		// font-weight: bold;
		display: flex;
		align-items: center;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #000;
	}
</style>