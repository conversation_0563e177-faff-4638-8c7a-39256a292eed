<template>
	<view>
		<view>
			<image class="invite-friends-bg" :src="poster" mode=""></image>
			<image class="qr-code" :src="qrCode" mode=""></image>
		</view>
		<view class="function-button">
			<view class="button-code" @click="openPost">
				<text class="iconfont icon-46 font-weight_bold"></text>生成海报
			</view>
			<button type="button" class="button-code" open-type="share">
				<text class="iconfont icon-fenxiang font-weight_bold"></text>立即分享
			</button>
		</view>
		<poster ref="poster" @closePost="closePost" v-if="posterShow"></poster>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				obj: {},

				imgUrl: this.$imgUrl,

				posterShow: false,
				
				poster: '',
				qrCode: '',

			};
		},
		
		onLoad(options) {
			if (options.poster && options.poster != null){
				this.poster = options.poster;
			}
			if (options.qrCode && options.qrCode != null){
				this.qrCode = options.qrCode;
			}
			// console.log(this.poster,this.qrCode);
		},
		
		onShow() {
			if (!uni.getStorageSync('uid')) {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},

		onShareAppMessage() {
			return {
				title: uni.getStorageSync('system').share_title,
				path: '/pages/index/index?scene='+uni.getStorageSync('uid'),
				imageUrl: uni.getStorageSync('system').share_pic,
				desc: uni.getStorageSync('system').share_desc,
			}
		},
		// 分享朋友圈(微信小程序)
		onShareTimeline() {
			return {
				title: uni.getStorageSync('system').share_title,
				query: 'scene='+uni.getStorageSync('uid'),
				imageUrl: uni.getStorageSync('system').share_pic,
			}
		},

		methods: {
			
			navig() {
				let pages = getCurrentPages();  //获取所有页面栈实例列表
				
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}else {
					uni.navigateBack();
				}
			},
			
			/*  推广二维码  */
			closePost() {
				this.posterShow = false;
			},
			openPost() {
				
				if (!this.poster) {
					this.$sun.toast("请先联系管理员设置分销海报",'none');
					return;
				}
				
				if (!this.qrCode) {
					this.$sun.toast("暂无分销二维码,请联系管理员",'none');
					return;
				}
				
				uni.showLoading({
					title: '加载中...',
					mask: true
				});
				this.posterShow = true;
				let param = {
					icon: this.poster,
					code: this.qrCode,
				}
				setTimeout(() => {
					this.$refs.poster.drawPoster(param); //在组件内drawPoster()函数编写需要绘制的内容
				}, 1000);
			},

		}
	};
</script>

<style lang="scss">
	
	page {
		background-color: #ffffff;
	}

	.invite-friends-bg {
		width: 630rpx;
		height: 952rpx;
		display: block;
		margin: 44rpx auto 0;
	}

	.qr-code {
		width: 162rpx;
		height: 162rpx;
		display: block;
		position: absolute;
		margin: -228rpx 0 0;
		right: 92rpx;
		z-index: 9;
	}

	.function-button {
		width: 510rpx;
		margin: 30rpx 120rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #FFFFFF;

	}

	.button-code {
		width: 226rpx;
		height: 66rpx;
		border: 2rpx solid #A1A1A1;
		line-height: 62rpx;
		text-align: center;
		border-radius: 10rpx;
		color: #5F5D5D;
	}

	button {
		padding-left: 0;
		padding-right: 0;
		background-color: #FFFFFF;
		line-height: 1;
		font-size: 28rpx;
		font-weight: 500;
	}
	
</style>

