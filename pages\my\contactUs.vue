<!-- 个人中心——联系我们 -->
<template>
	<view class="contact-us-page">
		<image :src="imgUrl+'272.jpg'" mode="widthFix"></image>
		<view class="display-a p-content" v-if="kfSet.kf_phone">
			<image :src="imgUrl+'273.png'" mode="widthFix"
				style="width: 68rpx;height: 68rpx;margin-right: 20rpx;"></image>
			<view class="msg-info flex-columns" @click="callPhone(kfSet.kf_phone)">
				<text>电话 | TEL</text>
				<view style="color: blue;">{{kfSet.kf_phone}}</view>
			</view>
		</view>
		<!-- <view class="display-a p-content" v-if="kfSet.kf_phone" @click="getCopy(kfSet.kf_phone)">
			<image :src="imgUrl+'277.png'" mode="widthFix"
				style="width: 68rpx;height: 68rpx;margin-right: 20rpx;"></image>
			<view class="msg-info flex-columns">
				<text>微信 | WeChat</text>
				<view>{{kfSet.kf_phone}}</view>
			</view>
		</view> -->
		<view class="display-a p-content" v-if="kfSet.kf_mailbox" @click="getCopy(kfSet.kf_mailbox)">
			<image :src="imgUrl+'274.png'" mode="widthFix"
				style="width: 68rpx;height: 68rpx;margin-right: 20rpx;"></image>
			<view class="msg-info flex-columns">
				<text>邮箱 | Mailbox</text>
				<view>{{kfSet.kf_mailbox}}</view>
			</view>
		</view>

		<view class="display-a p-content">
			<image :src="imgUrl+'275.png'" mode="widthFix"
				style="width: 68rpx;height: 68rpx;margin-right: 20rpx;"></image>
			<view class="msg-info flex-columns">
				<button open-type="contact" class="msg-info flex-columns" style="position:relative;">联系客服 | WeChat
					customer service</button>
			</view>
		</view>

		<view class="display-a p-content" v-if="kfSet.kf_date">
			<image :src="imgUrl+'276.png'" mode="widthFix"
				style="width: 68rpx;height: 68rpx;margin-right: 20rpx;"></image>
			<view class="msg-info flex-columns">
				<text>时间 | Time</text>
				<view>{{kfSet.kf_date}}</view>
			</view>
		</view>


		<view class="code" v-if="kfSet.kf_img">
			<image @click="getImg()" class="kf-img" :src="kfSet.kf_img"></image>
			<view class="">扫一扫添加好友</view>
		</view>
		
		<view style="height: 20rpx;"></view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				kfSet: {},
				
				imgUrl: this.$imgUrl,
			};
		},

		onLoad() {
			this.indexkfSet();
		},

		onShow() {

		},
		methods: {
			// 客服设置接口
			async indexkfSet() {
				const resolu = await this.$http.get({
					url: this.$api.kfSet
				})
				if (resolu.errno == 0) {
					this.kfSet = resolu.data
				} else {
					this.$sun.toast(resolu.message,"none")
				}
			},

			//复制
			getCopy(text) {
				if (text) {
					uni.setClipboardData({
						data: text,
						success: () => {
							// 复制成功的回调
							this.$sun.toast('复制成功');
							// uni.showToast({
							// 	title: '复制成功',
							// 	icon: 'success',
							// 	duration: 4000
							// });
						},
						fail: (err) => {
							console.log("复制失败原因===>",err);
							// 复制失败的回调
							uni.showToast({
								title: '复制失败：'+err,
								icon: 'none'
							});
						}
					});
				}
			},

			//预览图片
			getImg() {
				uni.previewImage({
					urls: [this.kfSet.kf_img],
				});
			},

			//拨打电话
			callPhone() {
				if (this.kfSet.kf_phone) {
					this.$sun.phone(this.kfSet.kf_phone);
				} else {
					this.$sun.toast("暂无联系方式", 'error');
				}
			},

		}
	};
</script>

<style lang="less">
	.kf-img {
		width: 200rpx;
		height: 200rpx;
	}

	.contact-us-page {
		// image {
		// 	width: 100%;
		// }

		.p-content {
			margin: 0 20rpx;
			padding: 40rpx 10rpx 36rpx;
			border-bottom: 2rpx solid #EAEAEA;
			position: relative;

			.icon-size {
				font-size: 64rpx;
				margin-right: 40rpx;
				color: #19C866;
			}

			.msg-info {
				text {
					color: #999999;
					font-size: 26rpx;
				}

				view {
					font-size: 32rpx;
					margin-top: 10rpx;
				}
			}
		}
	}

	button {
		background: white;
		border: none;
		text-align: left;
		margin: 0 !important;
		padding: 0 !important;
		padding: 0px 20rpx;
		font-size: 30rpx;
		margin-top: 10rpx;
		width: 100%;
		display: block;
		color: #000 !important;
	}

	button::after {
		border: none;
	}

	.code {
		text-align: center;
		margin: 50rpx 0 0;
	}

	page {
		background-color: #FFFFFF;
		border-top: none;
	}
</style>
