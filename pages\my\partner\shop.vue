<template>
	<view>
		
		<view style="height: 20rpx;"></view>
		
		<swiper  :indicator-dots="indicatorDots"
			indicator-active-color="#FFFFFF" :autoplay="autoplay" :circular="true" :interval="interval"
			:duration="duration">
			<swiper-item v-for="(item,index) in banner" :key="index">
				<image class="pic-img" :src="item.pic_url"
					@click="changeUrl(index,item)"></image>
			</swiper-item>
		</swiper>
		
		<view class="s-top">卡密列表</view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<view class="display-fw-js padding_0_20rpx">
				<block v-for="(item,index) in list" :key="index">
					<view class="s-list" @click="detail(item.id)">
						<image class="img-pic" :src="item.pic_url"></image>
						<view class="padding_10rpx color_FFFFFF">
							<view class="font-size_32rpx font-overflow margin-bottom_20rpx">{{item.name}}</view>
							<!-- <view class="font-size_24rpx margin-bottom_10rpx">形象克隆: {{item.avatar_twin_count}}(次)</view>
							<view class="font-size_24rpx margin-bottom_10rpx">声音克隆: {{item.voice_twin_count}}(次)</view> -->
							<!-- <view class="font-size_24rpx margin-bottom_20rpx">点数: {{item.point}}(点)</view> -->
							<view class="d-count margin-bottom_30rpx">
								获得: {{item.type == 1 ? item.day+'(天)' : item.point+'(点)'}}
							</view>
							<view class="display-a-js">
								<view class="s-money"><span class="font-size_26rpx">￥</span>{{Number(item.money)}}</view>
								<view class="s-sold">已售:{{item.sold}}</view>
							</view>
						</view>
					</view>
				</block>
			</view>
		</mescroll-body>
		
		<sunui-tabbar :fixed="true" :current="tabIndex" :types="2" tintColor="#23E9FF" backgroundColor="#323232"></sunui-tabbar>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				tabIndex: 2,
				
				// 轮播图
				indicatorDots: true,
				autoplay: true,
				interval: 4000,
				duration: 500,
				
				banner: [],
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
			}
		},
		
		onLoad() {
			this.getBanner();
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 560;
				},
			})
		},
		
		onShow() {
			this.$nextTick(() => {
				this.mescroll.resetUpScroll();
			});
		},
		
		methods: {
			
			//商品详情
			detail(id) {
				uni.navigateTo({
					url: '/pages/my/partner/shopDetail?shopId='+id
				})
			},
			
			//商品列表
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.partnerGoodsList,
					data: {
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
			//自定义跳转
			changeUrl(index, values) {
				if (values.type == 1) {
					uni.navigateTo({
						url: values.url
					});
				}
				if (values.type == 2) {
					
					if (values.appid) {
						wx.navigateToMiniProgram({
							appId: values.appid,
							success: (res) => {
								// 打开成功
								// console.log('成功', res);
							},
							fail: (err) => {
								// console.log('失败', err);
							}
						})
					}else {
						this.$sun.toast("请检查跳转外部小程序的APPID是否正确",'none');
					}
				}
			},
			
			//轮播图
			async getBanner() {
				const result = await this.$http.post({
					url: this.$api.banner,
					data: {
						b_type: 2
					}
				});
				if (result.errno == 0) {
					this.banner = result.data;
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.d-count {
		// width: 332rpx;
		// font-size: 26rpx;
		display: inline-block;
		padding: 10rpx 20rpx;
		background-color: #4C4C4C;
		border-radius: 10rpx;
		font-size: 24rpx;
		color: #FFF;
	}
	
	.s-sold {
		color: #CDCDCD;
		font-size: 24rpx;
	}
	
	.s-money {
		color: #FFC100;
		font-weight: bold;
		font-size: 34rpx;
	}
	
	.s-top {
		padding: 30rpx 20rpx;
		color: #FFF;
		font-size: 32rpx;
		font-weight: bold;
	}
	
	.img-pic {
		width: 320rpx;
		height: 320rpx;
	}
	
	.s-list {
		width: 320rpx;
		border-radius: 0px 0px 10rpx 10rpx;
		background: rgb(47, 47, 47);
		margin-bottom: 20rpx;
	}
	
	.pic-img {
		width: 710rpx;
		height: 280rpx;
		// margin: 0 20rpx;
		border-radius: 10rpx;
	}
	
	swiper {
		width: 710rpx;
		height: 280rpx;
		margin: 0 20rpx;
	}
	
	page {
		background-color: #1D1D1D;
		border-top: 1px solid rgb(56, 56, 56);;
	}
	
</style>
