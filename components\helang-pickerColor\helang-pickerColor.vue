<template>
	<view>
		<view class="flex_col" style="padding: 30rpx;">
			<view class="preview" :style="{'backgroundColor':pickerColor}"></view>
			<view class="value">
				<text v-if="pickerColor">颜色值：{{pickerColor}}</text>
			</view>
			<view class="ok" @tap="setColor">确定</view>
		</view>
		<view class="list flex_col" style="padding: 0 20rpx;" v-for="(item,index) in colorArr" :key="index">
			<view class="h-color" v-for="(v,i) in item" :key="i" 
				:style="{'backgroundColor':v}" 
				:data-color="v" 
				:data-index="index" 
				:data-i="i" 
				:class="{'active':(index==pickerArr[0] && i==pickerArr[1])}"
				@tap="picker"></view>
		</view>
	</view>
</template>

<script>
	export default {
		name:'picker-color',
		props:{
			defaultColor: {
				type: String,
				default: ''
			},
		},
		data() {
			return {
				colorArr:[
					['#000000','#111111','#222222','#333333','#444444','#666666','#999999','#CCCCCC','#EEEEEE','#FFFFFF'],
					['#ff0000','#ff0033','#ff3399','#ff33cc','#cc00ff','#9900ff','#cc00cc','#cc0099','#cc3399','#cc0066'],
					['#cc3300','#cc6600','#ff9933','#ff9966','#ff9999','#ff99cc','#ff99ff','#cc66ff','#9966ff','#cc33ff'],
					['#663300','#996600','#996633','#cc9900','#a58800','#cccc00','#ffff66','#ffff99','#ffffcc','#ffcccc'],
					['#336600','#669900','#009900','#009933','#00cc00','#66ff66','#339933','#339966','#009999','#33cccc'],
					['#003366','#336699','#3366cc','#0099ff','#000099','#0000cc','#660066','#993366','#993333','#800000']
				],
				pickerColor: '',
				pickerArr:[-1,-1]
			};
		},
		created() {
			this.pickerColor = this.defaultColor;
		},
		methods: {
			picker(e) {
				let data=e.currentTarget.dataset;
				this.pickerColor=data.color;
				this.pickerArr=[data.index,data.i];
			},
			setColor(){
				this.$emit("callback",this.pickerColor);
				this.$sun.toast("设置成功");
			}
		},
		
	}
</script>

<style scoped>

.flex_col{
	display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    align-content: center;
}
.list{
    justify-content: space-between;
}
.h-color{
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 16upx;
	box-sizing: border-box;
	border-radius: 3px;
	box-shadow: 0 0 2px #ccc;
}
.list .active{
	box-shadow: 0 0  2px #09f;
	transform:scale(1.05,1.05);
}
.preview{
	width: 180upx;
	height: 60upx;
}
.value{
	margin: 0 40upx;
	flex-grow: 1;
	color: #FFF;
}
.ok{
	width: 160upx;
	height: 60upx;
	line-height: 60upx;
	text-align: center;
	background: linear-gradient(180.00deg, rgb(89, 238, 82),rgb(176, 255, 130) 100%);
	color: #000;
	border-radius: 4px;
	letter-spacing: 3px;
	font-size: 32upx;
}
.ok:active{
	background: linear-gradient(180.00deg, rgb(89, 238, 82),rgb(176, 255, 130) 100%);
}
</style>
