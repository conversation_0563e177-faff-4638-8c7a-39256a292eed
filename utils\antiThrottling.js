export default {
	keyList: {},
	uniDoFunc(e = {}) {
		e.type = e.type || 'thro';
		e.instant = (!e.instant?true : e.instant);
		switch (e.type) {
			case 'anti':
				{
					if (e.instant && !this.keyList[e.key]) {
						e.success && e.success();
					}
					clearTimeout(this.keyList[e.key])
					this.keyList[e.key] = setTimeout(() => {
						e.success && e.success();
						this.releaseKey(e.key)
					}, e.time || 300);
					break;
				}
			case 'thro':
				{
					if (!this.keyList[e.key]) {
						if (e.instant) {
							this.lockKey(e.key);
							e.success && e.success();
						} else {
							this.lockKey(e.key);
						}
						if (e.time) {
							setTimeout(() => {
								if (!e.instant) {
									e.success && e.success();
								}
								this.releaseKey(e.key)
							}, e.time)
						}
					} else {
						e.fail && e.fail()
					}
					break;
				}
		}
	},
	release<PERSON><PERSON>(key) {
		delete this.keyList[key];
	},
	lock<PERSON><PERSON>(key) {
		this.keyList[key] = true;
	}
}
