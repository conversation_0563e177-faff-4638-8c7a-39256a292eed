<template>
	<view class="chat-item" :class="item.role === 'user' ? 'in' : 'out'">
		<!-- 用户消息 -->
		<view class="user-bubble" v-if="item.role === 'user'">
			<!-- 用户文本内容 -->
			<view v-if="item.content" class="user-text">{{ item.content }}</view>
			
			<!-- 用户参考图片 -->
			<view v-if="item.reference_images && item.reference_images.length > 0" class="user-reference-images">
				<view v-for="(img, index) in item.reference_images" :key="index" class="reference-image-item" @click="onReferenceImageClick(img)">
					<image :src="img" class="reference-image" mode="aspectFill" @load="onImageLoad" @error="onImageError"></image>
					<view class="reference-image-overlay">
						<text class="reference-image-text">参考图片</text>
					</view>
				</view>
			</view>
			<view v-else-if="item.img_url" class="user-reference-images">
				<view  class="reference-image-item" @click="onReferenceImageClick(item.img_url)">
					<image :src="item.img_url" class="reference-image" mode="aspectFill" @load="onImageLoad" @error="onImageError"></image>
					<view class="reference-image-overlay">
						<text class="reference-image-text">参考图片</text>
					</view>
				</view>
			</view>
		</view>

		<!-- AI回复（图片、视频和/或Markdown） -->
		<view class="ai-container" v-else>
			<!-- 视频消息 -->
			<view class="ai-video-container" v-if="hasVideo" @click="onVideoClick">
				<video class="ai-video" :src="item.video_url" controls show-center-play-btn show-play-btn show-fullscreen-btn>
				</video>
				<view class="content-actions">
					<view class="video-hint">
						<text class="video-text">点击播放</text>
					</view>
					<view class="action-btn favorite-btn" v-if="showVideoFavorite" @click.stop="onFavorite('video')">
						<text class="favorite-text">收藏</text>
					</view>
				</view>
			</view>

			<!-- 图片消息 -->
			<view class="ai-image-container" v-else-if="hasImage" @click="onImageClick">
				<image class="ai-image" :src="item.img_url" mode="aspectFit" @load="onImageLoad" @error="onImageError">
				</image>
				<view class="content-actions">
					<view class="zoom-hint">
						<text class="zoom-text">点击放大</text>
					</view>
					<view class="action-btn favorite-btn" v-if="showImageFavorite" @click.stop="onFavorite('image')">
						<text class="favorite-text">收藏</text>
					</view>
				</view>
			</view>

			<!-- Markdown内容 -->
			<view class="ai-markdown-container" v-else-if="hasMarkdown">
				<towxmlComponent :nodes="formattedContent" className="noneBgc" />
				<view class="markdown-actions">
					<view class="action-btn copy-btn" @click="copyMarkdown">复制</view>
					<view class="action-btn favorite-btn" v-if="showTextFavorite" @click="onFavorite('text')">收藏</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// 导入towxml库
	const towxml = require('../../wxcomponents/towxml/index');

	// 导入markdown处理相关工具
	import {
		markdownToText
	} from '../../utils/decodedString.js';
	import towxmlComponent from '../../wxcomponents/towxml/towxml'

	export default {
		name: 'ChatItem',
		components: {
			towxmlComponent
		},
		props: {
			item: {
				type: Object,
				required: true,
				default: () => ({})
			},
			// 添加imageUrls属性，用于多图预览
			imageUrls: {
				type: Array,
				default: () => []
			},
			// 添加主题设置
			theme: {
				type: String,
				default: 'dark',
				validator: (value) => ['light', 'dark'].includes(value)
			},
			// 是否显示文本收藏按钮
			showTextFavorite: {
				type: Boolean,
				default: false
			},
			// 是否显示图片收藏按钮
			showImageFavorite: {
				type: Boolean,
				default: false
			},
			// 是否显示视频收藏按钮
			showVideoFavorite: {
				type: Boolean,
				default: false
			},
			// 会话类型
			sessionType: {
				type: [String, Number],
				default: ''
			}
		},
		computed: {
			// 检查是否有视频URL
			hasVideo() {
				return this.item.role === "assistant" && this.item.video_url;
			},
			// 检查是否有图片URL
			hasImage() {
				return this.item.role === "assistant" && this.item.img_url;
			},
			// 检查是否有Markdown内容
			hasMarkdown() {
				return this.item.role === "assistant" && this.item.content;
			},
			// 转换Markdown为towxml格式
			formattedContent() {
				if (!this.hasMarkdown) return null;
				return towxml(this.item.content, 'markdown', {
					theme: this.theme, // 使用从props传入的主题
					className: 'dci'
				});
			}
		},
		methods: {
			// 视频点击事件
			onVideoClick() {
				if (this.hasVideo) {
					// 视频已经在video标签中播放，这里可以添加额外的处理逻辑
					console.log('视频点击:', this.item.video_url);
				} else {
					this.$emit('video-click', this.item);
				}
			},
			// 图片点击事件
			onImageClick() {
				if (this.hasImage) {
					this.previewImage(this.item.img_url);
				} else {
					this.$emit('image-click', this.item);
				}
			},
			// 参考图片点击事件
			onReferenceImageClick(imgUrl) {
				this.previewImage(imgUrl);
			},
			// 图片加载完成事件
			onImageLoad(e) {
				this.$emit('image-load', e);
			},
			// 图片加载失败事件
			onImageError(e) {
				this.$emit('image-error', e);
			},

			// 预览图片
			previewImage(url) {
				try {
					// 使用uni-app的图片预览API
					uni.previewImage({
						current: url, // 当前显示图片的链接
						urls: this.imageUrls.length > 0 ? this.imageUrls : [url], // 需要预览的图片链接列表
						indicator: 'number', // 显示顺序指示器
						loop: true, // 是否开启循环预览
						longPressActions: {
							itemList: ['保存图片', '复制链接'],
							success: (data) => {
								// 用户点击了选项
								const {
									tapIndex
								} = data;
								if (tapIndex === 0) {
									// 保存图片
									this.saveImage(url);
								} else if (tapIndex === 1) {
									// 复制链接
									uni.setClipboardData({
										data: url,
										success: () => {
											uni.showToast({
												title: '链接已复制',
												icon: 'none'
											});
										}
									});
								}
							}
						},
						success: () => {
							console.log('预览图片成功');
							this.$emit('preview-success', url);
						},
						fail: (err) => {
							console.error('预览图片失败', err);
							uni.showToast({
								title: '预览图片失败',
								icon: 'none'
							});
							this.$emit('preview-error', {
								url,
								error: err
							});
						}
					});
				} catch (error) {
					console.error('预览图片异常', error);
					uni.showToast({
						title: '预览图片失败',
						icon: 'none'
					});
					this.$emit('preview-error', {
						url,
						error
					});
				}
			},

			// 保存图片到相册
			saveImage(url) {
				uni.showLoading({
					title: '保存中...'
				});

				// 下载图片
				uni.downloadFile({
					url: url,
					success: (res) => {
						if (res.statusCode === 200) {
							// 保存图片到相册
							uni.saveImageToPhotosAlbum({
								filePath: res.tempFilePath,
								success: () => {
									uni.hideLoading();
									uni.showToast({
										title: '图片已保存到相册',
										icon: 'none'
									});
									this.$emit('save-success', url);
								},
								fail: (err) => {
									uni.hideLoading();
									// 如果是用户拒绝授权
									if (err.errMsg.indexOf('auth deny') >= 0) {
										uni.showToast({
											title: '保存失败，请授权相册访问权限',
											icon: 'none'
										});
									} else {
										uni.showToast({
											title: '保存图片失败',
											icon: 'none'
										});
									}
									this.$emit('save-error', {
										url,
										error: err
									});
								}
							});
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '下载图片失败',
								icon: 'none'
							});
							this.$emit('save-error', {
								url,
								error: 'Download failed'
							});
						}
					},
					fail: (err) => {
						uni.hideLoading();
						uni.showToast({
							title: '下载图片失败',
							icon: 'none'
						});
						this.$emit('save-error', {
							url,
							error: err
						});
					}
				});
			},

			// 复制Markdown内容为纯文本
			copyMarkdown() {
				if (this.hasMarkdown) {
					const plainText = markdownToText(this.item.content);
					uni.setClipboardData({
						data: plainText,
						success: () => {
							uni.showToast({
								title: '复制成功',
								icon: 'none'
							});
							this.$emit('copy-success', {
								content: plainText,
								item: this.item
							});
						},
						fail: (err) => {
							console.error('复制失败:', err);
							uni.showToast({
								title: '复制失败',
								icon: 'none'
							});
							this.$emit('copy-error', {
								error: err,
								item: this.item
							});
						}
					});
				}
			},

			// 收藏内容
			async onFavorite(contentType) {
				try {
					// 构建收藏接口参数
					let content = '';
					let category = 1; // 默认文本
					let cover_img = ''; // 封面图片字段

					// 根据内容类型设置参数
					switch (contentType) {
						case 'image':
							content = this.item.img_url;
							category = 2; // 图片
							break;
						case 'video':
							content = this.item.video_url;
							category = 3; // 视频
							// 生成视频封面URL - 使用阿里云OSS视频处理参数获取第一帧
							if (this.item.video_url) {
								cover_img = this.item.video_url + '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto';
							}
							break;
						case 'text':
							content = markdownToText(this.item.content);
							category = 1; // 文本
							break;
					}

					// 构建请求数据
					const requestData = {
						type: this.sessionType,
						uid: uni.getStorageSync('uid'),
						content: content,
						category: category
					};

					// 如果有封面图片，添加到请求数据中
					if (cover_img) {
						requestData.cover = cover_img;
					}

					// 调用收藏接口
					const result = await this.$http.post({
						url: this.$api.addFavorite,
						data: requestData
					});

					if (result.errno === 0) {
						// 收藏成功
						uni.showToast({
							title: '收藏成功',
							icon: 'none'
						});

						// 发送收藏成功事件给父组件
						this.$emit('favorite-success', {
							item: this.item,
							contentType: contentType,
							sessionType: this.sessionType,
							result: result
						});
					} else {
						// 接口返回错误
						uni.showToast({
							title: result.message || '收藏失败',
							icon: 'none'
						});

						this.$emit('favorite-error', {
							error: result.message,
							item: this.item,
							contentType: contentType
						});
					}
				} catch (error) {
					console.error('收藏失败:', error);
					uni.showToast({
						title: '收藏失败',
						icon: 'none'
					});

					this.$emit('favorite-error', {
						error: error,
						item: this.item,
						contentType: contentType
					});
				}
			}
		}
	}
</script>

<style lang="scss">
	/* 将样式设置为全局，以便外部样式可以影响组件 */
	.chat-item {
		display: flex;
		margin-bottom: 40rpx;
		position: relative;
		animation: fadeIn 0.3s ease;
		width: 100%;
		/* 确保占满宽度 */

		/* 因为在外部已经定义了.in和.out类，所以这里不需要再重复定义 */

		.user-bubble {
			max-width: 70%;
			padding: 20rpx 30rpx;
			background: linear-gradient(135deg, #6366f1, #8b5cf6);
			color: #fff;
			border-radius: 20rpx 4rpx 20rpx 20rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
			word-break: break-word;
			font-size: 28rpx;
			line-height: 1.5;
			position: relative;
			margin-left: auto;
			/* 确保右对齐 */
			margin-right: 0;
			animation: scaleIn 0.2s ease;

			&:after {
				content: '';
				position: absolute;
				right: -10rpx;
				top: 10rpx;
				width: 0;
				height: 0;
				border: 10rpx solid transparent;
				border-left-color: #8b5cf6;
				border-right: 0;
			}
			
			.user-text {
				margin-bottom: 16rpx;
			}
			
			.user-reference-images {
				display: flex;
				flex-wrap: wrap;
				gap: 12rpx;
				margin-top: 12rpx;
				
				.reference-image-item {
					position: relative;
					width: 120rpx;
					height: 120rpx;
					border-radius: 8rpx;
					overflow: hidden;
					cursor: pointer;
					transition: transform 0.2s ease;
					
					&:hover {
						transform: scale(1.05);
					}
					
					.reference-image {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
					
					.reference-image-overlay {
						position: absolute;
						bottom: 0;
						left: 0;
						right: 0;
						background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
						padding: 8rpx;
						
						.reference-image-text {
							color: white;
							font-size: 20rpx;
							text-align: center;
							display: block;
						}
					}
				}
			}
		}

		.ai-container {
			max-width: 90%;
			width: auto;
			display: flex;
			justify-content: flex-start;
			align-items: flex-start;
		}

		.ai-image-container {
			width: auto;
			position: relative;
			padding: 8rpx;
			background: rgba(30, 41, 59, 0.7);
			border-radius: 16rpx;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
			border: 2rpx solid rgba(148, 163, 184, 0.2);
			transition: all 0.3s ease;
			animation: scaleIn 0.3s ease;

			&:active {
				transform: scale(0.98);
			}

			// 内容操作区域
			.content-actions {
				position: absolute;
				bottom: 16rpx;
				right: 16rpx;
				display: flex;
				gap: 12rpx;
				z-index: 2;
			}

			// 添加点击提示图标
			.zoom-hint {
				background-color: rgba(0, 0, 0, 0.5);
				border-radius: 20rpx;
				padding: 6rpx 16rpx;
				display: flex;
				align-items: center;
				opacity: 0.7;
				transition: opacity 0.3s ease;

				&::before {
					content: '';
					width: 24rpx;
					height: 24rpx;
					margin-right: 6rpx;
					background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M15 3l2.3 2.3-2.89 2.87 1.42 1.42L18.7 6.7 21 9V3h-6zM3 9l2.3-2.3 2.87 2.89 1.42-1.42L6.7 5.3 9 3H3v6zm6 12l-2.3-2.3 2.89-2.87-1.42-1.42L5.3 17.3 3 15v6h6zm12-6l-2.3 2.3-2.87-2.89-1.42 1.42 2.89 2.87L15 21h6v-6z"/></svg>');
					background-size: cover;
					background-repeat: no-repeat;
				}

				.zoom-text {
					color: white;
					font-size: 20rpx;
					font-weight: 500;
				}
			}

			&:hover .zoom-hint {
				opacity: 1;
			}

			.ai-image {
				width: 375rpx;
				height: 280rpx;
				border-radius: 12rpx;
				object-fit: cover;
				transition: filter 0.3s ease;
			}
		}

		.ai-video-container {
			width: auto;
			position: relative;
			padding: 8rpx;
			background: rgba(30, 41, 59, 0.7);
			border-radius: 16rpx;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
			border: 2rpx solid rgba(148, 163, 184, 0.2);
			transition: all 0.3s ease;
			animation: scaleIn 0.3s ease;

			&:active {
				transform: scale(0.98);
			}

			// 内容操作区域
			.content-actions {
				position: absolute;
				bottom: 16rpx;
				right: 16rpx;
				display: flex;
				gap: 12rpx;
				z-index: 2;
			}

			// 添加点击提示图标
			.video-hint {
				background-color: rgba(0, 0, 0, 0.5);
				border-radius: 20rpx;
				padding: 6rpx 16rpx;
				display: flex;
				align-items: center;
				opacity: 0.7;
				transition: opacity 0.3s ease;

				&::before {
					content: '';
					width: 24rpx;
					height: 24rpx;
					margin-right: 6rpx;
					background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z"/></svg>');
					background-size: cover;
					background-repeat: no-repeat;
				}

				.video-text {
					color: white;
					font-size: 20rpx;
					font-weight: 500;
				}
			}

			&:hover .video-hint {
				opacity: 1;
			}

			.ai-video {
				width: 375rpx;
				height: 280rpx;
				border-radius: 12rpx;
				object-fit: cover;
				transition: filter 0.3s ease;
			}
		}



		.ai-markdown-container {
			width: 100%;
			padding: 20rpx;
			background: rgba(30, 41, 59, 0.7);
			border-radius: 16rpx;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
			border: 2rpx solid rgba(148, 163, 184, 0.2);
			color: #FFF;
			font-size: 28rpx;
			line-height: 1.6;
			animation: scaleIn 0.3s ease;

			// Markdown操作区域
			.markdown-actions {
				display: flex;
				justify-content: flex-end;
				margin-top: 16rpx;
				padding-top: 16rpx;
				border-top: 1px solid rgba(255, 255, 255, 0.1);

				
			}
		}
	}
.action-btn {
					padding: 8rpx 20rpx;
					font-size: 24rpx;
					border-radius: 30rpx;
					background: rgba(99, 102, 241, 0.3);
					color: rgba(255, 255, 255, 0.9);
					transition: all 0.2s ease;

					&:active {
						transform: scale(0.95);
					}

					&.copy-btn {
						display: flex;
						align-items: center;

						&::before {
							content: '';
							width: 24rpx;
							height: 24rpx;
							margin-right: 6rpx;
							background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/></svg>');
							background-size: cover;
							background-repeat: no-repeat;
						}

						&:active {
							background: rgba(99, 102, 241, 0.5);
						}
					}

					&.favorite-btn {
						display: flex;
						align-items: center;
						margin-left: 12rpx;

						&::before {
							content: '';
							width: 24rpx;
							height: 24rpx;
							margin-right: 6rpx;
							background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>');
							background-size: cover;
							background-repeat: no-repeat;
						}
					}
				}
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(10rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes scaleIn {
		from {
			opacity: 0;
			transform: scale(0.9);
		}

		to {
			opacity: 1;
			transform: scale(1);
		}
	}
</style>