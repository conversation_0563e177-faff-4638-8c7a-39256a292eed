<template>
	<view>

		<view class="bg" :style="{'background-image': 'url('+imgUrl+'405.png'+')'}">

			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_30rpx" @click="navig()">
					爆款仿拍
				</view>
			</view>

			<view class="list-public">
				<view class="display-a se-top">
					<view @click="getType(1)" style="border-radius: 20rpx 20rpx 20rpx 0;"
						:class="type == 1 ? 'type-1' : 'type-2'">网络提取</view>
					<view @click="getType(2)" style="border-radius: 20rpx 20rpx 0 20rpx;"
						:class="type == 2 ? 'type-1' : 'type-2'">本地提取</view>
				</view>

				<view :class="type == 1 ? 'se-frame' : 'se-frame-2'">
					<view class="t-area">
						<textarea v-if="textMsg" v-model="textMsg" placeholder="请输入" :cursor-spacing="50" maxlength="-1"
							placeholder-class="#BDBDBD"></textarea>
						<textarea v-else v-model="videoUrl"
							:placeholder="type == 1 ? '复制D音视频分享链接（注明：视频要带背景音乐和原声不然失败不退点数）' : '请复制网络视频地址或点击下方上传视频' "
							:cursor-spacing="50" maxlength="-1" placeholder-class="#BDBDBD"></textarea>
						<view class="display-a margin-bottom_20rpx" v-if="textMsg">
							<view class="msg-tips">{{textMsg.length}}/500字</view>
						</view>
						<view class="display-a" style="color: #EBEAEA;" v-if="textMsg">
							<view class="display-a margin-right_40rpx" @click="getAccountInfo(3)">
								<image class="img-409" :src="imgUrl+'410.png'"></image>
								<view class="font-size_26rpx">一键仿写</view>
							</view>
							<!-- <view class="display-a">
								<image class="img-409" :src="imgUrl+'411.png'"></image>
								<view class="font-size_26rpx">AI翻译</view>
							</view> -->
							<view class="clear-clip" @click="getClear()">清空</view>
						</view>
					</view>
				</view>
				<view @click="openRequirement()" class="img-431 display-a"
					:style="{'background-image': 'url('+imgUrl+'431.png'+')'}" v-if="type == 2">
					<image class="img-420" :src="imgUrl+'432.png'"></image>
					<view>
						<view class="color_12E3A2 margin-bottom_10rpx">点击上传视频</view>
						<view class="color_FFFFFF font-size_26rpx">支持MP4/MOV格式,时长建议小于60秒</view>
					</view>
				</view>

			</view>

			<view class="display-a padding_20rpx">
				<block v-if="systems.copywriting_require">
					<view class="se-line"></view>
					<view class="color_FFFFFF font-size_32rpx">文案要求</view>
				</block>
				<image @click="getRecord()" class="img-419" :src="imgUrl+'419.png'"></image>
				<view @click="getRecord()" style="color: rgba(32, 255, 134, 1)">生成记录</view>
			</view>
			<view class="padding_0_20rpx color_FFFFFF" v-if="systems.copywriting_require">
				<rich-parser :html="systems.copywriting_require"
					domain="https://6874-html-foe72-**********.tcb.qcloud.la" lazy-load ref="article" selectable
					show-with-animation use-anchor>
					<!-- 加载中... -->
				</rich-parser>
			</view>

			<view style="height: 160rpx;"></view>

		</view>

		<view class="pos-bott">

			<view class="but-next" v-if="textMsg" @click="getClip()">去创作</view>
			<view class="but-next" v-else @click="getAccountInfo(1)">
				解析视频文案
				<!-- <span class="margin-left_16rpx">
					{{tallySetObj.ai_video_extraction}}点/1次
				</span> -->
			</view>

		</view>

		<sunui-popup ref="pop4">
			<template v-slot:content>
				<view class="pop-bg">
					<image @click="closeRequirement()" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title">上传视频</view>

					<view class="pop-frame display-ac-jc" v-if="videoUrl">
						<video style="width: 700rpx;height: 518rpx;" :src="videoUrl"></video>
					</view>
					<view class="pop-frame display-ac-jc" @click="addVideoImg()" v-else>
						<image class="img-433" :src="imgUrl+'433.png'"></image>
						<view class="color_12E3A2 margin-bottom_10rpx">点击上传视频</view>
						<view class="color_FFFFFF font-size_26rpx">支持MP4/MOV格式,时长建议小于60秒</view>
					</view>


					<view class="display-a-js">
						<view class="but-again" @click="addVideoImg()">重新上传</view>
						<view class="but-next" style="width: 410rpx;color: #000;" @click="getAccountInfo(2)">
							解析视频文案
							<!-- <span class="margin-left_16rpx">
								{{tallySetObj.ai_video_extraction}}点/1次
							</span> -->
						</view>
					</view>

				</view>
			</template>
		</sunui-popup>


	</view>
</template>

<script>
	import {
		decodedString
	} from '../../../subPackages/subPackageA/utils/decodedString';
	const base64 = require('@/utils/ali-oos/base64.js'); //Base64,hmac,sha1,crypto相关算法
	require('@/utils/ali-oos/hmac.js');
	require('@/utils/ali-oos/sha1.js');
	const Crypto = require('@/utils/ali-oos/crypto.js');
	export default {
		data() {
			return {

				imgUrl: this.$imgUrl,

				heightSystemss: '',
				statusBarHeightss: '',
				windowHeight: '',

				type: '1', // 1网络提取 2本地上传
				appType: '', //

				videoUrl: '', //视频链接
				textMsg: '', //文案

				isWhether: true, //判断重复点击

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置
				systems: uni.getStorageSync("system"), //系统设置

				upPicUrl2: '',

				progress: 0, //上传视频进度条

				formData: {
					'key': '',
					'policy': '',
					'OSSAccessKeyId': '',
					'signature': '',
					'success_action_status': '200',
				},

				policyText: {
					"expiration": "2030-01-01T12:00:00.000Z", //设置该Policy的失效时间，超过这个失效时间之后，就没有办法通过这个policy上传文件了
					"conditions": [
						["content-length-range", 0, 524288000] // 设置上传文件的大小限制  104857600 209715200
					]
				},

				requestTask: null
			}
		},

		onLoad(options) {
			// 接收传递的type参数
			if (options.type) {
				this.type = options.type;
			}
			this.getSystemInfo();
			this.getAliyunConfig();
		},
		onUnload() {
			this.stopStream();
		},
		onShow() {

		},

		methods: {

			//视频/图片上传
			addVideoImg() {

				// 验证后端
				if (!this.upPicUrl2) {
					uni.showToast({
						title: '请配置阿里云',
						icon: 'none'
					});
					return;
				}

				uni.chooseVideo({
					count: 1,
					compressed: false,
					sourceType: ['album'],
					success: res => {
						let file = res.tempFilePath;
						let suffix = 'mp4';
						if (res.tempFilePath) {
							suffix = res.tempFilePath.split(".");
						} else {
							this.$sun.toast("视频资源异常,请重新选择!", 'none');
							return;
						}
						let maxSize = 500;

						if (res.size / 1024 / 1024 > maxSize) {
							this.$sun.toast(`视频不能超过${maxSize}M`, 'none');
							return;
						}

						if (res.duration > 60) {
							this.$sun.toast('请上传视频时长小于60秒!', 'none');
							return;
						}

						this.uploadBaseVideo(file, suffix[suffix.length - 1]);

					},
					complete: function() {

					},
					fail: function(err) {
						console.log("uni.chooseVideo err---->", err);
					}
				});

			},

			//上传视频
			uploadBaseVideo(file, suffix) {

				this.video_url = '';

				// 设置一个变量来存储数字
				let count = 0;
				// 设置一个变量来存储定时器
				let timer = null;

				uni.showLoading({
					title: '上传中...' + count + '%',
					mask: true
				});

				// 设置定时器更新数字
				timer = setInterval(() => {
					// count = (count + 1) % 100; // 这里模拟0-99的变化
					uni.showLoading({
						title: `上传中... ${count}%`, // 使用字符串模板并格式化
					});
				}, 300); // 每0.1秒更新一次

				this.formData.key = new Date().getTime() + Math.floor(Math.random() * 150) + '.' + suffix;

				console.log("this.formData.key---->", this.formData.key);

				// 创建上传对象
				const task = uni.uploadFile({
					url: this.upPicUrl2,
					filePath: file,
					fileType: 'video/mp4',
					name: 'file',
					formData: this.formData,
					header: {},
					success: uploadRes => {

						console.log('uploadRes', uploadRes);

						if (uploadRes.statusCode != 200) {
							uni.showToast({
								title: '上传失败 : ' + uploadRes.data,
								icon: 'none'
							});
							clearInterval(timer);
							uni.hideLoading();
						} else {
							count = 100;
							this.videoUrl = this.upPicUrl2 + '/' + this.formData.key;
							clearInterval(timer);
							uni.hideLoading();
						}
					},
					fail: e => {
						uni.showToast({
							title: '上传失败,' + e,
							icon: 'none'
						});
						clearInterval(timer);
						uni.hideLoading();
					}
				});
				task.onProgressUpdate(res => {
					if (res.progress > 0 && count < 100) {
						count = res.progress;
					}
				});
			},

			/*  阿里云设置  */
			async getAliyunConfig() {
				const result = await this.$http.post({
					url: this.$api.aliyunConfig
				});
				if (result.errno == 0) {
					this.upPicUrl2 = 'https://' + result.data.alioss_domain;
					this.formData.OSSAccessKeyId = result.data.alioss_access_key_id;

					this.formData.policy = base64.encode(JSON.stringify(this.policyText));
					let message = this.formData.policy;
					let bytes = Crypto.HMAC(Crypto.SHA1, message, result.data.alioss_access_key_secret, {
						asBytes: true
					});
					this.formData.signature = Crypto.util.bytesToBase64(bytes);
				}
			},

			openRequirement() {
				this.$refs.pop4.show({
					style: 'background-color:#000;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeRequirement() {
				this.$refs.pop4.close();
			},

			//生成记录
			getRecord() {
				uni.navigateTo({
					url: '/pages/index/selling/record'
				})
			},

			getClip() {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中，请稍后再试", 'none');
					return;
				}
				if (!this.textMsg) {
					this.$sun.toast("请输入文案", 'none');
					return;
				}

				if (this.textMsg.length > 500) {
					this.$sun.toast("请控制文案在500字以内!", 'none');
					return;
				}

				uni.setStorageSync("answer", this.textMsg);

				uni.navigateTo({
					url: '/pages/index/clip/clip?answerType=1'
				})
			},

			//查询点数是否足够
			async getAccountInfo(type) {

				if (type == 1) {
					if (!this.videoUrl) {
						this.$sun.toast("请输入D音分享链接", 'none');
						return;
					}
				}

				if (type == 2) {
					if (!this.videoUrl) {
						this.$sun.toast("请输入网络视频地址或者上传视频", 'none');
						return;
					}
				}


				if (uni.getStorageSync('uid')) {

					if (!this.isWhether) {
						return;
					}
					this.isWhether = false;

					const result = await this.$http.post({
						url: this.$api.accountInfo,
						data: {
							type: 2, //2视频提取 1文案仿写
							uid: uni.getStorageSync('uid'),
						}
					});
					if (result.errno == 0) {
						if (type == 3) {
							this.startStream();
						} else {
							this.getCopyGeneration(type);
						}
					} else {
						this.isWhether = true;
						if (result.errno == -2) {
							uni.showModal({
								content: result.message,
								cancelText: "取消",
								confirmText: "去开通",
								success: (res) => {
									if (res.confirm) {
										uni.navigateTo({
											url: '/pages/my/member'
										})
									} else if (res.cancel) {

									}
								}
							})
						} else {
							this.$sun.toast(result.message, 'none');
						}
					}

				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "返回",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.redirectTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {

							}
						}
					})
				}

			},

			async addGenerateRecord(content) {
				const result = await this.$http.post({
					url: this.$api.addGenerateRecord,
					data: {
						uid: uni.getStorageSync('uid'),
						content
					},
					mask: true,
					title: '保存中...'
				});
				if (result.errno == 0) {
					this.$sun.toast("文案保存成功");
					setTimeout(() => {
						this.videoUrl = '';
						this.textMsg = content;
						this.isWhether = true;
					}, 2000);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//视频提取
			async getCopyGeneration(type) {
				if (type === 1 && this.type == 1) {
					const result = await this.$http.post({
						url: 'https://te.92k.fun/user/analysis',
						data: {
							key: 'hf_7e1003028241ea9af5d7b748120b8a471000',
							url: this.videoUrl,
						},
						mask: true,
						title: '请求中...'
					});
					if (result.code == 200 && result?.transcripts[0].text) {
						this.$sun.toast("文案提取成功");
						this.addGenerateRecord(result.transcripts[0].text)

					} else {
						this.isWhether = true;
						this.$sun.toast(result.msg, 'none');
					}
				} else {
					const result = await this.$http.post({
						url: type == 1 ? this.$api.platformCopywriting : this.$api.localVideo,
						data: {
							question: this.videoUrl,
							uid: uni.getStorageSync('uid'),
						},
						mask: true,
						title: '请求中...'
					});
					if (result.errno == 0) {
						this.$sun.toast("文案提取成功");
						setTimeout(() => {
							this.videoUrl = '';
							this.textMsg = result.data.answer;
							this.isWhether = true;
						}, 2000);
					} else {
						this.isWhether = true;
						this.$sun.toast(result.msg, 'none');
					}
				}

			},

			// 中止请求
			stopStream() {
				if (this.requestTask) {
					this.requestTask.cancel();
					this.requestTask = null;
					console.log('已中止请求');
				}
			},
			// 一键仿写流式请求
			startStream() {
				if (!this.textMsg) {
					this.$sun.toast("请先输入文案", 'error');
					return;
				}

				this.isWhether = false

				uni.showLoading({
					title: '正在生成'
				})
				let url = this.$api.rewriteText

				let data = {
					uid: uni.getStorageSync('uid'), // 请求参数
					content: this.textMsg
				}
				this.textMsg = ''
				// 使用流式请求
				this.requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						uni.hideLoading()
						// 处理接收到的数据块
						try {

							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}

								// 确保换行符被保留（不进行额外处理，由computed属性处理格式化）
								this.textMsg += text;
							} else if (typeof data === 'string') {
								// 如果没有结束标记，直接追加
								this.textMsg += data;
							} else {
								console.warn("收到未知类型的数据块:", data);
							}
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						uni.hideLoading()
						uni.setStorageSync("answer", this.textMsg);
						this.isWhether = true
						console.log('文案生成完成');
					},
					onError: (err) => {
						uni.hideLoading()
						console.error("流式请求错误:", err);
					}
				});
			},

			//一键仿写
			async getCopyImitation() {
				const result = await this.$http.post({
					url: 'https://vapi.weijuyunke.com/api/doBao/copyImitation',
					data: {
						content: '请根据【' + this.textMsg + '】热点内容写1条长度480字以内的文案，不要附加话题，只生成纯文案。',
						ipStatus: 2,
						domainName: 'vapi.weijuyunke.com',
						requestIp: '**************'
					},
					mask: true,
					title: '请求中...'
				});
				if (result.code == 200) {
					this.getAICreation(result.data);
				} else {
					this.isWhether = true;
					this.$sun.toast(result.msg, 'none');
				}
			},

			//生成AI文案
			async getAICreation(answer) {

				const result = await this.$http.post({
					url: this.$api.aiCreateAdd,
					data: {
						uid: uni.getStorageSync('uid'),
						name: "爆款跟拍一键仿写",
						type: 17,
						question: this.textMsg, //拼接的文本
						answer: answer, // 接口返回的文本
						words: answer.length
					}
				});
				if (result.errno == 0) {
					this.$sun.toast("操作成功");
					setTimeout(() => {
						this.textMsg = answer;
						uni.setStorageSync("answer", answer);
						this.isWhether = true;
					}, 1000);
				} else {
					this.isWhether = true;
					if (result.errno == -2) {
						uni.showModal({
							content: result.message,
							cancelText: "取消",
							confirmText: "去开通",
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/my/member'
									})
								} else if (res.cancel) {

								}
							}
						})
					} else {
						this.$sun.toast(result.message, 'none');
					}
				}

			},

			getClear() {
				if (!this.isWhether) {
					this.$sun.toast("正在生成中，请稍后再试", 'none');
					return;
				}
				this.textMsg = '';
			},

			getType(type) {
				this.type = type;
				this.textMsg = '';
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
						this.windowHeight = res.windowHeight * 2;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	.img-433 {
		width: 70rpx;
		height: 70rpx;
		margin-bottom: 20rpx;
	}

	.pop-frame {
		width: 710rpx;
		height: 520rpx;
		border: 1px dashed rgba(32, 255, 134, 1);
		border-radius: 10rpx;
		margin: 40rpx 0;
	}

	.but-again {
		width: 280rpx;
		text-align: center;
		background-color: #FFF;
		padding: 30rpx 0;
		font-size: 32rpx;
		font-weight: 600;
		color: #000;
		border-radius: 10rpx;
	}

	.p-title {
		color: #FFF;
		font-size: 36rpx;
		font-weight: 600;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 42rpx;
		right: 30rpx;
	}

	.pop-bg {
		position: relative;
		padding: 34rpx 20rpx;
		color: #FFF;
	}

	.img-420 {
		width: 66rpx;
		height: 66rpx;
		margin-right: 14rpx;
	}

	.img-431 {
		width: 710rpx;
		height: 133rpx;
		background-repeat: no-repeat;
		background-size: cover;
		border-radius: 0 0 20rpx 20rpx;
		padding: 0 20rpx;
	}

	.msg-tips {
		// text-align: right;
		// padding-right: 20rpx;
		// padding-bottom: 20rpx;
		// margin-bottom: 20rpx;
		margin-left: auto;
		font-size: 24rpx;
		color: #C9CACA;
	}

	.t-area {
		width: 682rpx;
		padding: 20rpx;
		border-radius: 20rpx;
		background: rgba(28, 28, 28, 1);
	}

	.clear-clip {
		width: 90rpx;
		text-align: center;
		background-color: #2D2E33;
		border-radius: 10rpx;
		margin-left: auto;
		color: #EBEAEA;
		font-size: 24rpx;
		padding: 8rpx 0;
	}

	.img-409 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
	}


	.previous-step {
		width: 260rpx;
		text-align: center;
		background-color: #FFF;
		padding: 30rpx 0;
		font-size: 32rpx;
		font-weight: 600;
		color: #000;
		border-radius: 10rpx;
	}

	.but-next {
		width: 710rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgba(89, 238, 82, 1), rgba(130, 255, 242, 1) 100%);
		padding: 30rpx 0;
		font-size: 32rpx;
		font-weight: 600;
	}

	.pos-bott {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		padding: 20rpx 20rpx 30rpx;
		z-index: 99;
		background: rgba(48, 48, 48, 1);
	}

	.img-419 {
		width: 36rpx;
		height: 36rpx;
		margin-left: auto;
		margin-right: 8rpx;
	}

	.se-line {
		background: rgba(32, 255, 134, 1);
		width: 8rpx;
		height: 28rpx;
		border-radius: 4rpx;
		margin-right: 8rpx;
	}

	.se-top {
		border-radius: 20rpx 20rpx 0 0;
		background: linear-gradient(180.00deg, rgba(32, 255, 134, 1), rgba(23, 23, 23, 1) 100%);
	}

	textarea {
		width: 642rpx;
		height: 560rpx;
		color: #FFF;
	}

	.se-frame-2 {
		width: 710rpx;
		background: rgba(23, 23, 23, 1);
		padding: 20rpx 14rpx 12rpx;
		border-radius: 0;
	}

	.se-frame {
		width: 710rpx;
		background: rgba(23, 23, 23, 1);
		padding: 20rpx 14rpx 30rpx;
		border-radius: 0 0 20rpx 20rpx;
	}

	.type-2 {
		width: 376rpx;
		text-align: center;
		background: rgba(23, 23, 23, 1);
		color: #FFF;
		padding: 38rpx 0 30rpx;
		font-size: 30rpx;
		font-weight: 600;
	}

	.type-1 {
		width: 374rpx;
		text-align: center;
		background: linear-gradient(180.00deg, rgba(32, 255, 134, 1), rgba(0, 255, 229, 1) 100%);
		color: rgba(0, 0, 0, 1);
		padding: 38rpx 0 30rpx;
		font-size: 30rpx;
		font-weight: 600;
	}

	.list-public {
		background: rgba(23, 23, 23, 1);
		padding: 0;
		border-radius: 20rpx;
		margin-bottom: 10rpx;
	}

	.bg {
		width: 750rpx;
		height: 580rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 200rpx 0 0;
	}

	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		display: flex;
		align-items: center;
	}

	page {
		border: none;
		background: rgba(48, 48, 48, 1);
		width: 100%;
		overflow-x: hidden !important;
	}
</style>