uni-toast {
	z-index: 9999999 !important;
}

uni-modal {
	z-index: 9999999 !important;
}

page {
	background-color: #F7F7F7;
	color: #000000;
	font-size: 28rpx;
	border-top: 1px solid #F7F7F7;
}

textarea {
	font-size: 28rpx;
}

input {
	font-size: 28rpx;
}

.h_20rpx {
	height: 20rpx;
}

.h_10rpx {
	height: 10rpx;
}

.h_30rpx {
	height: 30rpx;
}

.width_374rpx-center {
	width: 374rpx;
	text-align: center;
}

.width_250rpx-center {
	width: 250rpx;
	text-align: center;
}

.width_170rpx-center {
	width: 170rpx;
	text-align: center;
}

.width_176rpx-center {
	width: 176rpx;
	text-align: center;
}

.width_150rpx-center {
	width: 150rpx;
	text-align: center;
}

.width_125rpx-center {
	width: 125rpx;
	text-align: center;
}

.width_230rpx-center {
	width: 230rpx;
	text-align: center;
}

.width_236rpx-center {
	width: 236rpx;
	text-align: center;
}

.width_355rpx-center {
	width: 355rpx;
	text-align: center;
}

.width_186rpx-center {
	width: 186rpx;
	text-align: center;
}

.list-public {
	width: 710rpx;
	margin: 0 20rpx 20rpx;
	background-color: #FFFFFF;
	border-radius: 10rpx;
	padding: 20rpx;
}

/* 垂直居中 */
.cell {
	display: table-cell;
	vertical-align: middle;
}

/*边框阴影*/
.Border_shadow {
	border-bottom: 2rpx solid #E5E5E5;
	box-shadow: #E5E5E5 0rpx 10rpx 20rpx;
}

/* 文字图片对齐 */
.setting {
	display: flex;
	flex-direction: row;
	align-items: center;
	/* justify-content: center; */
}

/* 居中 */
.justify-content_center {
	justify-content: center;
}

/* 文字删除线 */
.text-decoration_line-through {
	text-decoration: line-through;
}

.content {
	width: 750rpx;
	height: auto;
	background-color: #FFFFFF;
}

.blank {
	width: 750rpx;
	height: 20rpx;
	background-color: #F7F7F7;
}

.blank5 {
	width: 750rpx;
	height: 10rpx;
	background-color: #F7F7F7;
}

.p-bo {
	border-bottom: 2rpx solid #EEEEEF;
}

.sunui-pop-panel {
	z-index: 999 !important;
}

.placeholder {
	color: #C9CACA;
	font-size: 28rpx;
}

.placeholder2 {
	color: #C9CACA;
	font-size: 32rpx;
}

.placeholder3 {
	color: #FF8343;
	font-size: 28rpx;
}

/* 5行文字省略 */
.font-overflow5 {
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 5;
	overflow: hidden;
	-webkit-box-orient: vertical;
}

/* 5行文字省略 */
.font-overflow6 {
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 6;
	overflow: hidden;
	-webkit-box-orient: vertical;
}

/* 5行文字省略 */
.font-overflow8 {
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 8;
	overflow: hidden;
	-webkit-box-orient: vertical;
}

/* 3行文字省略 */
.font-overflow3 {
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	overflow: hidden;
	-webkit-box-orient: vertical;
}

/* 2行文字省略 */
.font-overflow2 {
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	overflow: hidden;
	-webkit-box-orient: vertical;
}

/* 单行文字省略 */
.font-overflow {
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

/*  display {  */

.display {
	display: flex;
}

.display-a {
	display: flex;
	align-items: center;
}

.display-js {
	display: flex;
	justify-content: space-between;
}

.display-a-js {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.display-fw-js {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.display-a-jc {
	display: flex;
	align-items: center;
	justify-content: center;
}

.display-ac-jc {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.display-a-jc-fw {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
}

.display-f-js {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.display-f-jc {
	display: flex;
	flex-direction: column;
	/* 如果你想要垂直排列 */
	justify-content: flex-end;
	/* 将子元素对齐到容器的底部 */
}

.display-mt {
	display: flex;
	margin-top: auto;
}

.display-fw-a {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}

.display-fw-a-ib {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	display: inline-block;
}

/*   字体   */
.font-weight {
	font-weight: bold;
}
/* 右边尖括号 */
.arrow {
	width: 6px;
	height: 6px;
	border-top: 1px solid #999;
	border-right: 1px solid #999;
	transform: rotate(45deg);
	position: absolute;
	right: 0px;
}

/* 禁止点击 */
.pointer-events_none {
	pointer-events: none;
}

/* 允许点击 */
.pointer-events_auto {
	pointer-events: auto;
}

.tabbar_sty {
	color: #FF147A;
}

.border-radius_50 {
	border-radius: 50%;
}

.display-none {
	display: none;
}

.display-block {
	display: block;
}

.display-flex {
	display: flex;
}

.flex-wrap_wrap {
	flex-wrap: wrap;
}

.width_100 {
	width: 100%;
}

.width_50 {
	width: 50%;
}

.width_33 {
	width: 33.3%;
}

.width_25 {
	width: 25%;
}

.height_100 {
	height: 100%;
}

.width_540rpx {
	width: 540rpx;
}

.flex_1 {
	flex: 1;
}

.flex_2 {
	flex: 2;
}

.flex_3 {
	flex: 3;
}

.flex_4 {
	flex: 4;
}

.flex_5 {
	flex: 5;
}

.flex_6 {
	flex: 6;
}

.flex_7 {
	flex: 7;
}

.flex_8 {
	flex: 8;
}

.flex_9 {
	flex: 9;
}

.border-bottom-f7f7f7 {
	border-bottom: 2rpx solid #f7f7f7;
}

/* 字体位置 */
.text-align_center {
	text-align: center;
}

.text-align_left {
	text-align: left;
}

.text-align_right {
	text-align: right;
}

/* text 居右 */
.justify-content_flex-end {
	justify-content: flex-end
}

/* 颜色 */

.color_49BDF6 {
	color: #49BDF6;
}

.color_0084ff {
	color: #0084ff;
}


.color_E0FF1E {
	color: #E0FF1E;
}

.color_20FF86 {
	color: #20FF86;
}

.color_AFAFAF {
	color: #AFAFAF;
}

.color_12E3A2 {
	color: #12E3A2;
}

.color_A8A8A8 {
	color: #a8a8a8;
}

.color_3AFA9A {
	color: #3AFA9A;
}

.color_E8E8E8 {
	color: #E8E8E8;
}

.color_00FFCA {
	color: #00FFCA;
}

.color_A0A0A1 {
	color: #A0A0A1;
}

.color_28C07D {
	color: #28C07D;
}

.color_E1E1E1 {
	color: #E1E1E1;
}

.color_C3C2C2 {
	color: #C3C2C2;
}

.color_B7B7B7 {
	color: #B7B7B7;
}

.color_08BB71 {
	color: #08BB71;
}

.color_BA76F8 {
	color: #BA76F8;
}

.color_9F57F7 {
	color: #9F57F7;
}

.color_515151 {
	color: #515151;
}

.color_c7c7c7 {
	color: #c7c7c7;
}

.color_D5D5D5 {
	color: #d5d5d5;
}

.color_979797 {
	color: #979797;
}

.color_00CE55 {
	color: #00CE55;
}

.color_39AC6A {
	color: #39AC6A;
}

.color_D3DBE7 {
	color: #D3DBE7;
}

.color_FF3EC9 {
	color: #FF3EC9;
}

.color_16A9FA {
	color: #16A9FA;
}

.color_FF4E3D {
	color: #FF4E3D;
}

.color_FF4E3D {
	color: #FF4E3D;
}

.color_9E6205 {
	color: #9E6205;
}

.color_B39C7A {
	color: #B39C7A;
}

.color_FF263B {
	color: #FF263B;
}

.color_1C6EFF {
	color: #1C6EFF;
}

.color_FFA835 {
	color: #FFA835;
}

.color_1972EC {
	color: #1972EC;
}

.color_FF5B5B {
	color: #FF5B5B;
}

.color_12CDC2 {
	color: #12CDC2;
}

.color_289889 {
	color: #289889;
}

.color_FFA835 {
	color: #FFA835;
}

.color_09C680 {
	color: #09c680;
}

.color_333333 {
	color: #333333;
}

.color_757575 {
	color: #757575;
}

.color_FFFFFF {
	color: #FFFFFF;
}

.color_FDECAE {
	color: #FDECAE;
}

.color_5E8BB0 {
	color: #5E8BB0;
}

.color_FFDC56 {
	color: #FFDC56;
}

.color_000000 {
	color: #000000 !important;
}

.color_B89200 {
	color: #B89200;
}

.color_A1A1A1 {
	color: #A1A1A1;
}

.color_93B5FF {
	color: #93B5FF;
}

.color_4FFF76 {
	color: #4FFF76;
}

.color_C4C4C4 {
	color: #C4C4C4;
}

.color_A3A3A3 {
	color: #A3A3A3;
}

.color_FF9D01 {
	color: #FF9D01;
}

.color_5F5D5D {
	color: #5F5D5D;
}

.color_FF3B3B {
	color: #FF3B3B;
}

.color_07C160 {
	color: #07C160;
}

.color_00C25E {
	color: #00C25E;
}

.color_09C17D {
	color: #09C17D;
}

.color_FE8A47 {
	color: #FE8A47;
}

.color_FF0000 {
	color: #FF0000;
}

.color_C9CACA {
	color: #C9CACA;
}

.color_666666 {
	color: #666666;
}

.color_FFC100 {
	color: #FFC100;
}

.color_30F99F {
	color: #30F99F;
}

.color_00DCFF {
	color: #00DCFF;
}

.color_c6c6c6 {
	color: #c6c6c6;
}

.color_0E8FF3 {
	color: #0E8FF3;
}

.color_656565 {
	color: #656565;
}

.color_9F6DFA {
	color: #9F6DFA;
}

.color_FF3456 {
	color: #FF3456;
}

.color_FF8830 {
	color: #FF8830;
}

.color_FEBA27 {
	color: #FEBA27;
}

.color_707070 {
	color: #707070;
}

.color_959595 {
	color: #959595;
}

.color_7c7c7c {
	color: #7c7c7c;
}

.color_FF8000 {
	color: #FF8000;
}

.color_81A1F7 {
	color: #81A1F7;
}

.color_8b8b8b {
	color: #8b8b8b;
}

.color_FEC431 {
	color: #FEC431;
}

.color_929292 {
	color: #929292;
}

.color_FF541C {
	color: #FF541C;
}

.color_FFDE3D {
	color: #FFDE3D;
}

.color_999999 {
	color: #999999;
}

.color_5EDE4F {
	color: #5EDE4F;
}

.color_FFBD3E {
	color: #FFBD3E;
}

.color_E53A3A {
	color: #E53A3A;
}


/* 背景颜色 */
.background-color_F7F7F7 {
	background-color: #F7F7F7;
}

.background-color_FFFFFF {
	background-color: #FFFFFF;
}

.background-color_EEEEEF {
	background-color: #EEEEEF;
}

.background-color_E9E9E9 {
	background-color: #E9E9E9;
}

/* 字体大小 */
.font-size_70rpx {
	font-size: 70rpx;
}

.font-size_56rpx {
	font-size: 56rpx;
}

.font-size_50rpx {
	font-size: 50rpx;
}

.font-size_48rpx {
	font-size: 48rpx;
}

.font-size_44rpx {
	font-size: 44rpx;
}

.font-size_40rpx {
	font-size: 40rpx;
}

.font-size_38rpx {
	font-size: 38rpx;
}

.font-size_36rpx {
	font-size: 36rpx;
}

.font-size_32rpx {
	font-size: 32rpx;
}

.font-size_30rpx {
	font-size: 30rpx;
}

.font-size_28rpx {
	font-size: 28rpx;
}

.font-size_26rpx {
	font-size: 26rpx;
}

.font-size_24rpx {
	font-size: 24rpx;
}

.font-size_22rpx {
	font-size: 22rpx;
}

.font-size_18rpx {
	font-size: 18rpx;
}

.font-size_20rpx {
	font-size: 20rpx;
}

.font-size_16rpx {
	font-size: 16rpx;
}

.font-size_12rpx {
	font-size: 12rpx;
}


/* 加粗 */
.font-weight_bold {
	font-weight: bold;
}

/* margin */
.margin_20rpx {
	margin: 20rpx;
}

.margin_10rpx {
	margin: 10rpx;
}

.margin_14rpx {
	margin: 14rpx;
}

.margin_28rpx {
	margin: 28rpx;
}

.margin_30rpx {
	margin: 30rpx;
}

.margin_0_6rpx {
	margin: 0 6rpx;
}

.margin_0_10rpx {
	margin: 0 10rpx;
}

.margin_0_14rpx {
	margin: 0 14rpx;
}

.margin_30rpx_22rpx {
	margin: 30rpx 22rpx;
}

.margin_50rpx_40rpx {
	margin: 50rpx 40rpx;
}

.margin_58rpx_20rpx_0 {
	margin: 58rpx 20rpx 0;
}

.margin_100rpx_0 {
	margin: 100rpx 0;
}

.margin_40rpx_0 {
	margin: 40rpx 0;
}

.margin_20rpx_0 {
	margin: 20rpx 0;
}

.margin_25rpx_0 {
	margin: 25rpx 0;
}

.margin_6rpx_0 {
	margin: 6rpx 0;
}

.margin_2rpx_0 {
	margin: 2rpx 0;
}

.margin_0_4rpx {
	margin: 0 4rpx;
}

.margin_10rpx_0 {
	margin: 10rpx 0;
}

.margin_0_30rpx {
	margin: 0 30rpx;
}

.margin_0_20rpx {
	margin: 0 20rpx;
}

.margin_20rpx_10rpx {
	margin: 20rpx 10rpx;
}

.margin_22rpx_0_0_22rpx {
	margin: 22rpx 0 0 22rpx;
}

.margin-left-auto {
	margin-left: auto;
}

.margin-left_10rpx {
	margin-left: 10rpx;
}

.margin-left_16rpx {
	margin-left: 16rpx;
}

.margin-left_20rpx {
	margin-left: 20rpx;
}

.margin-left_30rpx {
	margin-left: 30rpx;
}

.margin-left_38rpx {
	margin-left: 38rpx;
}


.margin-right_30rpx {
	margin-right: 30rpx;
}

.margin-right_40rpx {
	margin-right: 40rpx;
}

.margin-right_16rpx {
	margin-right: 16rpx;
}

.margin-right_20rpx {
	margin-right: 20rpx;
}


.margin-top_16rpx {
	margin-top: 16rpx;
}

.margin-top_20rpx {
	margin-top: 20rpx;
}

.margin-top_30rpx {
	margin-top: 30rpx;
}

.margin-top_60rpx {
	margin-top: 60rpx;
}

.margin-top_100rpx {
	margin-top: 160rpx;
}

.margin_auto {
	margin: auto;
}

.margin-top_10rpx {
	margin-top: 10rpx;
}

.margin-bottom_4rpx {
	margin-bottom: 4rpx;
}

.margin-bottom_10rpx {
	margin-bottom: 10rpx;
}

.margin-bottom_20rpx {
	margin-bottom: 20rpx;
}

.margin-bottom_30rpx {
	margin-bottom: 30rpx;
}

.margin-bottom_40rpx {
	margin-bottom: 40rpx;
}

.margin-bottom_50rpx {
	margin-bottom: 50rpx;
}

.margin-bottom_60rpx {
	margin-bottom: 60rpx;
}

/* padding */

.padding-top_20rpx {
	padding-top: 20rpx;
}

.padding-top_30rpx {
	padding-top: 30rpx;
}

.padding-top_10rpx_30rpx {
	padding: 10rpx 0 30rpx;
}

.padding-bottom_10rpx {
	padding-bottom: 10rpx;
}

.padding-bottom_20rpx {
	padding-bottom: 20rpx;
}

.padding-bottom_30rpx {
	padding-bottom: 30rpx;
}

.padding_10rpx {
	padding: 10rpx;
}

.padding_18rpx {
	padding: 18rpx;
}

.padding_20rpx {
	padding: 20rpx;
}

.padding_22rpx {
	padding: 22rpx;
}

.padding_26rpx {
	padding: 26rpx;
}

.padding_30rpx {
	padding: 30rpx;
}

.padding_48rpx {
	padding: 48rpx;
}

.padding_top_32rpx {
	padding-top: 32rpx;
}

.padding-left_30rpx {
	padding-left: 30rpx;
}

.padding-right_10rpx {
	padding-right: 10rpx;
}

.padding-right_20rpx {
	padding-right: 20rpx;
}

.padding-right_24rpx {
	padding-right: 24rpx;
}

.padding_30rpx_0 {
	padding: 30rpx 0;
}

.padding_40rpx_0 {
	padding: 40rpx 0;
}

.padding_20rpx_0 {
	padding: 20rpx 0;
}

.padding_10rpx_0 {
	padding: 10rpx 0;
}

.padding_6rpx_0 {
	padding: 6rpx 0;
}

.padding_0_10rpx {
	padding: 0 10rpx;
}

.padding_0_20rpx {
	padding: 0 20rpx;
}

.padding_0_30rpx {
	padding: 0 30rpx;
}

.padding_10rpx_20rpx {
	padding: 10rpx 20rpx;
}




/* line-height */
.line-height_50rpx {
	line-height: 50rpx;
}


/* flaot */
.float_left {
	float: left;
}

.float_right {
	float: right;
}

/* 轮播图图片 */
.swiper-img {
	width: 100%;
	height: 300rpx;
}

/* 右箭头图标 */
.rigth_ioc {
	width: 25rpx;
	height: 25rpx;
	position: relative;
	left: 475rpx;
	top: 3rpx;
}

/* 入住时间 */
.check_in {
	border-bottom: 2rpx solid #EEEEEE;
	margin: 35rpx 40rpx 0;
}

.check_in_date {
	margin: 2rpx 0 10rpx;
	line-height: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
}

/* 房间列表评分 */
.grade {
	font-size: 22rpx;
	color: #FFFFFF;
	background: #EBCD47 0% 0% no-repeat padding-box;
	border-radius: 8px;
	width: 68rpx;
	height: 32rpx;
	margin: auto;
	text-align: center;

}

.context {
	width: 340rpx;
	height: 404rpx;
	background-color: #FFFFFF;
	border-radius: 5px;

}

.context_img {
	width: 340rpx;
	height: 208rpx;
}

.context_title {
	height: 82rpx;
	line-height: 38rpx;
}

.context_price {
	color: #FF0000;
	font-size: 34rpx;
	margin-top: 13rpx;
	font-weight: bold;
}

/* 服务列表 recommendation */
.recommendation {
	width: 50%;
	/* height: 450rpx; */
	/* background-color: #FFFFFF; */
	border-radius: 5px;
	margin-top: 20rpx;

	/* margin: 22rpx 0 0 22rpx; */
	/* flex: 1; */

}

.recommendation_view {
	width: 330rpx;
	/* height: 330rpx; */
	margin: 0 auto;
	border-bottom: 2rpx solid #E5E5E5;
	box-shadow: #E5E5E5 0rpx 5rpx 10rpx;
	background-color: #FFFFFF;
	border-radius: 10rpx;
	padding-bottom: 10rpx;

}

.recommendation_img {
	width: 330rpx;
	height: 330rpx;
}

/* 优惠券 coupon */
.coupon {
	margin: 28rpx 20rpx 0;
	background-color: #FFFFFF;
	display: flex;
	position: relative;
}

.coupon_left {
	padding: 30rpx 30rpx 30rpx 50rpx;
}

.coupon_right {
	padding: 40rpx 25rpx;
}

.coupon_button {
	background-color: #EEEEEF;
	width: 90rpx;
	height: 30rpx;
	text-align: center;
	border-radius: 15rpx;
	position: relative;
	top: 6rpx;
	left: 16rpx;
}

.coupon_bottom {
	text-align: center;
	font-size: 24rpx;
	color: #999999;
	padding: 20rpx;
}

.noData {
	margin: 120rpx auto;
}

/* 计数按钮 */
.num_buttom {
	background-color: #FF147A;
	color: #FFFFFF;
	border-radius: 50%;
	width: 40rpx;
	height: 40rpx;
	line-height: 40rpx;
	text-align: center;
	font-size: 32rpx;
}

.input-placeholder-style {
	font-size: 28rpx;
	color: #A1A1A1;
}
