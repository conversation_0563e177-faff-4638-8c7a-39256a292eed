<template>
	<view>
		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-wrapper">
				<input class="search-input" v-model="searchKeyword" placeholder="搜索视频名称..."
					placeholder-style="color: #999;" @confirm="onSearch" confirm-type="search" />
				<view class="search-button" @click="onSearch">
					搜索
				</view>
			</view>
		</view>

		<!-- 顶部分类标签栏 -->
		<view class="tabbar-container">
			<scroll-view scroll-x="true" class="tabbar-scroll" :scroll-with-animation="true" :show-scrollbar="false">
				<view class="tabbar-wrapper">
					<view v-for="(item,index) in categoryList" :key="index" class="tabbar-item"
						:class="{'tabbar-item-active': currentCategoryId == item.id}" @click="getId(item)">
						<text class="tabbar-text">{{item.name}}</text>
						<view class="tabbar-line" :class="{'tabbar-line-active': currentCategoryId == item.id}"></view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 主内容区域 -->
		<mescroll-body ref="mescrollRef" :isShowEmptys="true" :height="windowHeight+'rpx'" @init="mescrollInit"
			@down="downCallback" @up="upCallback" :up="upOption" :down="downOption">

			<!-- 有数据时的内容展示 -->
			<block v-if="list.length > 0">
				<view class="display-fw-js" style="padding: 0 20rpx">
					<block v-for="(item,index) in list" :key="index">
						<view>
							<view class="r-frame-w" @click="getVideo(item)">
								<image class="introduce" :src="item.cover" mode=""></image>
							</view>
							<view class="frame-title margin-bottom_30rpx">
								<view class="display-a-js">
									<view class="font-size_26rpx font-overflow2 color_FFFFFF">
										{{item.name}}
									</view>
								</view>
							</view>
						</view>
					</block>
				</view>
			</block>

			<!-- 无数据时的提示 -->
			<block v-else>
				<view class="display-ac-jc">
					<image class="nodata" src="../../static/nodata.png"></image>
					<view class="nodata-tips">暂无视频数据~</view>
				</view>
			</block>
		</mescroll-body>

		<!-- 底部导航栏 -->
		<sunui-tabbar :fixed="true" :current="tabIndex" :types="1"></sunui-tabbar>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabIndex: 1, // tabbar当前选中项

				// 分类相关
				categoryList: [], // 分类列表
				currentCategoryId: '', // 当前选中的分类ID

				// 搜索相关
				searchKeyword: '', // 搜索关键词

				// 列表相关
				list: [], // 列表数据
				windowHeight: '', // 窗口高度

				// 用户相关
				userUid: uni.getStorageSync('uid'), // 用户ID

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: true, // 启用自动上拉加载
					page: {
						num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
						size: 5 // 每页数据的数量
					},
					noMoreSize: 3, // 如果列表已无数据,可设置列表的总数量要大于等于3条才显示无更多数据;避免列表数据过少(比如只有1条数据),显示无更多数据会不好看
					empty: {
						show: true, // 是否显示空布局
						icon: "../../static/nodata.png", // 空布局的图标
						tip: "暂无视频数据~" // 空布局的提示
					}
				},

				// 分页相关
				currentPage: 1, // 当前页码
				pageSize: 5, // 每页数量

				// 状态管理
				isLoading: false, // 是否正在加载
				hasError: false, // 是否有错误
			}
		},

		onLoad() {
			// 获取窗口高度，设置滚动区域高度
			uni.getSystemInfo({
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 420;
				},
			});

			// 初始化数据
			this.getFinishVideoType();
		},

		onHide() {
			// 页面隐藏时的处理
		},

		onUnload() {
			// 页面卸载时的处理
		},

		methods: {
			//播放视频
			getVideo(item) {
				//高级 快速版
				let param = {
					base_video: item.url,
					id: item.id,
					name: item.name
				};

				uni.navigateTo({
					url: '/pages/index/videos?type=2&param=' + encodeURIComponent(JSON.stringify(param))
				})
			},
			// mescroll初始化完成的回调
			mescrollInit(mescroll) {
				this.mescroll = mescroll;
			},

			// 下拉刷新回调
			downCallback() {
				// 重置页码为1，重新加载当前分类的数据
				this.currentPage = 1;
				this.mescroll.resetUpScroll();
			},

			// 上拉加载回调
			async upCallback(scroll) {
				if (!this.currentCategoryId) {
					this.mescroll.endErr();
					return;
				}

				try {
					this.isLoading = true;
					this.hasError = false;

					const result = await this.$http.post({
						url: this.$api.getFinishVideoList,
						data: {
							page: scroll.num,
							limit: this.pageSize,
							type_id: this.currentCategoryId,
							name: this.searchKeyword || '' // 添加搜索参数
						}
					});

					if (result.errno == 0) {
						// 计算总页数
						const totalPages = Math.ceil(result.data.total / this.pageSize);
						this.mescroll.endByPage(result.data.list.length, totalPages);

						if (scroll.num == 1) {
							this.list = [];
						}
						this.list = this.list.concat(result.data.list || []);

						// 如果是第一页且没有数据，显示空状态
						if (scroll.num == 1 && (!result.data.list || result.data.list.length === 0)) {
							// mescroll会自动显示空状态
						}
					} else {
						this.hasError = true;
						this.mescroll.endErr();
						this.$sun.toast(result.message || '加载失败', 'none');
					}
				} catch (error) {
					console.error('获取列表数据失败:', error);
					this.hasError = true;
					this.mescroll.endErr();
					this.$sun.toast('网络错误，请重试', 'none');
				} finally {
					this.isLoading = false;
				}
			},

			// 获取分类数据
			async getFinishVideoType() {
				try {
					const result = await this.$http.get({
						url: this.$api.getFinishVideoType
					});

					if (result.errno == 0) {
						this.categoryList = result.data || [];
						// 默认选中第一个分类
						if (this.categoryList.length > 0) {
							this.currentCategoryId = this.categoryList[0].id;
							// 初始化列表数据
							this.$nextTick(() => {
								if (this.mescroll) {
									this.mescroll.resetUpScroll();
								}
							});
						}
					} else {
						this.$sun.toast(result.message || '获取分类失败', 'none');
					}
				} catch (error) {
					console.error('获取分类数据失败:', error);
					this.$sun.toast('网络错误，请重试', 'none');
				}
			},

			// 分类切换
			getId(category) {
				this.currentCategoryId = category.id;
				this.list = [];
				this.$nextTick(() => {
					if (this.mescroll) {
						this.mescroll.resetUpScroll();
					}
				});
			},



			// 搜索功能
			onSearch() {
				// 重置列表
				this.list = [];

				// 重新加载数据
				this.$nextTick(() => {
					if (this.mescroll) {
						this.mescroll.resetUpScroll();
					}
				});
			}
		}
	}
</script>

<style>
	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #232323;
	}

	/* 搜索框样式 */
	.search-container {
		background-color: #1B1B1B;
		padding: 20rpx;
		border-bottom: 1px solid #333;
	}

	.search-wrapper {
		display: flex;
		align-items: center;
		border: 2rpx solid transparent;
		transition: all 0.3s ease;
	}

	.search-input {
		flex: 1;
		height: 70rpx;
		font-size: 28rpx;
		color: #FFFFFF;
		background-color: transparent;
		border: none;
		outline: none;
		background-color: #2A2A2A;
		border-radius: 10rpx;
		padding: 0 20rpx;
	}

	.search-button {
		width: 120rpx;
		height: 70rpx;
		background: linear-gradient(135deg, #E496FD 0%, #B366FF 100%);
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 15rpx;
		transition: all 0.3s ease;
		box-shadow: 0 4rpx 12rpx rgba(228, 150, 253, 0.3);
		color: #FFF;
	}

	.search-button:active {
		transform: scale(0.95);
		box-shadow: 0 2rpx 8rpx rgba(228, 150, 253, 0.5);
	}

	.search-icon {
		font-size: 24rpx;
		color: #FFFFFF;
	}

	/* 分类标签栏样式 */
	.tabbar-container {
		background-color: #1B1B1B;
		padding: 20rpx 0;
		border-bottom: 1px solid #333;
	}

	.tabbar-scroll {
		width: 100%;
		white-space: nowrap;
	}

	.tabbar-wrapper {
		display: flex;
		padding: 0 20rpx;
	}

	.tabbar-item {
		position: relative;
		margin-right: 40rpx;
		padding: 10rpx 20rpx;
		flex-shrink: 0;
	}

	.tabbar-item-active {
		/* background-color: rgba(228, 150, 253, 0.1); */
		border-radius: 20rpx;
	}

	.tabbar-text {
		font-size: 28rpx;
		color: #999;
		transition: color 0.3s;
	}

	.tabbar-item-active .tabbar-text {
		color: #E496FD;
		/* font-weight: bold; */
	}

	.tabbar-line {
		position: absolute;
		bottom: -2rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 0;
		height: 6rpx;
		background: linear-gradient(90deg, #E496FD 0%, #B366FF 50%, #E496FD 100%);
		border-radius: 3rpx;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 2rpx 8rpx rgba(228, 150, 253, 0.3);
	}

	.tabbar-line-active {
		width: calc(100% - 20rpx);
		box-shadow: 0 2rpx 12rpx rgba(228, 150, 253, 0.5);
	}

	/* 视频列表样式 - 参考course页面 */
	.introduce {
		width: 100%;
		height: 100%;
		border-radius: 10rpx 10rpx 0 0;
	}

	.frame-title {
		background-color: #414141;
		padding: 20rpx 10rpx;
		width: 344rpx;
		border-radius: 0 0 10rpx 10rpx;
	}

	.r-frame-w {
		width: 344rpx;
		border-radius: 10rpx 10rpx 0 0;
		position: relative;
		height: 612rpx;
		overflow: hidden;
		display: block;
	}

	/* 辅助样式类 */
	.display-fw-js {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.display-a-js {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.font-overflow2 {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		max-width: 300rpx;
	}

	.margin-bottom_30rpx {
		margin-bottom: 30rpx;
	}

	/* 无数据样式 */
	.display-ac-jc {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		height: 400rpx;
	}

	.nodata {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 20rpx;
	}

	.nodata-tips {
		color: #999;
		font-size: 28rpx;
		margin-bottom: 20rpx;
	}

	.nodata-but {
		background-color: #E496FD;
		color: #fff;
		padding: 15rpx 30rpx;
		border-radius: 25rpx;
		font-size: 26rpx;
	}
</style>