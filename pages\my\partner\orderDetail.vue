<template>
	<view>
		
		<view style="height: 30rpx;"></view>
		
		<view class="o-search display-a-jc">
			<view style="width: 80rpx;"></view>
			<icon type="search" size="16"></icon>
			<input type="text" class="o-input" @input="inputChange2" @confirm="confirmChange" v-model="log_no" placeholder="请输入用户名称" placeholder-class="placeholder"/>
		</view>
		
		<view class="display-a margin-bottom_30rpx">
			<block v-for="(item,index) in arr" :key="index">
				<view @click="getTab(item)" class="o-arr" :class="arrId == item.id ? 'o-arr-2' : 'o-arr-1'">{{item.name}}</view>
			</block>
		</view>
		
		<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
			@up="upCallback" :up="upOption" :down="downOption">
			<block v-for="(item,index) in list" :key="index">
				<view class="list-public display-a">
					<block v-if="item.is_status == 1">
						<image class="img-55" :src="imgUrl + '55.png'"></image>
						<view class="color_C3C2C2">
							<view class="font-size_30rpx margin-bottom_10rpx">未知用户</view>
							<view class="margin-bottom_10rpx">卡密: {{item.card}}</view>
							<view class="font-size_26rpx">当前状态: 未激活</view>
						</view>
						<view class="o-copy" @click="getCopy(item.card)">复制卡密</view>
					</block>
					<block v-if="item.is_status == 2">
						<image class="img-55" :src="item.avatar"></image>
						<view class="color_FFFFFF">
							<view class="font-size_32rpx font-weight_bold margin-bottom_10rpx">{{item.nickname}}</view>
							<view class="margin-bottom_10rpx" @click="getCopy(item.card)">卡密: {{item.card}}</view>
							<view class="display-a">
								<view class="font-size_26rpx"><span class="color_A7A7A7" style="margin-right: 8rpx;">当前状态:</span> 已激活</view>
								<view class="color_A7A7A7 font-size_24rpx margin-left_38rpx">{{item.use_time}}</view>
							</view>
						</view>
					</block>
				</view>
			</block>
		</mescroll-body>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				arr: [
					{id:'',name:'全部'},
					{id:2,name:'已激活'},
					{id:1,name:'未激活'},
				],
				arrId: '',
				
				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},
				
				list: [],
				
				windowHeight: '',
				
				log_no: '', //用户名称
				
				logId: '', //记录ID
				
			}
		},
		
		onLoad(options) {
			uni.getSystemInfo({ //获取系统信息
				success: res => {
					this.windowHeight = res.windowHeight * 2 - 380;
				},
			})
			if (options.logName) {
				this.$sun.title(options.logName);
			}
			if (options.logId) {
				this.logId = options.logId;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			}
		},
		
		onShow() {
			
		},
		
		methods: {
			
			// 输入事件
			inputChange2(e) {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			
			},
			confirmChange(e) {
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			//复制
			getCopy(text) {
				if (text) {
					uni.setClipboardData({
						data: text,
						success: () => {
							// 复制成功的回调
							this.$sun.toast('复制成功');
							// uni.showToast({
							// 	title: '复制成功',
							// 	icon: 'success',
							// 	duration: 4000
							// });
						},
						fail: (err) => {
							console.log("复制失败原因===>",err);
							// 复制失败的回调
							uni.showToast({
								title: '复制失败：'+err,
								icon: 'none'
							});
						}
					});
				}
			},
			
			getTab(obj) {
				this.arrId = obj.id;
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			},
			
			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.partnerCardList,
					data: {
						partner_id: uni.getStorageSync("partnerId"),
						log_id: this.logId,
						nickname: this.log_no,
						is_status: this.arrId,
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},
			
		}
	}
</script>

<style lang="scss">
	
	.color_A7A7A7 {
		color: #A7A7A7;
	}
	
	.color_C3C2C2 {
		color: #C3C2C2;
	}
	
	.img-55 {
		width: 94rpx;
		height: 94rpx;
		margin-right: 20rpx;
		border-radius: 100rpx;
	}
	
	.o-copy {
		width: 150rpx;
		text-align: center;
		color: #FFF;
		padding: 12rpx 0;
		font-size: 26rpx;
		margin-left: auto;
		background: linear-gradient(90.00deg, rgb(0, 185, 255) 0%,rgb(0, 132, 255) 100%);
		border-radius: 100rpx;
	}
	
	.list-public {
		background-color: #333333;
		padding: 30rpx;
	}
	
	.o-arr-2 {
		background-color: #008EFF;
		color: #FFF;
	}
	
	.o-arr-1 {
		background-color: #454545;
		color: #C4BDBD;
	}
	
	.o-arr {
		width: 120rpx;
		text-align: center;
		padding: 6rpx 0;
		border-radius: 10rpx;
		margin-left: 26rpx;
	}
	
	.o-input {
		width: 300rpx;
		margin-left: 10rpx;
	}
	
	.o-search {
		width: 710rpx;
		background-color: #FFF;
		border-radius: 100rpx;
		margin: 0 20rpx 30rpx;
		padding: 20rpx 0;
	}
	
	page {
		background-color: #1D1D1D;
		border-top: 1px solid rgb(56, 56, 56);;
	}
	
</style>

